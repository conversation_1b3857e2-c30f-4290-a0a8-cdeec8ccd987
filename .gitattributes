# Git 属性配置文件
# 统一管理整个项目的文件处理规则

# ===========================================
# 默认规则：自动检测文本文件并统一换行符
# ===========================================
* text=auto eol=lf

# ===========================================
# 明确指定文本文件类型
# ===========================================
# Python 文件
*.py text eol=lf
*.pyi text eol=lf
*.pyx text eol=lf

# Web 前端文件
*.js text eol=lf
*.ts text eol=lf
*.vue text eol=lf
*.jsx text eol=lf
*.tsx text eol=lf
*.css text eol=lf
*.scss text eol=lf
*.sass text eol=lf
*.less text eol=lf
*.html text eol=lf
*.htm text eol=lf

# 配置文件
*.json text eol=lf
*.yaml text eol=lf
*.yml text eol=lf
*.toml text eol=lf
*.ini text eol=lf
*.cfg text eol=lf
*.conf text eol=lf
*.xml text eol=lf

# 文档文件
*.md text eol=lf
*.txt text eol=lf
*.rst text eol=lf

# Shell 脚本
*.sh text eol=lf
*.bash text eol=lf
*.zsh text eol=lf

# Docker 文件
Dockerfile text eol=lf
*.dockerfile text eol=lf
docker-compose*.yml text eol=lf

# Git 配置
.gitignore text eol=lf
.gitattributes text eol=lf

# ===========================================
# 明确指定二进制文件（防止误处理）
# ===========================================
# 图片文件
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.svg binary
*.webp binary
*.bmp binary
*.tiff binary

# 字体文件
*.woff binary
*.woff2 binary
*.ttf binary
*.otf binary
*.eot binary

# 压缩文件
*.zip binary
*.tar binary
*.gz binary
*.bz2 binary
*.7z binary
*.rar binary

# 可执行文件
*.exe binary
*.dll binary
*.so binary
*.dylib binary

# 数据库文件
*.db binary
*.sqlite binary
*.sqlite3 binary

# 其他二进制文件
*.pdf binary
*.doc binary
*.docx binary
*.xls binary
*.xlsx binary
*.ppt binary
*.pptx binary

# ===========================================
# 特殊处理的文件
# ===========================================
# Windows 批处理文件需要 CRLF
*.bat text eol=crlf
*.cmd text eol=crlf

# 某些配置文件可能需要特殊处理
# （根据具体需求调整）

# ===========================================
# 语言特定的差异处理
# ===========================================
# Python 文件的特殊属性
*.py diff=python

# JavaScript/TypeScript 文件的特殊属性
*.js diff=javascript
*.ts diff=javascript
*.vue diff=javascript
