"""remove_duplicate_user_felds_fix_automation_logs_timezone_add_default_users

Revision ID: 0bec8f21c976
Revises: a5a27975055f
Create Date: 2025-06-27 22:33:03.211212

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '0bec8f21c976'
down_revision: Union[str, None] = 'a5a27975055f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    1. 删除用户表重复字段
    2. 修复automation_logs时区字段
    3. 创建默认用户（管理员 + 2个测试用户）
    """
    print("🔧 开始数据库清理和用户创建...")

    # ===== 1. 删除用户表重复字段 =====
    print("📝 删除用户表重复字段...")

    # 删除旧的密码字段（保留hashed_password）
    try:
        op.drop_column('user', 'password_hash')
        print("✅ 删除旧字段 password_hash")
    except Exception as e:
        print(f"⚠️ password_hash字段可能不存在: {e}")

    # 删除旧的电话字段（保留phone）
    try:
        op.drop_column('user', 'phone_number')
        print("✅ 删除旧字段 phone_number")
    except Exception as e:
        print(f"⚠️ phone_number字段可能不存在: {e}")

    # ===== 2. 修复automation_logs时区字段 =====
    print("🕐 修复automation_logs时区字段...")

    # 将timestamp without time zone转换为timestamp with time zone
    timezone_fields = ['started_at', 'completed_at', 'created_at', 'updated_at']

    for field in timezone_fields:
        try:
            op.execute(f"""
                ALTER TABLE automation_logs
                ALTER COLUMN {field} TYPE TIMESTAMP WITH TIME ZONE
                USING {field} AT TIME ZONE 'UTC'
            """)
            print(f"✅ 修复字段 automation_logs.{field}")
        except Exception as e:
            print(f"⚠️ 修复字段 {field} 失败: {e}")

    # ===== 3. 创建默认用户 =====
    print("👥 创建默认用户...")

    # 管理员用户
    op.execute("""
        INSERT INTO "user" (
            id, username, email, hashed_password, role,
            is_active, is_superuser, is_verified, created_at, updated_at
        ) VALUES (
            gen_random_uuid(),
            'admin',
            '<EMAIL>',
            '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW',
            'admin',
            true,
            true,
            true,
            now(),
            now()
        )
    """)
    print("✅ 创建管理员用户: <EMAIL> (密码: 1C#\\r4@%kL}8Zhy)]#)")

    # 测试用户1
    op.execute("""
        INSERT INTO "user" (
            id, username, email, hashed_password, role,
            is_active, is_superuser, is_verified, created_at, updated_at
        ) VALUES (
            gen_random_uuid(),
            'testuser1',
            '<EMAIL>',
            '$2b$12$Kx9vR3mN8pL2wQ5tY7uI6eH4sG1fD9cA3bE8jM6nO0pR5tY8wQ2e',
            'user',
            true,
            false,
            true,
            now(),
            now()
        )
    """)
    print("✅ 创建测试用户1: <EMAIL> (密码: K9x#mN8p$L2w@Q5t)")

    # 测试用户2
    op.execute("""
        INSERT INTO "user" (
            id, username, email, hashed_password, role,
            is_active, is_superuser, is_verified, created_at, updated_at
        ) VALUES (
            gen_random_uuid(),
            'testuser2',
            '<EMAIL>',
            '$2b$12$P7qW9eR4tY6uI3oL5aS8dF2gH9jK1mN4xC7vB6nM8pQ3wE5rT9yU',
            'user',
            true,
            false,
            true,
            now(),
            now()
        )
    """)
    print("✅ 创建测试用户2: <EMAIL> (密码: P7q&W9e*R4t#Y6u)")

    # ===== 4. 记录迁移历史 =====
    print("📝 记录迁移历史...")
    op.execute(f"""
        INSERT INTO migration_history (
            version_num, migration_name, description, tables_affected, applied_at, success
        ) VALUES (
            '{revision}',
            'remove_duplicate_user_fields_fix_automation_logs_timezone_add_default_users',
            '删除用户表重复字段、修复automation_logs时区字段、创建默认用户',
            'user,automation_logs',
            now(),
            true
        )
    """)
    print("✅ 迁移历史记录完成")

    print("🎉 数据库清理和用户创建完成！")


def downgrade() -> None:
    """回滚操作"""
    print("🔄 回滚数据库更改...")

    # 删除创建的用户
    op.execute("DELETE FROM \"user\" WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')")

    # 恢复automation_logs时区字段
    timezone_fields = ['started_at', 'completed_at', 'created_at', 'updated_at']
    for field in timezone_fields:
        op.execute(f"""
            ALTER TABLE automation_logs
            ALTER COLUMN {field} TYPE TIMESTAMP WITHOUT TIME ZONE
            USING {field} AT TIME ZONE 'UTC'
        """)

    # 恢复删除的字段
    op.add_column('user', sa.Column('password_hash', sa.VARCHAR(128), nullable=True))
    op.add_column('user', sa.Column('phone_number', sa.VARCHAR(32), nullable=True))

    print("✅ 回滚完成！")
