"""fix_correct_argon2_password_hashes

Revision ID: 44d7547e971d
Revises: fc0eefdc4553
Create Date: 2025-06-28 09:39:31.644103

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '44d7547e971d'
down_revision: Union[str, None] = 'fc0eefdc4553'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """修复密码哈希 - 使用正确的Argon2哈希值"""
    print("🔧 修复密码哈希为正确的Argon2值...")

    # 生成正确的密码哈希
    import argon2
    ph = argon2.PasswordHasher()

    # 管理员用户密码: 1C#\r4@%kL}8Zhy)]#
    admin_hash = ph.hash('1C#\\r4@%kL}8Zhy)]#')
    op.execute(f"""
        UPDATE "user"
        SET hashed_password = '{admin_hash}'
        WHERE email = '<EMAIL>'
    """)
    print("✅ 更新管理员用户密码哈希")

    # 测试用户1密码: K9x#mN8p$L2w@Q5t
    test1_hash = ph.hash('K9x#mN8p$L2w@Q5t')
    op.execute(f"""
        UPDATE "user"
        SET hashed_password = '{test1_hash}'
        WHERE email = '<EMAIL>'
    """)
    print("✅ 更新测试用户1密码哈希")

    # 测试用户2密码: P7q&W9e*R4t#Y6u
    test2_hash = ph.hash('P7q&W9e*R4t#Y6u')
    op.execute(f"""
        UPDATE "user"
        SET hashed_password = '{test2_hash}'
        WHERE email = '<EMAIL>'
    """)
    print("✅ 更新测试用户2密码哈希")

    # 记录迁移历史
    print("📝 记录迁移历史...")
    op.execute(f"""
        INSERT INTO migration_history (
            version_num, migration_name, description, tables_affected, applied_at, success
        ) VALUES (
            '{revision}',
            'fix_correct_argon2_password_hashes',
            '修复密码哈希为正确的Argon2值，解决登录失败问题',
            'user',
            now(),
            true
        )
    """)
    print("✅ 迁移历史记录完成")

    print("🎉 密码哈希修复完成！现在可以使用正确的密码登录了")


def downgrade() -> None:
    """回滚到之前的错误哈希值"""
    print("🔄 回滚到之前的密码哈希...")

    # 恢复之前的错误哈希值
    op.execute("""
        UPDATE "user"
        SET hashed_password = '$argon2id$v=19$m=65536,t=3,p=4$YmvrZ+1ykUcamkZGQWBBoA$g1hKBUmiOG0lSwbvcjV5EvnDUDzD6Eugp2cCzeI44ks'
        WHERE email = '<EMAIL>'
    """)

    op.execute("""
        UPDATE "user"
        SET hashed_password = '$argon2id$v=19$m=65536,t=3,p=4$ZgCSPA1/LAoNyNUk1+jACw$RwmK6spP3zkaNsWma4MWcft/Z7XQ3FO10qYNBlRFhuQ'
        WHERE email = '<EMAIL>'
    """)

    op.execute("""
        UPDATE "user"
        SET hashed_password = '$argon2id$v=19$m=65536,t=3,p=4$pPVB9oAM5rnFo1mT2B831Q$e0lWVtfniSuPNHndNSneurhnFGquecWD1J1MA6sllkE'
        WHERE email = '<EMAIL>'
    """)

    # 删除迁移历史记录
    op.execute(f"DELETE FROM migration_history WHERE version_num = '{revision}'")

    print("✅ 密码哈希回滚完成！")
