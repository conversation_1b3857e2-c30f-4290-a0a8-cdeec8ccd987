"""remove_changed_at_from_visa_status_history

Revision ID: 4a676de393d7
Revises: ffc2e87fe465
Create Date: 2025-06-22 04:42:55.591746

删除visa_status_history表的changed_at字段，因为已经有updated_at字段
保持与其他表的命名一致性，避免字段重复
"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "4a676de393d7"
down_revision: str | None = "ffc2e87fe465"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """删除changed_at字段，保持updated_at字段"""
    print("=" * 60)
    print("🗑️ 删除visa_status_history.changed_at字段...")
    print("=" * 60)

    try:
        # 删除相关索引
        op.execute("DROP INDEX IF EXISTS ix_visa_status_changed_at")
        print("✅ 删除索引: ix_visa_status_changed_at")

        # 删除changed_at字段
        op.drop_column("visa_status_history", "changed_at")
        print("✅ 删除字段: changed_at")

        # 确保updated_at字段有正确的索引（如果不存在）
        op.execute("""
            CREATE INDEX IF NOT EXISTS ix_visa_status_history_updated_at
            ON visa_status_history (updated_at)
        """)
        print("✅ 创建索引: ix_visa_status_history_updated_at")

    except Exception as e:
        print(f"❌ 删除字段失败: {e}")
        raise

    print("=" * 60)
    print("✅ 字段删除完成！")
    print("📋 现在visa_status_history表只使用updated_at字段，与其他表保持一致")
    print("=" * 60)


def downgrade() -> None:
    """回滚：重新添加changed_at字段"""
    print("🔄 回滚：重新添加changed_at字段...")

    try:
        # 删除新索引
        op.drop_index("ix_visa_status_history_updated_at", "visa_status_history")

        # 重新添加changed_at字段
        op.add_column(
            "visa_status_history",
            sa.Column(
                "changed_at", sa.DateTime, nullable=False, server_default=sa.func.now()
            ),
        )

        # 恢复旧索引
        op.create_index(
            "ix_visa_status_changed_at",
            "visa_status_history",
            ["visa_status", "changed_at"],
        )

        print("✅ 回滚完成")

    except Exception as e:
        print(f"❌ 回滚失败: {e}")
        raise
