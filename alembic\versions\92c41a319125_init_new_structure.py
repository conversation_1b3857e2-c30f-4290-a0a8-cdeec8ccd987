"""init new structure

Revision ID: 92c41a319125
Revises:
Create Date: 2025-06-14 11:59:40.222718

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "92c41a319125"
down_revision: str | None = None
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "user",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("username", sa.String(length=64), nullable=False),
        sa.Column("email", sa.String(length=128), nullable=False),
        sa.Column("password_hash", sa.String(length=128), nullable=False),
        sa.Column("company_name", sa.String(length=128), nullable=True),
        sa.Column("company_address", sa.String(length=256), nullable=True),
        sa.Column("company_contacts", sa.String(length=64), nullable=True),
        sa.Column("phone_number", sa.String(length=32), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=True),
        sa.Column(
            "created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.Column(
            "updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("email"),
        sa.UniqueConstraint("username"),
    )
    op.create_table(
        "visa_type",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("country", sa.String(length=64), nullable=False),
        sa.Column("visa_code", sa.String(length=32), nullable=False),
        sa.Column("name", sa.String(length=128), nullable=False),
        sa.Column("description", sa.String(length=256), nullable=True),
        sa.Column("validity_days", sa.Integer(), nullable=True),
        sa.Column("entry_type", sa.String(length=32), nullable=True),
        sa.Column("price", sa.Float(), nullable=True),
        sa.Column("enabled", sa.Boolean(), nullable=True),
        sa.Column(
            "created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.Column(
            "updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("country", "visa_code", name="uq_country_visa_code"),
    )
    op.create_table(
        "applicant",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("user_id", sa.UUID(), nullable=False),
        sa.Column("surname", sa.String(length=64), nullable=False),
        sa.Column("given_name", sa.String(length=64), nullable=False),
        sa.Column("chinese_name", sa.String(length=64), nullable=True),
        sa.Column("sex", sa.String(length=8), nullable=True),
        sa.Column("nationality", sa.String(length=32), nullable=True),
        sa.Column("date_of_birth", sa.Date(), nullable=True),
        sa.Column("place_of_birth", sa.String(length=128), nullable=True),
        sa.Column("telephone_number", sa.String(length=32), nullable=True),
        sa.Column("email", sa.String(length=128), nullable=True),
        sa.Column("permanent_address", sa.String(length=256), nullable=True),
        sa.Column("contact_address", sa.String(length=256), nullable=True),
        sa.Column("passport_number", sa.String(length=32), nullable=False),
        sa.Column("date_of_issue", sa.Date(), nullable=True),
        sa.Column("date_of_expiry", sa.Date(), nullable=True),
        sa.Column("place_of_issue", sa.String(length=128), nullable=True),
        sa.Column("work_unit", sa.String(length=128), nullable=True),
        sa.Column("work_address", sa.String(length=256), nullable=True),
        sa.Column("emergency_contact_name", sa.String(length=64), nullable=True),
        sa.Column("emergency_contact_phone", sa.String(length=32), nullable=True),
        sa.Column("emergency_address", sa.String(length=256), nullable=True),
        sa.Column("visa_entry_type", sa.String(length=32), nullable=True),
        sa.Column("visa_validity_duration", sa.String(length=32), nullable=True),
        sa.Column("visa_start_date", sa.Date(), nullable=True),
        sa.Column("intended_entry_gate", sa.String(length=128), nullable=True),
        sa.Column("purpose_of_entry", sa.String(length=128), nullable=True),
        sa.Column("visited_vietnam_last_year", sa.Boolean(), nullable=True),
        sa.Column("previous_entry_date", sa.Date(), nullable=True),
        sa.Column("previous_exit_date", sa.Date(), nullable=True),
        sa.Column("previous_purpose", sa.String(length=128), nullable=True),
        sa.Column("has_vietnam_contact", sa.Boolean(), nullable=True),
        sa.Column("vietnam_contact_organization", sa.String(length=128), nullable=True),
        sa.Column("vietnam_contact_phone", sa.String(length=32), nullable=True),
        sa.Column("vietnam_contact_address", sa.String(length=256), nullable=True),
        sa.Column("vietnam_contact_purpose", sa.String(length=128), nullable=True),
        sa.Column(
            "created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.Column(
            "updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.ForeignKeyConstraint(["user_id"], ["user.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("user_id", "passport_number", name="uq_user_passport"),
    )
    op.create_index(
        op.f("ix_applicant_user_id"), "applicant", ["user_id"], unique=False
    )
    op.create_table(
        "order",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("user_id", sa.UUID(), nullable=False),
        sa.Column("order_no", sa.String(length=32), nullable=False),
        sa.Column("total_amount", sa.Float(), nullable=True),
        sa.Column("status", sa.String(length=32), nullable=False),
        sa.Column(
            "created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.Column(
            "updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.ForeignKeyConstraint(["user_id"], ["user.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("order_no"),
    )
    op.create_index(op.f("ix_order_user_id"), "order", ["user_id"], unique=False)
    op.create_table(
        "visa_field",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("visa_type_id", sa.UUID(), nullable=False),
        sa.Column("field_name", sa.String(length=64), nullable=False),
        sa.Column("field_type", sa.String(length=32), nullable=False),
        sa.Column("required", sa.Boolean(), nullable=True),
        sa.Column("label", sa.String(length=128), nullable=True),
        sa.Column("description", sa.String(length=256), nullable=True),
        sa.Column("order", sa.Integer(), nullable=True),
        sa.Column(
            "created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.Column(
            "updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.ForeignKeyConstraint(["visa_type_id"], ["visa_type.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("visa_type_id", "field_name", name="uq_visa_field"),
    )
    op.create_index(
        op.f("ix_visa_field_visa_type_id"), "visa_field", ["visa_type_id"], unique=False
    )
    op.create_table(
        "visa_status",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("visa_type_id", sa.UUID(), nullable=False),
        sa.Column("status_code", sa.String(length=32), nullable=False),
        sa.Column("status_name", sa.String(length=64), nullable=False),
        sa.Column("description", sa.String(length=256), nullable=True),
        sa.Column("order", sa.Integer(), nullable=True),
        sa.Column("is_terminal", sa.Boolean(), nullable=True),
        sa.Column(
            "created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.Column(
            "updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.ForeignKeyConstraint(["visa_type_id"], ["visa_type.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("visa_type_id", "status_code", name="uq_visa_status"),
    )
    op.create_index(
        op.f("ix_visa_status_visa_type_id"),
        "visa_status",
        ["visa_type_id"],
        unique=False,
    )
    op.create_table(
        "application",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("order_id", sa.UUID(), nullable=False),
        sa.Column("applicant_id", sa.UUID(), nullable=False),
        sa.Column("visa_type_id", sa.UUID(), nullable=False),
        sa.Column("status", sa.String(length=32), nullable=False),
        sa.Column("form_snapshot", sa.JSON(), nullable=False),
        sa.Column("vietnam_application_number", sa.String(length=64), nullable=True),
        sa.Column(
            "created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.Column(
            "updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.ForeignKeyConstraint(["applicant_id"], ["applicant.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["order_id"], ["order.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["visa_type_id"], ["visa_type.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_application_applicant_id"),
        "application",
        ["applicant_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_application_order_id"), "application", ["order_id"], unique=False
    )
    op.create_index(
        op.f("ix_application_status"), "application", ["status"], unique=False
    )
    op.create_index(
        op.f("ix_application_vietnam_application_number"),
        "application",
        ["vietnam_application_number"],
        unique=False,
    )
    op.create_index(
        op.f("ix_application_visa_type_id"),
        "application",
        ["visa_type_id"],
        unique=False,
    )
    op.create_table(
        "user_payment",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("order_id", sa.UUID(), nullable=False),
        sa.Column("user_id", sa.UUID(), nullable=False),
        sa.Column("amount", sa.Float(), nullable=False),
        sa.Column("currency", sa.String(length=16), nullable=True),
        sa.Column("status", sa.String(length=32), nullable=False),
        sa.Column("payment_channel", sa.String(length=32), nullable=True),
        sa.Column("paid_at", sa.DateTime(), nullable=True),
        sa.Column("transaction_id", sa.String(length=64), nullable=True),
        sa.Column(
            "created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.Column(
            "updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.ForeignKeyConstraint(["order_id"], ["order.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["user_id"], ["user.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_user_payment_order_id"), "user_payment", ["order_id"], unique=False
    )
    op.create_index(
        op.f("ix_user_payment_user_id"), "user_payment", ["user_id"], unique=False
    )
    op.create_table(
        "file",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("application_id", sa.UUID(), nullable=False),
        sa.Column("file_type", sa.String(length=32), nullable=False),
        sa.Column("file_url", sa.String(length=256), nullable=False),
        sa.Column("file_name", sa.String(length=128), nullable=True),
        sa.Column(
            "uploaded_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.Column(
            "created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.Column(
            "updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.ForeignKeyConstraint(
            ["application_id"], ["application.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_file_application_id"), "file", ["application_id"], unique=False
    )
    op.create_table(
        "visa_payment",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("application_id", sa.UUID(), nullable=False),
        sa.Column("amount", sa.Float(), nullable=False),
        sa.Column("currency", sa.String(length=16), nullable=True),
        sa.Column("status", sa.String(length=32), nullable=False),
        sa.Column("payment_channel", sa.String(length=32), nullable=True),
        sa.Column("paid_at", sa.DateTime(), nullable=True),
        sa.Column("vietnam_order_no", sa.String(length=64), nullable=True),
        sa.Column("receipt_url", sa.String(length=256), nullable=True),
        sa.Column("card_number", sa.String(length=32), nullable=True),
        sa.Column(
            "created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.Column(
            "updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.ForeignKeyConstraint(
            ["application_id"], ["application.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_visa_payment_application_id"),
        "visa_payment",
        ["application_id"],
        unique=False,
    )
    op.create_table(
        "visa_status_history",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("application_id", sa.UUID(), nullable=False),
        sa.Column("from_status", sa.String(length=32), nullable=False),
        sa.Column("to_status", sa.String(length=32), nullable=False),
        sa.Column(
            "changed_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.Column("operator", sa.String(length=64), nullable=True),
        sa.Column("remark", sa.String(length=256), nullable=True),
        sa.Column(
            "created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.Column(
            "updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.ForeignKeyConstraint(
            ["application_id"], ["application.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_visa_status_history_application_id"),
        "visa_status_history",
        ["application_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_visa_status_history_application_id"), table_name="visa_status_history"
    )
    op.drop_table("visa_status_history")
    op.drop_index(op.f("ix_visa_payment_application_id"), table_name="visa_payment")
    op.drop_table("visa_payment")
    op.drop_index(op.f("ix_file_application_id"), table_name="file")
    op.drop_table("file")
    op.drop_index(op.f("ix_user_payment_user_id"), table_name="user_payment")
    op.drop_index(op.f("ix_user_payment_order_id"), table_name="user_payment")
    op.drop_table("user_payment")
    op.drop_index(op.f("ix_application_visa_type_id"), table_name="application")
    op.drop_index(
        op.f("ix_application_vietnam_application_number"), table_name="application"
    )
    op.drop_index(op.f("ix_application_status"), table_name="application")
    op.drop_index(op.f("ix_application_order_id"), table_name="application")
    op.drop_index(op.f("ix_application_applicant_id"), table_name="application")
    op.drop_table("application")
    op.drop_index(op.f("ix_visa_status_visa_type_id"), table_name="visa_status")
    op.drop_table("visa_status")
    op.drop_index(op.f("ix_visa_field_visa_type_id"), table_name="visa_field")
    op.drop_table("visa_field")
    op.drop_index(op.f("ix_order_user_id"), table_name="order")
    op.drop_table("order")
    op.drop_index(op.f("ix_applicant_user_id"), table_name="applicant")
    op.drop_table("applicant")
    op.drop_table("visa_type")
    op.drop_table("user")
    # ### end Alembic commands ###
