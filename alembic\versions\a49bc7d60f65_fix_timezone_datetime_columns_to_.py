"""fix_timezone_datetime_columns_to_timezone_aware

修复时区感知的DateTime列

此迁移记录了从TIMESTAMP WITHOUT TIME ZONE到TIMESTAMP WITH TIME ZONE的转换。


主要更改：
1. 所有DateTime列改为TIMESTAMP WITH TIME ZONE
2. SQLAlchemy模型已配置为DateTime(timezone=True)
3. 统一使用func.timezone('Asia/Shanghai', func.now())作为默认值



Revision ID: a49bc7d60f65
Revises: add_order_id_visa_status
Create Date: 2025-06-27 00:37:16.494252

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a49bc7d60f65'
down_revision: Union[str, None] = 'add_order_id_visa_status'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    应用时区感知DateTime列的修复

    将所有TIMESTAMP WITHOUT TIME ZONE字段改为TIMESTAMP WITH TIME ZONE
    并设置默认时区为Asia/Shanghai
    """
    # 获取所有需要修复的表和字段
    tables_and_columns = [
        ('applicant', ['created_at', 'updated_at']),
        ('application', ['created_at', 'updated_at']),
        ('file', ['created_at', 'updated_at', 'uploaded_at']),
        ('"order"', ['created_at', 'updated_at']),  # order是保留字，需要双引号
        ('"user"', ['created_at', 'updated_at']),   # user是保留字，需要双引号
        ('user_payment', ['created_at', 'updated_at', 'paid_at']),
        ('visa_payment', ['created_at', 'updated_at', 'paid_at']),
        ('visa_status_history', ['created_at', 'updated_at']),  # changed_at字段已被删除
        # 注意：visa_field、visa_status、visa_type 表在之前的迁移中已被删除
    ]

    # 修改每个表的时间字段类型
    for table_name, columns in tables_and_columns:
        for column_name in columns:
            print(f"  🔧 修复 {table_name}.{column_name} 时区...")
            op.execute(f"""
                ALTER TABLE {table_name}
                ALTER COLUMN {column_name}
                TYPE TIMESTAMP WITH TIME ZONE
                USING {column_name} AT TIME ZONE 'UTC'
            """)

    print("  ✅ 所有时间字段已修复为时区感知类型")


def downgrade() -> None:
    """
    回滚时区感知DateTime列的修复
    """
    # 获取所有需要回滚的表和字段
    tables_and_columns = [
        ('applicant', ['created_at', 'updated_at']),
        ('application', ['created_at', 'updated_at']),
        ('file', ['created_at', 'updated_at', 'uploaded_at']),
        ('"order"', ['created_at', 'updated_at']),
        ('"user"', ['created_at', 'updated_at']),
        ('user_payment', ['created_at', 'updated_at', 'paid_at']),
        ('visa_payment', ['created_at', 'updated_at', 'paid_at']),
        ('visa_status_history', ['created_at', 'updated_at']),
    ]

    # 回滚每个表的时间字段类型
    for table_name, columns in tables_and_columns:
        for column_name in columns:
            op.execute(f"""
                ALTER TABLE {table_name}
                ALTER COLUMN {column_name}
                TYPE TIMESTAMP WITHOUT TIME ZONE
            """)
