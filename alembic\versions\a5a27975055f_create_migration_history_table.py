"""create_migration_history_table

Revision ID: a5a27975055f
Revises: a49bc7d60f65
Create Date: 2025-06-27 13:58:53.143738

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'a5a27975055f'
down_revision: Union[str, None] = 'a49bc7d60f65'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """创建迁移历史表"""
    print("🔧 创建迁移历史表...")

    # 创建迁移历史表
    op.create_table(
        'migration_history',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True, comment='历史记录ID'),
        sa.Column('version_num', sa.String(32), nullable=False, comment='迁移版本号'),
        sa.<PERSON>umn('migration_name', sa.String(255), nullable=False, comment='迁移名称'),
        sa.Column('description', sa.Text(), nullable=True, comment='迁移描述'),
        sa.Column('tables_affected', sa.Text(), nullable=True, comment='受影响的表（逗号分隔）'),
        sa.Column('applied_at', sa.TIMESTAMP(timezone=True), nullable=False, server_default=sa.text('now()'), comment='应用时间'),
        sa.Column('success', sa.Boolean(), nullable=False, default=True, comment='是否成功'),
        sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息（如果失败）'),
    )

    # 创建基本索引
    op.create_index('ix_migration_history_version_num', 'migration_history', ['version_num'])
    op.create_index('ix_migration_history_applied_at', 'migration_history', ['applied_at'])

    print("✅ 迁移历史表创建完成!")

    # 记录重要迁移节点
    print("📝 记录重要迁移节点...")

    important_migrations = [
        ('4a278b4fa3d9', 'add_display_name_and_auth_fields_to_user_table', '添加display_name和认证字段到user表', 'user'),
        ('a49bc7d60f65', 'fix_timezone_datetime_columns_to_timezone_aware', '修复时区datetime列为时区感知类型', 'applicant,application,file,order,user,user_payment,visa_payment,visa_status_history'),
        ('a5a27975055f', 'create_migration_history_table', '创建迁移历史表', 'migration_history')
    ]

    for version, name, desc, tables in important_migrations:
        op.execute(f"""
            INSERT INTO migration_history (version_num, migration_name, description, tables_affected, applied_at, success)
            VALUES ('{version}', '{name}', '{desc}', '{tables}', now(), true)
        """)

    print("✅ 迁移历史记录完成!")


def downgrade() -> None:
    """删除迁移历史表"""
    print("🗑️ 删除迁移历史表...")
    op.drop_table('migration_history')
    print("✅ 迁移历史表删除完成!")
