"""add missing company fields to user table

Revision ID: add_missing_company_fields
Revises: 4a278b4fa3d9
Create Date: 2025-06-17 07:20:00.000000

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "add_missing_company_fields"
down_revision: str | None = "4a278b4fa3d9"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """
    只添加3个缺失的company字段，与User模型完全一致：
    - company_name: VARCHAR(128), nullable=True
    - company_address: VARCHAR(256), nullable=True
    - company_contacts: VARCHAR(64), nullable=True

    添加字段存在性检查，避免重复添加
    """
    # 获取数据库连接
    connection = op.get_bind()

    # 检查字段是否存在的SQL
    def column_exists(table_name, column_name):
        result = connection.execute(sa.text("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.columns
                WHERE table_name = :table_name
                AND column_name = :column_name
                AND table_schema = 'public'
            )
        """), {"table_name": table_name, "column_name": column_name})
        return result.scalar()

    # 只在字段不存在时添加
    if not column_exists("user", "company_name"):
        print("  🔧 添加 company_name 字段...")
        op.add_column(
            "user", sa.Column("company_name", sa.String(length=128), nullable=True)
        )
    else:
        print("  ✅ company_name 字段已存在，跳过")

    if not column_exists("user", "company_address"):
        print("  🔧 添加 company_address 字段...")
        op.add_column(
            "user", sa.Column("company_address", sa.String(length=256), nullable=True)
        )
    else:
        print("  ✅ company_address 字段已存在，跳过")

    if not column_exists("user", "company_contacts"):
        print("  🔧 添加 company_contacts 字段...")
        op.add_column(
            "user", sa.Column("company_contacts", sa.String(length=64), nullable=True)
        )
    else:
        print("  ✅ company_contacts 字段已存在，跳过")


def downgrade() -> None:
    """
    回滚操作：删除添加的3个字段
    """
    op.drop_column("user", "company_contacts")
    op.drop_column("user", "company_address")
    op.drop_column("user", "company_name")
