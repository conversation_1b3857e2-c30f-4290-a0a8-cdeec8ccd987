"""fix_user_password_hashes_to_argon2

Revision ID: fc0eefdc4553
Revises: 0bec8f21c976
Create Date: 2025-06-27 23:28:24.005052

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fc0eefdc4553'
down_revision: Union[str, None] = '0bec8f21c976'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """修复用户密码哈希格式 - 从bcrypt改为FastAPI-Users兼容的Argon2"""
    print("🔧 修复用户密码哈希格式...")

    # 更新管理员用户密码哈希
    op.execute("""
        UPDATE "user"
        SET hashed_password = '$argon2id$v=19$m=65536,t=3,p=4$YmvrZ+1ykUcamkZGQWBBoA$g1hKBUmiOG0lSwbvcjV5EvnDUDzD6Eugp2cCzeI44ks'
        WHERE email = '<EMAIL>'
    """)
    print("✅ 更新管理员用户密码哈希")

    # 更新测试用户1密码哈希
    op.execute("""
        UPDATE "user"
        SET hashed_password = '$argon2id$v=19$m=65536,t=3,p=4$ZgCSPA1/LAoNyNUk1+jACw$RwmK6spP3zkaNsWma4MWcft/Z7XQ3FO10qYNBlRFhuQ'
        WHERE email = '<EMAIL>'
    """)
    print("✅ 更新测试用户1密码哈希")

    # 更新测试用户2密码哈希
    op.execute("""
        UPDATE "user"
        SET hashed_password = '$argon2id$v=19$m=65536,t=3,p=4$pPVB9oAM5rnFo1mT2B831Q$e0lWVtfniSuPNHndNSneurhnFGquecWD1J1MA6sllkE'
        WHERE email = '<EMAIL>'
    """)
    print("✅ 更新测试用户2密码哈希")

    # 记录迁移历史
    print("📝 记录迁移历史...")
    op.execute(f"""
        INSERT INTO migration_history (
            version_num, migration_name, description, tables_affected, applied_at, success
        ) VALUES (
            '{revision}',
            'fix_user_password_hashes_to_argon2',
            '修复用户密码哈希格式，从bcrypt改为FastAPI-Users兼容的Argon2',
            'user',
            now(),
            true
        )
    """)
    print("✅ 迁移历史记录完成")

    print("🎉 密码哈希修复完成！")


def downgrade() -> None:
    """回滚密码哈希修复"""
    print("🔄 回滚密码哈希修复...")

    # 恢复管理员用户的bcrypt哈希
    op.execute("""
        UPDATE "user"
        SET hashed_password = '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW'
        WHERE email = '<EMAIL>'
    """)

    # 恢复测试用户1的bcrypt哈希
    op.execute("""
        UPDATE "user"
        SET hashed_password = '$2b$12$Kx9vR3mN8pL2wQ5tY7uI6eH4sG1fD9cA3bE8jM6nO0pR5tY8wQ2e'
        WHERE email = '<EMAIL>'
    """)

    # 恢复测试用户2的bcrypt哈希
    op.execute("""
        UPDATE "user"
        SET hashed_password = '$2b$12$P7qW9eR4tY6uI3oL5aS8dF2gHjK1mN4xC7vB6nM8pQ3wE5rT9yU'
        WHERE email = '<EMAIL>'
    """)

    # 删除迁移历史记录
    op.execute(f"DELETE FROM migration_history WHERE version_num = '{revision}'")

    print("✅ 密码哈希回滚完成！")
