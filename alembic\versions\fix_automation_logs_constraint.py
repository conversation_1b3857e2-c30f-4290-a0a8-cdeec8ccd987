"""fix automation_logs constraint

Revision ID: fix_automation_logs_constraint
Revises: fix_table_organization
Create Date: 2025-06-19 15:30:00.000000

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "fix_automation_logs_constraint"
down_revision = "fix_table_organization"
branch_labels = None
depends_on = None


def upgrade():
    """
    修复automation_logs约束冲突问题

    问题：fix_table_organization.py创建了错误的automation_task_status_check约束，
    只允许3个状态，但automation_completion_required约束期望processing状态存在

    解决：删除错误约束，重建正确约束
    """
    print("🔧 开始修复automation_logs约束冲突...")

    # 删除由fix_table_organization.py创建的错误约束
    print("🗑️ 删除错误的automation_task_status_check约束（只允许3个状态）...")
    op.drop_constraint("automation_task_status_check", "automation_logs", type_="check")
    print("✅ 成功删除automation_task_status_check约束")

    print("🗑️ 删除automation_completion_required约束...")
    op.drop_constraint(
        "automation_completion_required", "automation_logs", type_="check"
    )
    print("✅ 成功删除automation_completion_required约束")

    # 重建正确的约束：允许所有4个状态
    print("✅ 创建正确的automation_task_status_check约束（允许4个状态）...")
    op.create_check_constraint(
        "automation_task_status_check",
        "automation_logs",
        "task_status IN ('processing', 'success', 'failed', 'cancelled')",
    )
    print(
        "✅ 成功创建automation_task_status_check约束：processing, success, failed, cancelled"
    )

    # 重建完成时间约束：与状态约束保持一致
    print("✅ 创建automation_completion_required约束...")
    op.create_check_constraint(
        "automation_completion_required",
        "automation_logs",
        "(task_status IN ('success', 'failed', 'cancelled') AND completed_at IS NOT NULL) OR (task_status = 'processing' AND completed_at IS NULL)",
    )
    print("✅ 成功创建automation_completion_required约束")
    print("🎉 automation_logs约束修复完成！现在支持processing状态了！")


def downgrade():
    # 恢复原约束
    op.drop_constraint("automation_task_status_check", "automation_logs", type_="check")
    op.drop_constraint(
        "automation_completion_required", "automation_logs", type_="check"
    )

    # 恢复原来的错误约束
    op.create_check_constraint(
        "automation_task_status_check",
        "automation_logs",
        "task_status IN ('success', 'failed', 'cancelled')",
    )
    op.create_check_constraint(
        "automation_completion_required", "automation_logs", "completed_at IS NOT NULL"
    )
