"""简化状态系统 - 正确的业务逻辑实现

Revision ID: simplify_status_system
Revises: refactor_applicant_application
Create Date: 2025-01-23 20:00:00.000000

基于正确的业务逻辑理解：
1. automation_logs = 自动化任务执行记录（系统层面）
2. visa_status_history = 签证官方流程状态（业务层面）
3. 删除冗余的application.status字段
4. 简化visa_status_history为5个状态：submit_failure, submitted, additional_info_required, approved, denied
5. 删除visa_type表，保持application表原有字段结构
"""

import sqlalchemy as sa

from alembic import op

# revision identifiers
revision = "simplify_status_system"
down_revision = "refactor_applicant_application"
branch_labels = None
depends_on = None


def upgrade():
    """
    简化状态系统 - 实现正确的业务逻辑
    """

    print("🔧 开始简化状态系统...")

    # ==================== 1. 删除冗余的application.status ====================
    print("📋 删除冗余的application.status字段...")

    try:
        # 1.1 直接删除application.status字段（不做数据迁移）
        op.drop_column("application", "status")
        print("✅ 删除application.status字段")

    except Exception as e:
        print(f"⚠️ application.status处理失败: {e}")

    # ==================== 2. 简化visa_status_history状态约束 ====================
    print("📋 简化visa_status_history状态约束...")

    try:
        # 2.1 删除旧的状态约束（如果存在）
        op.execute(
            "ALTER TABLE visa_status_history DROP CONSTRAINT IF EXISTS visa_status_history_status_check"
        )

        # 2.2 添加简化的状态约束
        op.execute("""
            ALTER TABLE visa_status_history ADD CONSTRAINT simple_visa_status_check
            CHECK (to_status IN (
                'submit_failure',         -- 提交失败（自动化任务执行失败）
                'submitted',              -- 已提交（自动化成功提交到越南系统）
                'additional_info_required', -- 补充资料（越南领事馆要求补充额外信息）
                'approved',               -- 已批准（越南领事馆批准签证）
                'denied'                  -- 已拒绝（越南领事馆拒绝签证）
            ))
        """)
        print("✅ 添加简化的visa_status_history状态约束")

        # 2.3 清理不符合新约束的数据
        op.execute("""
            UPDATE visa_status_history
            SET to_status = CASE
                WHEN to_status IN ('created', 'pending', 'submitted', 'payment_pending') THEN 'submitted'
                WHEN to_status IN ('document_required', 'additional_info', 'documents_requested') THEN 'additional_info_required'
                WHEN to_status IN ('approved', 'completed', 'issued') THEN 'approved'
                WHEN to_status IN ('rejected', 'denied', 'cancelled') THEN 'denied'
                WHEN to_status IN ('failed', 'error', 'submit_failed') THEN 'submit_failure'
                ELSE 'submitted'
            END
            WHERE to_status NOT IN ('submit_failure', 'submitted', 'additional_info_required', 'approved', 'denied')
        """)
        print("✅ 清理并标准化visa_status_history数据")

    except Exception as e:
        print(f"⚠️ visa_status_history约束更新失败: {e}")

    # ==================== 3. 完善automation_logs时间戳逻辑 ====================
    print("🤖 完善automation_logs时间戳逻辑...")

    try:
        # 3.1 添加时间戳逻辑约束
        op.execute("""
            ALTER TABLE automation_logs ADD CONSTRAINT check_completion_time_logic
            CHECK (
                (task_status = 'processing' AND completed_at IS NULL) OR
                (task_status IN ('success', 'failed', 'cancelled') AND completed_at IS NOT NULL)
            )
        """)
        print("✅ 添加automation_logs时间戳逻辑约束")

        # 3.2 修复现有数据的completed_at字段
        op.execute("""
            UPDATE automation_logs
            SET completed_at = updated_at
            WHERE task_status IN ('success', 'failed', 'cancelled')
            AND completed_at IS NULL
        """)
        print("✅ 修复automation_logs的completed_at数据")

    except Exception as e:
        print(f"⚠️ automation_logs时间戳处理失败: {e}")

    # ==================== 4. 清理其他冗余字段 ====================
    print("🧹 清理其他冗余字段...")

    # 4.1 删除user.display_name（如果存在）
    try:
        op.drop_column("user", "display_name")
        print("✅ 删除user.display_name字段")
    except Exception as e:
        print(f"⚠️ user.display_name字段删除失败或不存在: {e}")

    # 4.2 删除visa_status表（根据用户决策：过度设计，维护负担，架构脱节）
    try:
        print("🗑️ 开始删除visa_status表...")

        # 使用CASCADE直接删除表，自动处理所有依赖关系
        op.execute("DROP TABLE IF EXISTS visa_status CASCADE")
        print("✅ 删除visa_status表")

        print("🎯 删除理由:")
        print("   • 过度设计：当前所有签证类型都使用相同的5状态流程")
        print("   • 维护负担：需要为每种签证类型配置状态，但实际都相同")
        print("   • 架构脱节：visa_status_history直接使用字符串，未引用visa_status表")

    except Exception as e:
        print(f"⚠️ visa_status表删除失败: {e}")

    # 4.3 删除visa_field表（完全未使用的死代码）
    try:
        print("🗑️ 删除visa_field表（死代码）...")
        op.execute("DROP TABLE IF EXISTS visa_field CASCADE")
        print("✅ 删除visa_field表")
        print("🎯 删除理由: 完全未使用，前端使用硬编码表单字段")

    except Exception as e:
        print(f"⚠️ visa_field表删除失败: {e}")

    # 4.4 删除visa_type表（简化为配置）
    try:
        print("🗑️ 删除visa_type表（改为代码配置）...")
        op.execute("DROP TABLE IF EXISTS visa_type CASCADE")
        print("✅ 删除visa_type表")
        print("🎯 删除理由: 过度设计，本质是配置而非数据表")

    except Exception as e:
        print(f"⚠️ visa_type表删除失败: {e}")

    # 4.5 向application表添加签证配置字段
    try:
        print("🔧 向application表添加签证配置字段...")

        # 添加签证基本配置字段
        op.add_column(
            "application",
            sa.Column("country", sa.String(3), doc="国家代码 (ISO 3166-1 alpha-3)"),
        )
        op.add_column(
            "application",
            sa.Column(
                "category",
                sa.String(32),
                doc="签证类别 (tourist, business, working, visit_family)",
            ),
        )

        # 添加用户选择的字段
        op.add_column(
            "application",
            sa.Column(
                "selected_validity_duration", sa.String(32), doc="用户选择的有效期"
            ),
        )
        op.add_column(
            "application",
            sa.Column("selected_entry_type", sa.String(32), doc="用户选择的入境类型"),
        )

        print("✅ 添加application表签证配置字段完成")

    except Exception as e:
        print(f"⚠️ application表字段添加失败: {e}")

    # 4.6 简化order.order_status（删除重建）
    try:
        # 删除旧的order_status字段
        op.execute('ALTER TABLE "order" DROP COLUMN IF EXISTS order_status CASCADE')
        print("✅ 删除旧的order_status字段")

        # 重新添加干净的order_status字段
        op.add_column(
            "order",
            sa.Column(
                "order_status", sa.String(32), nullable=False, server_default="created"
            ),
        )
        print("✅ 添加新的order_status字段")

        # 添加简化的订单状态约束
        op.execute("""
            ALTER TABLE "order" ADD CONSTRAINT simple_order_status_check
            CHECK (order_status IN ('created', 'cancelled'))
        """)
        print("✅ 添加order状态约束")

    except Exception as e:
        print(f"⚠️ order状态简化失败: {e}")

    # ==================== 5. 更新字段注释 ====================
    print("📝 更新字段注释...")

    try:
        op.execute(
            "COMMENT ON COLUMN automation_logs.task_status IS '任务状态: processing, success, failed, cancelled'"
        )
        op.execute(
            "COMMENT ON COLUMN automation_logs.completed_at IS '完成时间: 任务完成时间（成功或失败都要记录）'"
        )
        op.execute(
            "COMMENT ON COLUMN visa_status_history.to_status IS '签证状态: submit_failure, submitted, additional_info_required, approved, denied'"
        )
        op.execute(
            "COMMENT ON COLUMN visa_status_history.from_status IS '原状态: 状态变更前的状态'"
        )
        op.execute(
            "COMMENT ON COLUMN \"order\".order_status IS '订单状态: created, cancelled（纯订单管理）'"
        )
        print("✅ 字段注释更新完成")
    except Exception as e:
        print(f"⚠️ 字段注释更新失败: {e}")

    # ==================== 总结 ====================
    print("\n" + "=" * 60)
    print("✅ 状态系统简化完成！")
    print("=" * 60)
    print("📋 已完成的简化：")
    print("   ✓ 删除application.status冗余字段")
    print(
        "   ✓ 简化visa_status_history为5个状态: submit_failure, submitted, additional_info_required, approved, denied"
    )
    print("   ✓ 完善automation_logs时间戳逻辑")
    print("   ✓ 删除user.display_name冗余字段")
    print("   ✓ 删除visa_status表（过度设计，架构脱节）")
    print("   ✓ 删除visa_field表（死代码，完全未使用）")
    print("   ✓ 删除visa_type表（去除过度设计）")
    print("   ✓ 在application表添加country和category字段")
    print("   ✓ 简化order.order_status为纯订单管理")
    print("\n🎯 新的状态逻辑：")
    print("   • automation_logs: 自动化任务执行记录")
    print("   • visa_status_history: 签证流程状态（包含提交成功/失败及后续官方流程）")
    print("   • 前端状态: processing, success, failed")


def downgrade():
    """
    回滚操作
    """
    print("🔄 回滚状态系统简化...")

    # 恢复application.status字段
    try:
        op.add_column(
            "application",
            sa.Column(
                "status",
                sa.String(32),
                nullable=False,
                default="created",
                doc="当前状态",
            ),
        )
        print("✅ 恢复application.status字段")
    except Exception as e:
        print(f"⚠️ 恢复application.status字段失败: {e}")

    # 恢复user.display_name字段
    try:
        op.add_column("user", sa.Column("display_name", sa.String(64), doc="显示昵称"))
        print("✅ 恢复user.display_name字段")
    except Exception as e:
        print(f"⚠️ 恢复display_name字段失败: {e}")

    # 恢复visa_status表
    try:
        # 重新创建visa_status表
        from sqlalchemy.dialects import postgresql

        op.create_table(
            "visa_status",
            sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
            sa.Column("visa_type_id", postgresql.UUID(as_uuid=True), nullable=False),
            sa.Column("status_code", sa.String(length=32), nullable=False),
            sa.Column("status_name", sa.String(length=64), nullable=False),
            sa.Column("description", sa.String(length=256), nullable=True),
            sa.Column("order", sa.Integer(), nullable=True, default=0),
            sa.Column("is_terminal", sa.Boolean(), nullable=True, default=False),
            sa.Column(
                "created_at",
                sa.DateTime(),
                server_default=sa.text("now()"),
                nullable=True,
            ),
            sa.Column(
                "updated_at",
                sa.DateTime(),
                server_default=sa.text("now()"),
                nullable=True,
            ),
            sa.ForeignKeyConstraint(
                ["visa_type_id"], ["visa_type.id"], ondelete="CASCADE"
            ),
            sa.PrimaryKeyConstraint("id"),
            sa.UniqueConstraint("visa_type_id", "status_code", name="uq_visa_status"),
        )

        # 重新创建索引
        op.create_index(
            "ix_visa_status_visa_type_id", "visa_status", ["visa_type_id"], unique=False
        )
        op.create_index(
            "ix_visa_status_type_order",
            "visa_status",
            ["visa_type_id", "order"],
            unique=False,
        )

        # 恢复备份数据
        backup_exists = (
            op.get_bind()
            .execute(
                sa.text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'visa_status_backup'
            )
        """)
            )
            .scalar()
        )

        if backup_exists:
            op.execute("INSERT INTO visa_status SELECT * FROM visa_status_backup")
            op.drop_table("visa_status_backup")
            print("✅ 恢复visa_status表和数据")
        else:
            print("⚠️ 未找到备份表，visa_status表已重新创建但无数据")

    except Exception as e:
        print(f"⚠️ 恢复visa_status表失败: {e}")

    # 恢复visa_field表
    try:
        # 重新创建visa_field表
        from sqlalchemy.dialects import postgresql

        op.create_table(
            "visa_field",
            sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
            sa.Column("visa_type_id", postgresql.UUID(as_uuid=True), nullable=False),
            sa.Column("field_name", sa.String(length=64), nullable=False),
            sa.Column("field_type", sa.String(length=32), nullable=False),
            sa.Column("required", sa.Boolean(), nullable=True, default=False),
            sa.Column("label", sa.String(length=128), nullable=True),
            sa.Column("description", sa.String(length=256), nullable=True),
            sa.Column("order", sa.Integer(), nullable=True, default=0),
            sa.Column(
                "created_at",
                sa.DateTime(),
                server_default=sa.text("now()"),
                nullable=True,
            ),
            sa.Column(
                "updated_at",
                sa.DateTime(),
                server_default=sa.text("now()"),
                nullable=True,
            ),
            sa.ForeignKeyConstraint(
                ["visa_type_id"], ["visa_type.id"], ondelete="CASCADE"
            ),
            sa.PrimaryKeyConstraint("id"),
            sa.UniqueConstraint("visa_type_id", "field_name", name="uq_visa_field"),
        )

        # 恢复索引
        op.create_index(
            "ix_visa_field_visa_type_id", "visa_field", ["visa_type_id"], unique=False
        )
        op.create_index(
            "ix_visa_field_type_order",
            "visa_field",
            ["visa_type_id", "order"],
            unique=False,
        )

        # 恢复备份数据
        backup_exists = (
            op.get_bind()
            .execute(
                sa.text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'visa_field_backup'
            )
        """)
            )
            .scalar()
        )

        if backup_exists:
            op.execute("INSERT INTO visa_field SELECT * FROM visa_field_backup")
            op.drop_table("visa_field_backup")
            print("✅ 恢复visa_field表和数据")
        else:
            print("✅ 重新创建空的visa_field表")

    except Exception as e:
        print(f"⚠️ 恢复visa_field表失败: {e}")

    # 恢复visa_type表
    try:
        print("🔄 恢复visa_type表...")

        # 重新创建visa_type表
        from sqlalchemy.dialects import postgresql

        op.create_table(
            "visa_type",
            sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
            sa.Column("country", sa.String(length=64), nullable=False),
            sa.Column("visa_code", sa.String(length=32), nullable=False),
            sa.Column("visa_name", sa.String(length=128), nullable=False),
            sa.Column("validity_days", sa.Integer(), nullable=True),
            sa.Column("entry_type", sa.String(length=32), nullable=True),
            sa.Column("price", sa.Float(), nullable=True),
            sa.Column("description", sa.String(length=256), nullable=True),
            sa.Column("enabled", sa.Boolean(), nullable=True, default=True),
            sa.Column(
                "created_at",
                sa.DateTime(),
                server_default=sa.text("now()"),
                nullable=True,
            ),
            sa.Column(
                "updated_at",
                sa.DateTime(),
                server_default=sa.text("now()"),
                nullable=True,
            ),
            sa.PrimaryKeyConstraint("id"),
            sa.UniqueConstraint("country", "visa_code", name="uq_country_visa_code"),
        )

        # 恢复备份数据
        backup_exists = (
            op.get_bind()
            .execute(
                sa.text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'visa_type_backup'
            )
        """)
            )
            .scalar()
        )

        if backup_exists:
            op.execute("INSERT INTO visa_type SELECT * FROM visa_type_backup")
            op.drop_table("visa_type_backup")
            print("✅ 恢复visa_type表和数据")
        else:
            print("⚠️ 未找到备份，visa_type表已重新创建但无数据")

        # 删除application表中添加的字段
        op.drop_column("application", "country")
        op.drop_column("application", "category")
        op.drop_column("application", "selected_validity_duration")
        op.drop_column("application", "selected_entry_type")
        print("✅ 删除application表中添加的字段")

    except Exception as e:
        print(f"⚠️ 恢复visa_type表失败: {e}")

    print("✅ 回滚完成")
