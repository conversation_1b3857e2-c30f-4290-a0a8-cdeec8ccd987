from datetime import datetime
from pathlib import Path
import re

from app.batch_processing.batch_passport_ocr import extract_passport_info_via_ocr
from app.batch_processing.contact_parser import find_default_contact_from_folder
from app.data.model import VietnamEVisaApplicant
from app.utils.date_utils import (
    VIETNAM_HOLIDAYS_2025,
    calculate_working_days_from_today,
)
from app.utils.logger_config import get_logger

# 设置日志
logger = get_logger()


ENTRY_GATE_MAP = {
    "胡志明": "Tan Son Nhat Int Airport (Ho Chi Minh City)",
    "河内": "Noi Bai Int Airport",
    "岘港": "Da Nang International Airport",
    "芽庄": "Cam Ranh Int Airport (Khanh Hoa)",
    "东兴": "Mong Cai Landport",  # 添加新的东兴口岸
    "友谊": "Huu Nghi Landport",  # 添加新的友谊口岸
}

# 支持：
# 22-30天-单次-岘港-15-06-2025.jpg
# 22-30天-单次-岘港-出签生效.jpg
FILENAME_PATTERN = re.compile(
    r"(\d+)[\.-](\d+天)[\.-](单次|多次)[\.-]([\u4e00-\u9fa5A-Za-z]+)[\.-]((\d{2}-\d{2}-\d{4})|出签生效)"
)

# ✅ 计算从今天起第 4 个工作日 通过调用app.utils.date_utils模块实现
# visa_start_date_obj = calculate_working_days_from_today(4, holidays=VIETNAM_HOLIDAYS_2025)
# visa_start_date = visa_start_date_obj.strftime("%d/%m/%Y")


def import_applicants_from_folder(folder_path: str) -> list[VietnamEVisaApplicant]:
    folder = Path(folder_path)
    applicants = []

    passport_images = {}
    photo_images = {}

    # 提取默认手机号和邮箱（默认从文档提取）
    default_phone, default_email = find_default_contact_from_folder(folder_path)
    if default_phone:
        logger.info(f"📱 从文档读取到默认手机号: {default_phone}")
    if default_email:
        logger.info(f"📧 从文档读取到默认邮箱: {default_email}")

    # 扫描常见图片格式
    for file in folder.glob("*"):
        if file.suffix.lower() not in [".jpg", ".jpeg", ".png", ".bmp", ".webp"]:
            continue
        name = file.stem  # 文件名（不含扩展名）

        # 匹配护照图
        match = FILENAME_PATTERN.match(name)
        if match:
            groups = match.groups()
            number, days, entry_type, entry_gate, raw_date = groups[:5]

            # ✅ 口岸映射检查
            if entry_gate.strip() not in ENTRY_GATE_MAP:
                logger.error(f"❌ 未知入境口岸：{entry_gate}，请检查文件名拼写！")
                continue  # 跳过这个申请人

            mapped_gate = ENTRY_GATE_MAP[entry_gate.strip()]

            # 自动计算出签生效日期
            if raw_date == "出签生效":
                visa_start_date_obj = calculate_working_days_from_today(
                    4, holidays=VIETNAM_HOLIDAYS_2025
                )
                visa_start_date = visa_start_date_obj.strftime("%d/%m/%Y")
            else:
                visa_start_date = datetime.strptime(raw_date, "%d-%m-%Y").strftime(
                    "%d/%m/%Y"
                )

            passport_images[number] = {
                "path": file,
                "days": days,
                "entry_type": "Single-entry"
                if entry_type == "单次"
                else "Multiple-entry",
                "entry_gate": mapped_gate,
                "visa_start_date": visa_start_date,
            }
        else:
            # 假设是照片图（编号如 1.jpg）
            photo_images[file.stem] = file

    # 自动配对构造 Applicant（仅部分字段，后续由 OCR 补全）
    for number, passport_info in passport_images.items():
        photo_number = number[0]  # 如 22 对应 2.jpg
        portrait_path = photo_images.get(photo_number)

        if not portrait_path:
            logger.warning(f"⚠️ 找不到与 {number} 配对的照片图")
            continue

        # ✅ 执行 OCR
        ocr_data = extract_passport_info_via_ocr(str(passport_info["path"]))

        if not ocr_data:
            logger.error(f"⚠️ OCR失败，跳过 {number}")
            continue

        # 从图片名中提取邮箱/手机号（优先）
        file_name_parts = Path(passport_info["path"]).stem.split("-")
        phone_from_name = next(
            (part for part in file_name_parts if re.fullmatch(r"1[3-9]\d{9}", part)),
            None,
        )
        email_from_name = next(
            (
                part
                for part in file_name_parts
                if re.fullmatch(r"[\w\.-]+@[\w\.-]+\.\w+", part)
            ),
            None,
        )

        final_phone = phone_from_name or default_phone or ""
        final_email = email_from_name or default_email or ""

        logger.info(
            f"📄 {number} 使用单独手机号: {final_phone}，使用单独邮箱: {final_email}"
        )

        applicant = VietnamEVisaApplicant(
            surname=ocr_data.get("surname", ""),
            given_name=ocr_data.get("given_name", ""),
            chinese_name=ocr_data.get("chinese_name", ""),
            sex=ocr_data.get("sex", ""),
            dob=ocr_data.get("dob", ""),
            place_of_birth=ocr_data.get("place_of_birth", ""),
            passport_number=ocr_data.get("passport_number", ""),
            place_of_issue=ocr_data.get("place_of_issue", ""),
            date_of_issue=ocr_data.get("date_of_issue", ""),
            passport_expiry=ocr_data.get("passport_expiry", ""),
            nationality=ocr_data.get("nationality", ""),
            visa_validity_duration=str(passport_info["days"]),
            visa_entry_type=str(passport_info["entry_type"]),
            intended_entry_gate=str(passport_info["entry_gate"]),
            visa_start_date=str(passport_info["visa_start_date"]),
            portrait_photo_path=str(portrait_path),
            passport_scan_path=str(passport_info["path"]),
            email=final_email,
            telephone_number=final_phone,
        )
        applicants.append(applicant)

    logger.info(f"✅ 共导入 {len(applicants)} 位申请人")

    return applicants
