from app.data.applicant_mapper import normalize_ocr_result_for_applicant
from app.utils.logger_config import get_logger
from app.utils.ocr_utils import run_aliyun_passport_ocr

logger = get_logger()


def extract_passport_info_via_ocr(image_path: str) -> dict:
    logger.info(f"[批量OCR] 开始处理护照图片: {image_path}")
    ocr_result = run_aliyun_passport_ocr(image_path)

    if not ocr_result:
        logger.warning("OCR识别失败，结果为空")
        return {}

    # 使用统一映射器处理OCR结果（替代原来的重复映射逻辑）
    normalized_result = normalize_ocr_result_for_applicant(ocr_result)

    if normalized_result:
        logger.info(
            f"✅ 批量OCR映射成功，国籍: {normalized_result.get('nationality', 'N/A')}"
        )
        return normalized_result
    else:
        logger.warning("OCR映射失败，返回空结果")
        return {}
