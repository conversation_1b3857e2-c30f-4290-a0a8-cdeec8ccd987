from pathlib import Path
import re

from docx import Document
import openpyxl

from app.utils.logger_config import get_logger

logger = get_logger()


def extract_phone_and_email_from_text(text: str) -> tuple[str | None, str | None]:
    """从纯文本中提取手机号和邮箱"""
    phone_match = re.search(r"1[3-9]\d{9}", text)
    email_match = re.search(r"[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+", text)
    phone = phone_match.group(0) if phone_match else None
    email = email_match.group(0) if email_match else None
    return phone, email


def read_docx(path: Path) -> str:
    """读取 Word 文档内容"""
    try:
        logger.info(f"正在读取 Word 文档: {path}")
        doc = Document(str(path))
        return "\n".join(p.text for p in doc.paragraphs)
    except Exception as e:
        logger.error(f"读取 Word 文档失败: {e}")
        return ""


def read_txt(path: Path) -> str:
    """读取纯文本内容"""
    try:
        logger.info(f"正在读取文本文件: {path}")
        return path.read_text(encoding="utf-8", errors="ignore")
    except Exception as e:
        logger.error(f"读取文本文件失败: {e}")
        return ""


def read_xlsx(path: Path) -> str:
    """读取 Excel 内容（仅提取前两列文本）"""
    try:
        wb = openpyxl.load_workbook(path)
        text = []
        for sheet in wb.worksheets:
            for row in sheet.iter_rows(values_only=True):
                row_text = [str(cell) for cell in row if cell]
                text.append(" ".join(row_text))
        return "\n".join(text)
    except Exception as e:
        logger.error(f"读取 Excel 文件失败: {e}")
        return ""


def find_default_contact_from_folder(
    folder_path: str,
) -> tuple[str | None, str | None]:
    """
    扫描文件夹中的文档，提取首个手机号和邮箱（默认值）
    """
    folder = Path(folder_path)
    for file in folder.glob("*"):
        if file.suffix.lower() in [".txt", ".log"]:
            text = read_txt(file)
        elif file.suffix.lower() in [".docx", ".doc", ".wps"]:
            text = read_docx(file)
        elif file.suffix.lower() in [".xls", ".xlsx", ".et"]:
            text = read_xlsx(file)
        else:
            logger.info(f"跳过不支持的文件类型: {file}")
            continue

        phone, email = extract_phone_and_email_from_text(text)
        if phone or email:
            logger.info(
                f"✅ 在文件 {file.name} 中找到联系方式 - 手机: {phone}, 邮箱: {email}"
            )
            return phone, email
    logger.warning(f"⚠️ 在 {file.name} 个文件中未找到任何联系方式")
    return None, None  # 未找到任何联系方式
