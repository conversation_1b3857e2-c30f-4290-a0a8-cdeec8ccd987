# 文件: app/core/visa_automation_engine.py
# 移除未使用的 threading 导入
from pathlib import Path
from time import sleep  # 保留 sleep 导入
import uuid

from playwright.sync_api import Error as PlaywrightError
from playwright.sync_api import sync_playwright
import yaml

from app.data.model import VietnamEVisaApplicant
from app.fillers.vietnam_filler import VietnamFiller
from app.payment.payment_automation import process_payment_with_existing_page
from app.utils.logger_config import get_logger

# 移除未使用的 ThreadPoolExecutor 导入
# 移除模块级别的数据库导入，改为在函数内部按需导入，避免循环导入
# from app.database.sync_wrapper import get_or_create_applicant, insert_visa_task, insert_payment_record, check_payment_exists
# 移除模块级别的邮件导入，改为本地导入避免循环导入
# from app.email.email_trigger import confirm_payment_arrived
# 导入统一的浏览器配置模块
from config.browser_config import get_browser_config, launch_form_browser

# 注: 简化状态推送，专注于数据库更新


logger = get_logger()

BASE_DIR = Path(__file__).resolve().parent.parent.parent  # 项目根目录


class VisaAutomationEngine:
    """
    专业的签证自动化引擎。
    处理配置加载、浏览器自动化、表单填写、验证码处理和状态管理。
    支持多国签证申请流程的自动化处理。
    """

    def __init__(self):
        self.locators = {}
        self.settings = {}
        self._load_config()

    # 移除了 _log_process_status 函数，直接使用 logger 记录状态

    def _load_config(self):
        """加载元素定位器locators/*.yaml和环境变量配置"""
        # 加载定位器
        locators_dir = BASE_DIR / "config/locators"
        logger.debug(f"Attempting to load locators from directory: {locators_dir}")
        if locators_dir.is_dir():
            for file_path in locators_dir.glob("*.yaml"):
                try:
                    with open(file_path, encoding="utf-8") as f:
                        country_key = file_path.stem
                        self.locators[country_key] = yaml.safe_load(f) or {}
                    logger.info(f"✅ Successfully loaded locators: {file_path}")
                except Exception as e:
                    logger.error(
                        f"Failed to load locator file {file_path}: {e}", exc_info=True
                    )
        else:
            logger.warning(f"Locators directory not found: {locators_dir}")

        # 从环境变量加载设置
        from app.utils.env_loader import load_env_var

        self.settings = {
            "vietnam_evisa_url": load_env_var(
                "VIETNAM_EVISA_URL", "https://evisa.gov.vn/"
            ),
            "browser": load_env_var("BROWSER", "chromium"),
            "headless": (load_env_var("HEADLESS", "true") or "true").lower() == "true",
            "slow_mo": int(load_env_var("SLOW_MO", "0") or "0"),
            "anti_captcha_api_key": load_env_var("ANTI_CAPTCHA_API_KEY", ""),
        }
        logger.info("✅ Successfully loaded settings from environment variables")

    def run_vietnam_evisa_step1(
        self, applicant: VietnamEVisaApplicant
    ) -> bool:  # 明确返回类型为 bool
        """
        执行越南电子签第一步填充和付款的核心函数。
        包含启动浏览器、导航、调用 Filler、执行付款和关闭浏览器。
        返回 True 表示整个流程（填表+付款）成功，否则返回 False。
        添加全流程自动重试机制，最大重试次数为3次(含第一次)

        ✅ 修复：简化状态更新，专注于数据库一致性
        """
        # 为整个流程创建一个会话ID
        session_id = str(uuid.uuid4())
        logger.info(
            f"创建新的自动化会话: {session_id}, 申请人: {applicant.passport_number}"
        )

        # ✅ 正确架构：simple_engine专注于核心业务逻辑
        # 不处理订单概念，只处理申请人信息
        applicant_info = f"{applicant.chinese_name} ({applicant.passport_number})"

        # 记录流程开始
        logger.info(f"📋 流程开始 - 会话{session_id}: 开始签证申请自动化流程")
        logger.info(f"   申请人: {applicant_info}")

        # ✅ 简化：移除重复付款检查逻辑
        # 在新架构下，重复检查应该在 FastAPI 层处理
        # simple_engine 专注于执行自动化流程
        logger.info(f"开始处理签证申请: {applicant.chinese_name}")

        # 检查是否是强制重新提交（如果需要的话）
        force_resubmit = getattr(applicant, "force_resubmit", False)
        if force_resubmit:
            logger.info(f"✅ 强制重新提交模式: {applicant.passport_number}")

        # 移除过时的重复付款检查逻辑

        # ✅ 修复：用于跟踪最终结果的变量
        final_success = False
        attempts_made = 0

        max_retries = 3
        for attempt in range(1, max_retries + 1):
            attempts_made = attempt
            logger.info(
                f"==== 第 {attempt} 次自动化流程尝试，会话ID: {session_id} ===="
            )
            try:
                logger.info(
                    f"引擎开始执行越南电子签流程（填表+付款），会话ID: {session_id}"
                )
                page = None
                browser = None
                p_context = None
                fill_success = False  # 初始化填表状态
                payment_success = False  # 初始化付款状态
                overall_success = False  # 初始化整体流程状态

                filler = VietnamFiller()
                # 将会话ID传递给filler
                filler.session_id = session_id
                try:
                    logger.debug(f"准备加载Filler配置,会话ID: {session_id}")
                    filler.prepare(self.locators, self.settings)
                    logger.debug(f"✅Filler配置加载成功,会话ID: {session_id}")
                except ValueError as e:
                    logger.error(f"Filler 准备失败: {e} 会话ID: {session_id}")
                    # 不直接返回，而是设置标志变量，允许重试循环继续
                    fill_success = False
                    continue  # 跳到下一次重试循环

                # 从统一的浏览器配置模块获取配置
                browser_config = get_browser_config()
                browser_type = browser_config.get("browser_type", "chromium")
                headless = browser_config.get("headless", True)
                # 修复 union-attr 错误：安全获取配置值
                headless_env = self.settings.get("headless")
                if headless_env is not None and isinstance(headless_env, bool):
                    headless = headless_env
                slow_mo = int(self.settings.get("slow_mo", 0) or 0)
                logger.debug(
                    f"浏览器配置: 类型={browser_type}, 无头模式={headless}, 慢动作={slow_mo}ms"
                )

                # Playwright 上下文管理
                try:
                    logger.info(
                        f"启动 Playwright (浏览器: {browser_type}, Headless: {headless})..."
                    )
                    p_context = sync_playwright().start()
                    logger.debug("✅ Playwright上下文创建成功")

                    # 使用统一的浏览器启动函数
                    browser, _, page = launch_form_browser(p_context)
                    logger.debug("✅ 浏览器、上下文和页面已创建")

                    # 导航到目标URL并实现一次自动刷新
                    logger.info(f"导航至: {filler.base_url}")
                    if not filler.base_url:
                        logger.error("配置中缺少越南电子签 URL！")
                        raise ValueError(
                            "配置中缺少越南电子签 URL"
                        )  # 抛出异常而不是返回False
                    try:
                        # 第一次尝试加载页面，30秒超时
                        logger.info("开始加载页面...")
                        page.goto(filler.base_url, timeout=30000)  # 30秒超时
                        logger.debug("✅ 页面加载成功")
                    except Exception as e:
                        logger.warning(
                            f"❌页面加载超时 (30秒)，尝试刷新: {e},再等30秒..."
                        )
                        try:
                            # 刷新页面，再等30秒
                            page.reload(timeout=30000)  # 再给30秒尝试刷新
                            logger.info("✅ 页面刷新加载成功")
                        except Exception as refresh_e:
                            # 如果刷新后仍然失败，则触发外层重试
                            logger.error(f"❌页面刷新后仍然加载失败: {refresh_e}")
                            raise  # 重新抛出异常，触发外层重试

                    # --- 步骤 1: 执行填表 ---
                    logger.info("页面加载完成，开始调用 filler 填充...")
                    # 保存filler实例到engine中，以便API能够访问
                    self.filler = filler
                    fill_success = filler.fill_step1_personal_info(page, applicant)
                    logger.debug(f"填表结果: {fill_success}")

                    payment_success = False
                    if fill_success:
                        logger.info("✅ Filler 主要填充流程成功完成。")

                        # --- 步骤 2: 执行付款 ---
                        try:
                            # 使用现有页面执行付款流程
                            logger.info("使用现有页面执行付款流程...")
                            # 从统一的浏览器配置模块获取付款配置
                            payment_settings = {
                                "payment_timeout_ms": browser_config.get(
                                    "timeout_ms", 300000
                                ),
                                "screenshots_dir": browser_config.get(
                                    "screenshots_dir", "screenshots"
                                ),
                            }

                            # 直接在当前线程中执行，不使用ThreadPoolExecutor
                            payment_result = process_payment_with_existing_page(
                                page, settings=payment_settings
                            )

                            if payment_result is True:
                                logger.info("✅ 支付流程成功完成")
                                payment_success = True
                            elif payment_result is False:
                                logger.warning("❌ 支付流程失败")
                                payment_success = False
                            elif payment_result is None or payment_result == "unknown":  # type: ignore[unreachable]
                                logger.info(
                                    "已成功执行支付自动化流程，但付款状态不明确，请客户检查邮箱确认付款状态。"
                                )
                                payment_success = True  # 将付款状态不明确视为成功
                        except Exception as payment_e:
                            logger.error(
                                f"❌ 在执行付款流程时发生异常: {payment_e}",
                                exc_info=True,
                            )
                            logger.debug(f"❌ 付款异常详情: {str(payment_e)}")
                            payment_success = False
                            # 移除这里的 return False，让代码继续执行到 finally 块进行资源清理
                    else:
                        logger.error("❌ Filler 报告在填充过程中遇到错误。")
                        logger.debug("❌ 填表失败，检查 Filler 日志以获取详细错误信息")
                        if not headless:
                            logger.info(
                                "❌ 流程出错（填表失败），浏览器将保持打开 10 秒钟以便检查错误..."
                            )
                            logger.debug("非无头模式下保持浏览器打开10秒以便检查")
                            sleep(10)
                        fill_success = False
                        # return False  # 填表失败也返回失败状态

                except PlaywrightError as pe:
                    logger.error(f"Playwright 运行时发生错误: {pe}", exc_info=True)
                    logger.debug(f"Playwright错误详情: {str(pe)}")
                    raise  # 重新抛出异常，让外层catch捕获并触发重试
                except Exception as e:
                    logger.critical(
                        f"❌ 自动化引擎运行时发生未预料的严重错误: {e}", exc_info=True
                    )
                    logger.debug(f"❌ 未预期异常详情: {e!r},{str(e)}")
                    raise  # 重新抛出异常，让外层catch捕获并触发重试
                finally:
                    # --- 清理填表流程的资源 ---
                    logger.info("执行填表流程清理操作...")
                    try:
                        if page and not page.is_closed():
                            try:
                                logger.debug("关闭页面")
                                page.close()
                                logger.info("页面已关闭。")
                            except Exception as page_close_e:
                                logger.warning(
                                    f"❌ 关闭页面时发生错误（可忽略）: {page_close_e}"
                                )
                                logger.debug(
                                    f"❌ 关闭页面错误详情: {str(page_close_e)}"
                                )

                        if browser and browser.is_connected():
                            try:
                                logger.debug("关闭浏览器")
                                browser.close()
                                logger.info("浏览器已关闭。")
                            except Exception as browser_close_e:
                                logger.warning(
                                    f"❌ 关闭浏览器时发生错误（可忽略）: {browser_close_e}"
                                )
                                logger.debug(
                                    f"❌ 关闭浏览器错误详情: {str(browser_close_e)}"
                                )

                        if p_context:
                            try:
                                logger.debug("停止Playwright上下文")
                                p_context.stop()
                                logger.info("Playwright 上下文已停止。")
                            except Exception as playwright_stop_e:
                                logger.warning(
                                    f"❌ 停止 Playwright 时发生错误（可忽略）: {playwright_stop_e}"
                                )
                                logger.debug(
                                    f"❌ 停止Playwright错误详情: {str(playwright_stop_e)}"
                                )

                        logger.info("填表流程清理完成。")
                    except Exception as e:
                        logger.critical(
                            f"❌ 在执行清理操作时发生严重错误: {e}", exc_info=True
                        )
                        logger.debug(f"❌ 清理操作严重错误详情: {str(e)}")
                        # 确保即使在清理过程中出现问题，也能继续执行后续代码

                # --- 步骤 3: 确定整体流程结果 ---
                overall_success = (
                    fill_success and payment_success
                )  # 必须填表和付款都成功
                logger.debug(
                    f"整体流程结果: 填表={fill_success}, 付款={payment_success}, 整体={overall_success}"
                )
                if overall_success:
                    logger.info(
                        f"✅✅ 整个签证申请流程（填表+付款）成功完成：会话{session_id}，申请人: {applicant_info}"
                    )

                    # ✅ 正确架构：simple_engine不修改applicant对象
                    # 申请编号将通过Celery任务完成通知传递给FastAPI层保存到数据库
                    # ✅ 获取准确的关键信息
                    vietnam_application_number = getattr(
                        filler, "application_number", None
                    )
                    payment_amount = getattr(filler, "payment_amount", None)

                    logger.info("✅ 申请流程成功完成，准确的关键信息:")
                    logger.info(
                        f"   - 申请人: {applicant.chinese_name} ({applicant.passport_number})"
                    )
                    logger.info(f"   - 越南申请编号: {vietnam_application_number}")
                    logger.info(f"   - 付款金额: {payment_amount}")
                    logger.info(f"   - 会话ID: {session_id}")

                    # ✅ 修复数据传递断链：将准确的关键数据传递给 applicant 对象
                    # 这样 Celery 任务就能正确获取到数据
                    if vietnam_application_number:
                        applicant.application_number = vietnam_application_number
                        logger.info(
                            f"✅ 申请编号已传递给applicant对象: {vietnam_application_number}"
                        )

                    if payment_amount:
                        applicant.payment_amount = payment_amount
                        logger.info(
                            f"✅ 付款金额已传递给applicant对象: {payment_amount}"
                        )

                    final_success = True  # 设置最终成功标志
                    return True  # 返回整体流程成功
                else:
                    logger.warning(
                        "⚠️⚠️ 整个签证申请流程未能成功完成（填表或付款失败）。"
                    )
                    logger.debug(
                        f"流程失败原因: 填表状态={fill_success}, 付款状态={payment_success}"
                    )

                    # ✅ 修复：设置当前尝试的失败信息，但不立即推送
                    failure_reason = "填表失败" if not fill_success else "付款失败"
                    current_attempt_message = f"第{attempt}次尝试失败 - {failure_reason}: {applicant.chinese_name}"
                    logger.info(current_attempt_message)

                logger.info("准备关闭浏览器...")

                # return overall_success # 返回整体流程的最终结果  # type: ignore[unreachable]
                # --- 添加垃圾回收 ---
                try:
                    import gc

                    logger.debug("执行垃圾回收")
                    gc.collect()
                    logger.info("✅ 垃圾回收已完成。")
                except Exception as gc_error:
                    logger.warning(f"垃圾回收时发生错误（可忽略）: {gc_error}")
                    logger.debug(f"垃圾回收错误详情: {str(gc_error)}")

            # ====== 原有全部流程代码结束 ======
            except Exception as e:
                logger.error(f"第 {attempt} 次流程发生未捕获异常: {e}", exc_info=True)
                logger.debug(f"未捕获异常详情: {str(e)}")
                logger.debug(f"未捕获异常详情 (e_attempt): {e!r}", exc_info=True)
            if attempt < max_retries:
                logger.info(f"准备进行第 {attempt + 1} 次全流程重试...")

        # ✅ 修复：在这里统一记录失败状态
        if not final_success:  # 仅在最终失败时记录失败状态
            final_failure_message = f"签证申请流程最终失败 - 已尝试{attempts_made}次"
            logger.error(f"📋 流程失败 - 会话{session_id}: {final_failure_message}")
            logger.error(f"   申请人: {applicant_info}")
            logger.error(
                f"❌ 最终结果：失败 - {final_failure_message}: {applicant.chinese_name}"
            )
            return False

        # 如果 final_success 为 True，返回成功
        else:
            return True
