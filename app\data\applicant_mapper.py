# app/data/applicant_mapper.py
"""
表单数据到Applicant对象的映射器
"""

from datetime import datetime
from typing import Any

from app.data.model import VietnamEVisaApplicant
from app.utils.logger_config import get_logger

logger = get_logger()


def normalize_date_format(date_str: str) -> str:
    """
    标准化日期格式，将YYYYMMDD转换为DD/MM/YYYY

    Args:
        date_str: 输入的日期字符串

    Returns:
        str: 标准化后的日期字符串 (DD/MM/YYYY)
    """
    if not date_str or not isinstance(date_str, str):
        return date_str

    # 如果已经是DD/MM/YYYY格式，直接返回
    if "/" in date_str and len(date_str) == 10:
        return date_str

    # 如果是YYYYMMDD格式，转换为DD/MM/YYYY
    if len(date_str) == 8 and date_str.isdigit():
        try:
            dt_obj = datetime.strptime(date_str, "%Y%m%d")
            formatted_date = dt_obj.strftime("%d/%m/%Y")
            logger.info(f"日期格式转换: {date_str} -> {formatted_date}")
            return formatted_date
        except ValueError:
            logger.warning(f"⚠️ 无法解析日期格式: {date_str}，保持原值")
            return date_str

    # 其他情况保持原值
    return date_str


def map_form_to_applicant(form_data: dict[str, Any]) -> VietnamEVisaApplicant:
    """
    将表单数据映射为VietnamEVisaApplicant对象

    Args:
        form_data: 来自前端表单的数据字典

    Returns:
        VietnamEVisaApplicant: 映射后的申请人对象
    """
    try:
        # 标准化日期格式
        dob_normalized = normalize_date_format(form_data.get("dob", ""))
        date_of_issue_normalized = normalize_date_format(
            form_data.get("date_of_issue", "")
        )
        passport_expiry_normalized = normalize_date_format(
            form_data.get("passport_expiry", "")
        )

        # 创建VietnamEVisaApplicant对象，使用表单数据
        applicant = VietnamEVisaApplicant(
            # 基本信息
            surname=form_data.get("surname", ""),
            given_name=form_data.get("given_name", ""),
            chinese_name=form_data.get("chinese_name", ""),
            sex=form_data.get("sex", ""),
            dob=dob_normalized,
            place_of_birth=form_data.get("place_of_birth", ""),
            nationality=form_data.get("nationality", "CHINA"),
            religion=form_data.get("religion", "NO"),
            # 护照信息
            passport_number=form_data.get("passport_number", ""),
            passport_type=form_data.get("passport_type", "Ordinary passport"),
            place_of_issue=form_data.get("place_of_issue", ""),
            date_of_issue=date_of_issue_normalized,
            passport_expiry=passport_expiry_normalized,
            # 联系信息
            email=form_data.get("email", ""),
            telephone_number=form_data.get("telephone_number", ""),
            permanent_address=form_data.get("permanent_address", ""),
            contact_address=form_data.get("contact_address", ""),
            # 紧急联系人
            emergency_contact_name=form_data.get("emergency_contact_name", ""),
            emergency_address=form_data.get("emergency_address", ""),
            emergency_contact_phone=form_data.get("emergency_contact_phone", ""),
            # 签证信息
            visa_entry_type=form_data.get("visa_entry_type", ""),
            visa_validity_duration=form_data.get("visa_validity_duration", ""),
            visa_start_date=form_data.get("visa_start_date", ""),
            intended_entry_gate=form_data.get("intended_entry_gate", ""),
            purpose_of_entry=form_data.get("purpose_of_entry", "Tourist"),
            # 文件路径（如果有的话）
            portrait_photo_path=form_data.get("portrait_photo_path", ""),
            passport_scan_path=form_data.get("passport_scan_path", ""),
            # 其他字段使用默认值
            customer_source=form_data.get("customer_source", "API"),
        )

        logger.info(
            f"成功映射表单数据到VietnamEVisaApplicant对象: {applicant.surname} {applicant.given_name}"
        )
        return applicant

    except Exception as e:
        logger.error(f"映射表单数据到Applicant对象失败: {e}")
        raise ValueError(f"表单数据映射失败: {str(e)}")


def normalize_ocr_result_for_applicant(ocr_result: dict) -> dict:
    """
    将OCR识别结果格式化为表单字段格式

    Args:
        ocr_result: OCR识别的原始结果

    Returns:
        Dict: 格式化后的字段字典
    """
    try:
        # 1. 姓名拆分
        name_raw = ocr_result.get("name", "")
        if "," in name_raw:
            surname, given_name = name_raw.split(",", 1)
            surname = surname.strip()
            given_name = given_name.strip().lstrip("/")
        else:
            surname = name_raw.strip()
            given_name = ""

        # 2. 出生地处理（提取拼音）
        birth_place_raw = ocr_result.get("birth_place_raw", "")
        if birth_place_raw and "/" in birth_place_raw:
            parts = birth_place_raw.split("/")
            place_of_birth = parts[1].strip().upper() if len(parts) > 1 else "GUANGDONG"
        elif birth_place_raw and birth_place_raw.isascii():
            place_of_birth = birth_place_raw.strip().upper()
        else:
            place_of_birth = "GUANGDONG"

        # 3. 国籍标准化处理 - 将三字代码转换为全称
        country_raw = ocr_result.get("country", "")
        if country_raw == "CHN":
            nationality = "China"
            logger.info("国籍代码映射: CHN -> China")
        elif country_raw == "CHINA" or country_raw == "China":
            nationality = "China"
        elif country_raw:  # 其他非空值保持原样
            nationality = country_raw
        else:
            nationality = "China"  # 默认值

        # 4. 标准化日期格式
        dob_raw = ocr_result.get("birth_date", "")
        date_of_issue_raw = ocr_result.get("issue_date", "")
        passport_expiry_raw = ocr_result.get("expiry_date", "")

        # 5. 格式化结果
        normalized_result = {
            "chinese_name": ocr_result.get("name_cn", "")
            or ocr_result.get("name_cn_raw", ""),
            "surname": surname,
            "given_name": given_name,
            "sex": ocr_result.get("sex", ""),
            "nationality": nationality,
            "dob": normalize_date_format(dob_raw),
            "passport_number": ocr_result.get("passport_no", ""),
            "place_of_birth": place_of_birth,
            "place_of_issue": ocr_result.get("issue_place", ""),
            "date_of_issue": normalize_date_format(date_of_issue_raw),
            "passport_expiry": normalize_date_format(passport_expiry_raw),
            "passport_type": "Ordinary passport",  # 固定值
        }

        logger.info(
            f"成功格式化OCR结果: {normalized_result.get('surname')} {normalized_result.get('given_name')}, 国籍: {nationality}"
        )
        return normalized_result

    except Exception as e:
        logger.error(f"格式化OCR结果失败: {e}")
        return {}
