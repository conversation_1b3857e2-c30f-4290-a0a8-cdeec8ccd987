# models package marker

# 导入所有模型，便于统一访问
from .applicant import Applicant
from .application import Application
from .automation_logs import AutomationLogs
from .file import File

# VisaType已删除-
from .order import Order
from .user import User
from .user_payment import UserPayment
from .visa_payment import VisaPayment
from .visa_status_history import VisaStatusHistory

__all__ = [
    "User",
    "Applicant",
    "Order",
    "Application",
    "File",
    "UserPayment",
    "VisaPayment",
    "VisaStatusHistory",
    "AutomationLogs",
]
