import uuid

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Column, Date, DateTime, ForeignKey, Index, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import validates
from sqlalchemy.sql import func

from ..base import Base


class Application(Base):
    __tablename__ = "application"
    id = Column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, doc="申请主键"
    )
    # 🔐 架构修复：添加user_id以简化权限控制
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("user.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="用户外键（权限控制）",
    )
    order_id = Column(
        UUID(as_uuid=True),
        ForeignKey("order.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="订单外键",
    )
    applicant_id = Column(
        UUID(as_uuid=True),
        ForeignKey("applicant.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="申请人外键",
    )

    # 签证配置字段（替代visa_type表）
    country = Column(String(3), doc="国家代码 (ISO 3166-1 alpha-3)")
    category = Column(
        String(32), doc="签证类别 (tourist, business, working, visit_family)"
    )

    # 申请具体信息
    visa_entry_type = Column(String(32), doc="签证入境类型")
    visa_validity_duration = Column(String(32), doc="签证有效期")
    visa_start_date = Column(Date, doc="签证生效日期")
    intended_entry_gate = Column(String(128), doc="入境口岸")
    purpose_of_entry = Column(String(128), doc="入境目的")

    # 既往越南访问记录
    visited_vietnam_last_year = Column(
        Boolean, default=False, nullable=False, doc="去年是否访问越南"
    )
    previous_entry_date = Column(Date, doc="上次入境日期")
    previous_exit_date = Column(Date, doc="上次出境日期")
    previous_purpose = Column(String(128), doc="上次访问目的")

    # 越南联系人信息
    has_vietnam_contact = Column(
        Boolean, default=False, nullable=False, doc="是否有越南联系人"
    )
    vietnam_contact_organization = Column(String(128), doc="越南联系人组织")
    vietnam_contact_phone = Column(String(32), doc="越南联系人电话")
    vietnam_contact_address = Column(String(256), doc="越南联系人地址")
    vietnam_contact_purpose = Column(String(128), doc="越南联系目的")

    # 系统字段
    form_snapshot = Column(JSON, nullable=False, doc="表单快照")
    vietnam_application_number = Column(
        String(64), nullable=True, index=True, doc="越南官方编号"
    )
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.timezone("Asia/Shanghai", func.now()),
        doc="创建时间",
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.timezone("Asia/Shanghai", func.now()),
        onupdate=func.timezone("Asia/Shanghai", func.now()),
        doc="更新时间",
    )

    __table_args__ = (
        # 🔐 权限控制索引
        Index("ix_app_user_country", "user_id", "country"),
        Index("ix_app_user_created", "user_id", "created_at"),
        # 原有业务索引
        Index("ix_app_order_country", "order_id", "country"),
        Index("ix_app_created_country", "created_at", "country"),
        Index("ix_app_vietnam_number", "vietnam_application_number"),
        Index("ix_app_visa_dates", "visa_start_date", "visa_validity_duration"),
        Index("ix_app_entry_info", "visa_entry_type", "intended_entry_gate"),
    )

    @validates("country")
    def validate_country(self, key, country):
        if country:
            # ISO 3166-1 alpha-3 country codes
            valid_countries = ["VNM", "CHN", "USA", "GBR"]  # 可扩展
            if country not in valid_countries:
                raise ValueError(f"Invalid country code: {country}")
        return country

    @validates("category")
    def validate_category(self, key, category):
        if category:
            valid_categories = [
                "tourist",
                "business",
                "working",
                "visit_family",
                "transit",
            ]
            if category not in valid_categories:
                raise ValueError(f"Invalid visa category: {category}")
        return category

    @validates("visa_entry_type")
    def validate_visa_entry_type(self, key, entry_type):
        if entry_type:
            valid_types = ["Single-entry", "Multiple-entry"]
            if entry_type not in valid_types:
                raise ValueError(f"Invalid visa entry type: {entry_type}")
        return entry_type
