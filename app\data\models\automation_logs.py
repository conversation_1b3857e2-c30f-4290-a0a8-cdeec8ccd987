import uuid

from sqlalchemy import Column, DateTime, ForeignKey, Index, Integer, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func

from ..base import Base


class AutomationLogs(Base):
    __tablename__ = "automation_logs"

    # 🔑 主键和关联
    id = Column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, doc="日志主键"
    )
    application_id = Column(
        UUID(as_uuid=True),
        ForeignKey("application.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="关联申请",
    )
    order_id = Column(
        UUID(as_uuid=True),
        ForeignKey("order.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="关联订单",
    )

    # 🎯 任务信息
    task_type = Column(
        String(32),
        nullable=False,
        index=True,
        doc="任务类型: vietnam_evisa, passport_ocr",
    )
    task_status = Column(
        String(16),
        nullable=False,
        index=True,
        doc="任务状态: processing, success, failed, cancelled",
    )

    # 🔧 Celery集成
    celery_task_id = Column(String(64), nullable=True, doc="Celery任务ID")

    # 📊 执行结果和重试
    error_message = Column(Text, nullable=True, doc="错误详细信息")
    retry_count = Column(Integer, default=0, doc="重试次数")
    max_retries = Column(Integer, default=3, doc="最大重试次数")

    # ⏰ 时间戳
    started_at = Column(DateTime(timezone=True), nullable=True, doc="任务开始时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, doc="任务完成时间")
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.timezone("Asia/Shanghai", func.now()),
        doc="记录创建时间",
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.timezone("Asia/Shanghai", func.now()),
        onupdate=func.timezone("Asia/Shanghai", func.now()),
        doc="记录更新时间",
    )

    # 📊 索引优化
    __table_args__ = (
        Index("ix_automation_logs_app_task", "application_id", "task_type"),
        Index("ix_automation_logs_task_status", "task_type", "task_status"),
    )
