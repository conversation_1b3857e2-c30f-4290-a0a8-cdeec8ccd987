import uuid

from sqlalchemy import Column, DateTime, ForeignKey, Index, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func

from ..base import Base


class Order(Base):
    __tablename__ = "order"
    id = Column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, doc="订单主键"
    )
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("user.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="所属用户",
    )
    order_no = Column(String(32), unique=True, nullable=False, doc="订单号")

    # 📋 订单管理字段（匹配迁移约束：created, cancelled）
    order_status = Column(
        String(32),
        nullable=False,
        server_default="created",
        doc="订单状态: created, cancelled",
    )
    order_type = Column(
        String(32), nullable=False, server_default="visa_application", doc="订单类型"
    )

    # 🔧 技术控制字段
    idempotent_key = Column(String(100), doc="幂等性键，防止重复创建订单")
    notes = Column(Text, doc="订单备注信息")

    # ⏰ 时间戳 - 🔧 修复：使用timezone('Asia/Shanghai', now())确保时区一致性
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.timezone("Asia/Shanghai", func.now()),
        doc="创建时间",
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.timezone("Asia/Shanghai", func.now()),
        onupdate=func.timezone("Asia/Shanghai", func.now()),
        doc="更新时间",
    )

    # 📊 索引优化（匹配迁移）
    __table_args__ = (Index("ix_order_user_status", "user_id", "order_status"),)
