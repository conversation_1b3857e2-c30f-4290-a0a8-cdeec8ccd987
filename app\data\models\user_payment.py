import uuid

from sqlalchemy import Column, DateTime, Float, ForeignKey, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func

from ..base import Base


class UserPayment(Base):
    __tablename__ = "user_payment"
    id = Column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, doc="用户支付主键"
    )
    order_id = Column(
        UUID(as_uuid=True),
        ForeignKey("order.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="订单外键",
    )
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("user.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="用户外键",
    )
    amount = Column(Float, nullable=False, doc="支付金额")
    currency = Column(String(16), default="CNY", doc="币种")
    status = Column(String(32), nullable=False, doc="支付状态")
    payment_channel = Column(String(32), doc="支付渠道")
    paid_at = Column(DateTime(timezone=True), doc="支付时间")
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.timezone("Asia/Shanghai", func.now()),
        doc="创建时间",
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.timezone("Asia/Shanghai", func.now()),
        onupdate=func.timezone("Asia/Shanghai", func.now()),
        doc="更新时间",
    )
