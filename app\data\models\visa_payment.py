import uuid

from sqlalchemy import Column, DateTime, Float, ForeignKey, Index, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func

from ..base import Base


class VisaPayment(Base):
    __tablename__ = "visa_payment"
    id = Column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, doc="平台代付主键"
    )
    # 🔐 架构修复：添加user_id和order_id以简化权限控制
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("user.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="用户外键（权限控制）",
    )
    order_id = Column(
        UUID(as_uuid=True),
        ForeignKey("order.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="订单外键（业务关联）",
    )
    application_id = Column(
        UUID(as_uuid=True),
        ForeignKey("application.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="申请外键",
    )
    amount = Column(Float, nullable=False, doc="支付金额")
    currency = Column(String(16), default="USD", doc="币种")
    status = Column(String(32), nullable=False, doc="支付状态")
    payment_channel = Column(String(32), doc="支付渠道")
    paid_at = Column(DateTime(timezone=True), doc="支付时间")
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.timezone("Asia/Shanghai", func.now()),
        doc="创建时间",
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.timezone("Asia/Shanghai", func.now()),
        onupdate=func.timezone("Asia/Shanghai", func.now()),
        doc="更新时间",
    )

    # 🔐 权限控制和性能优化索引
    __table_args__ = (
        Index("ix_visa_payment_user_status", "user_id", "status"),
        Index("ix_visa_payment_user_created", "user_id", "created_at"),
        Index("ix_visa_payment_order_app", "order_id", "application_id"),
        Index("ix_visa_payment_app_status", "application_id", "status"),
    )
