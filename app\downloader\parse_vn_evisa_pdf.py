from datetime import datetime
from pathlib import Path
import re
from typing import Any

from pypdf import PdfReader

from app.utils.logger_config import get_logger

logger = get_logger()


def guess_visa_days(valid_from: str, valid_until: str) -> int | None:
    fmt = "%d/%m/%Y"
    try:
        d1 = datetime.strptime(valid_from, fmt)
        d2 = datetime.strptime(valid_until, fmt)
        days = (d2 - d1).days + 1
        logger.debug(f"签证有效期计算: {valid_from} ~ {valid_until} -> {days}天")
        if 25 < days < 40:
            return 30
        elif 80 < days < 100:
            return 90
        else:
            return days
    except Exception as e:
        logger.error(f"解析有效期天数失败: {e}")
        return None


def extract_vn_visa_info(pdf_path: str) -> dict[str, Any]:
    pdf_path_obj = Path(pdf_path)
    logger.info(f"开始解析PDF文件: {pdf_path_obj}")
    reader = PdfReader(str(pdf_path_obj))
    text = ""
    for i, page in enumerate(reader.pages):
        page_text = page.extract_text() or ""
        logger.debug(f"Page {i + 1} text: {page_text[:200]}...")  # 只打印前200字符
        text += page_text

    data = {}

    # 中文姓名
    match = re.search(r"HỌ TÊN:\s*(.+)", text)
    data["full name"] = match.group(1).strip() if match else None
    logger.debug(f"解析姓名: {data['full name']}")

    # 护照号
    match = re.search(r"SỐ HỘ CHIẾU:\s*([A-Z0-9]+)", text)
    data["passport_number"] = match.group(1).strip() if match else None
    logger.debug(f"解析护照号: {data['passport_number']}")

    # 生效日期
    match = re.search(r"THỊ THỰC CÓ GIÁ TRỊ TỪ NGÀY (\d{2}/\d{2}/\d{4})", text)
    data["visa_start_date"] = match.group(1) if match else None
    logger.debug(f"解析签证生效日期: {data['visa_start_date']}")

    # 截止日期
    match = re.search(r"ĐẾN NGÀY (\d{2}/\d{2}/\d{4})", text)
    data["visa_end_date"] = match.group(1) if match else None
    logger.debug(f"解析签证截止日期: {data['visa_end_date']}")

    # 签证类型（多次/单次）
    if "MULTIPLE" in text.upper():
        data["visa_type"] = "多次"
    elif "SINGLE" in text.upper():
        data["visa_type"] = "单次"
    else:
        data["visa_type"] = "未知"
    logger.debug(f"解析签证类型: {data['visa_type']}")

    # 有效期天数
    start_date = data.get("visa_start_date")
    end_date = data.get("visa_end_date")
    if start_date and end_date:
        data["visa_validity_days"] = guess_visa_days(start_date, end_date)
    else:
        data["visa_validity_days"] = None
    logger.debug(f"解析签证有效期天数: {data['visa_validity_days']}")

    return data
