"""
统一邮件解析器
==============

整合所有邮件解析功能，消除重复代码，提供统一的邮件解析接口

功能：
- 邮件类型识别
- 提交确认邮件解析
- 付款确认邮件解析
- 出签结果邮件解析
- 统一的错误处理和重试机制
"""

from dataclasses import dataclass
from datetime import datetime
import email.header
from enum import Enum
import re
from typing import Any

from bs4 import BeautifulSoup

from app.utils.logger_config import get_logger
from app.utils.retry_strategy import email_parsing_retry

logger = get_logger()


class EmailType(Enum):
    """邮件类型枚举"""

    SUBMISSION = "submission"
    PAYMENT = "payment"
    RESULT = "result"
    UNKNOWN = "unknown"


@dataclass
class ParsedEmailData:
    """解析后的邮件数据"""

    email_type: EmailType
    data: dict[str, Any] | None = None
    success: bool = False
    error: str | None = None


class UnifiedEmailParser:
    """统一邮件解析器"""

    def __init__(self):
        self.logger = logger

    def parse_email(
        self, sender: str, subject: str, body: str, raw_email=None
    ) -> ParsedEmailData:
        """
        解析邮件并返回结构化数据

        参数:
            sender: 发件人
            subject: 邮件主题
            body: 邮件正文
            raw_email: 原始邮件对象（可选）

        返回:
            ParsedEmailData: 解析结果
        """
        try:
            # 确定邮件类型
            email_type = self._determine_email_type(sender, subject, body)

            # 根据类型解析邮件
            if email_type == EmailType.SUBMISSION:
                data = self._parse_submission_email(body)
            elif email_type == EmailType.PAYMENT:
                data = self._parse_payment_email(body)
            elif email_type == EmailType.RESULT:
                data = self._parse_result_email(body, raw_email)
            else:
                data = None

            return ParsedEmailData(
                email_type=email_type,
                data=data,
                success=data is not None,
                error=None if data else f"无法解析{email_type.value}邮件",
            )

        except Exception as e:
            self.logger.error(f"❌ 邮件解析失败: {e}", exc_info=True)
            return ParsedEmailData(
                email_type=EmailType.UNKNOWN, success=False, error=str(e)
            )

    def _determine_email_type(self, sender: str, subject: str, body: str) -> EmailType:
        """确定邮件类型"""
        try:
            # 解码MIME编码的主题
            if "=?" in subject and "?=" in subject:
                decoded_subject, _ = email.header.decode_header(subject)[0]
                if isinstance(decoded_subject, bytes):
                    subject = decoded_subject.decode("utf-8", errors="replace")
        except Exception as e:
            self.logger.warning(f"解码邮件主题失败: {e}")

        # 转换为小写以忽略大小写
        sender_lower = sender.lower()
        subject_lower = subject.lower()
        body_lower = body.lower()

        # 提取邮件主题的前100个字符用于日志记录
        subject_preview = subject[:100] + "..." if len(subject) > 100 else subject
        self.logger.info(f"处理邮件: 发件人={sender}, 主题={subject_preview}")

        # 1. 付款成功通知邮件
        if (
            "<EMAIL>" in sender_lower
            and "payment confirmation" in subject_lower
        ):
            self.logger.info("📨 ✅识别为: 付款成功通知邮件")
            return EmailType.PAYMENT

        # 2. 来自越南移民局的邮件
        if "immigration.gov.vn" in sender_lower:
            # 出签状态通知邮件
            if "has been processed" in body_lower:
                self.logger.info("📨 ✅识别为: 出签状态通知邮件")
                return EmailType.RESULT

            # 提交成功通知邮件
            if (
                "we have received your e-visa application" in body_lower
                or "proceed with the e-visa fee payment" in body_lower
            ):
                self.logger.info("📨 ✅识别为: 提交成功通知邮件")
                return EmailType.SUBMISSION

        # 无法确定类型
        self.logger.warning(
            f"⚠️ 无法确定邮件类型: 发件人={sender}, 主题={subject_preview}"
        )
        return EmailType.UNKNOWN

    @email_parsing_retry
    def _parse_submission_email(self, content: str) -> dict[str, Any] | None:
        """解析提交确认邮件"""
        self.logger.debug("开始解析提交确认邮件")

        try:
            # 使用BeautifulSoup提取纯文本
            soup = BeautifulSoup(content, "html.parser")
            text = soup.get_text(separator=" ", strip=True)

            self.logger.info("📨 ✅成功识别提取确认提交邮件")

            # 提取申请编号
            app_number_patterns = [
                r"e-visa application number\s*([A-Z0-9]+)",
                r"application number\s*([A-Z0-9]+)",
                r"reference number\s*([A-Z0-9]+)",
            ]

            application_number = None
            for pattern in app_number_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    application_number = match.group(1)
                    break

            # 提取姓名
            name_patterns = [
                r"dear\s+([A-Z\s]+),",
                r"applicant name\s*[:\-]\s*([A-Z\s]+)",
                r"full name\s*[:\-]\s*([A-Z\s]+)",
            ]

            full_name = None
            for pattern in name_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    full_name = match.group(1).strip()
                    break

            # 提取出生日期
            dob_patterns = [
                r"date of birth\s*[:\-]\s*(\d{2}/\d{2}/\d{4})",
                r"birth date\s*[:\-]\s*(\d{2}/\d{2}/\d{4})",
                r"dob\s*[:\-]\s*(\d{2}/\d{2}/\d{4})",
            ]

            dob = None
            for pattern in dob_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    dob = match.group(1)
                    break

            if application_number and full_name and dob:
                result = {
                    "application_number": application_number,
                    "full_name": full_name.upper().strip(),
                    "dob": dob,
                }

                self.logger.info(f"✅ 提交确认邮件解析成功: {result}")
                return result
            else:
                self.logger.warning("⚠️ 提交确认邮件缺少必要信息")
                return None

        except Exception as e:
            self.logger.error(f"❌ 解析提交确认邮件失败: {e}")
            return None

    @email_parsing_retry
    def _parse_payment_email(self, content: str) -> dict[str, Any] | None:
        """解析付款确认邮件"""
        self.logger.debug("开始解析付款确认邮件")

        try:
            # 使用BeautifulSoup提取纯文本
            soup = BeautifulSoup(content, "html.parser")
            text = soup.get_text(separator=" ", strip=True)

            self.logger.info("💰 ✅成功识别付款确认邮件")

            # 提取申请编号
            app_number_match = re.search(r"([A-Z0-9]{15,25})", text)
            application_number = app_number_match.group(1) if app_number_match else None

            # 提取付款金额
            amount_patterns = [
                r"amount\s*[:\-]\s*(\d+(?:\.\d{2})?)\s*USD",
                r"(\d+(?:\.\d{2})?)\s*USD",
                r"total\s*[:\-]\s*(\d+(?:\.\d{2})?)",
            ]

            payment_amount = None
            for pattern in amount_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    payment_amount = f"{match.group(1)} USD"
                    break

            # 提取付款时间（转换为上海时间）
            time_patterns = [
                r"(\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2}:\d{2})",
                r"(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})",
            ]

            payment_time = None
            for pattern in time_patterns:
                match = re.search(pattern, text)
                if match:
                    try:
                        # 尝试解析时间并转换为上海时间
                        time_str = match.group(1)
                        if "/" in time_str:
                            dt = datetime.strptime(time_str, "%d/%m/%Y %H:%M:%S")
                        else:
                            dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")

                        # 假设原时间为UTC，转换为上海时间
                        from zoneinfo import ZoneInfo

                        shanghai_tz = ZoneInfo("Asia/Shanghai")
                        dt_shanghai = dt.replace(tzinfo=ZoneInfo("UTC")).astimezone(
                            shanghai_tz
                        )
                        payment_time = dt_shanghai.strftime("%Y-%m-%d %H:%M:%S")
                        break
                    except (ValueError, TypeError, AttributeError):
                        # 时间格式解析失败，尝试下一个模式
                        continue

            if application_number:
                result = {
                    "application_number": application_number,
                    "payment_amount": payment_amount or "unknown",
                    "payment_time": payment_time
                    or datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                }

                self.logger.info(f"✅ 付款确认邮件解析成功: {result}")
                return result
            else:
                self.logger.warning("⚠️ 付款确认邮件缺少申请编号")
                return None

        except Exception as e:
            self.logger.error(f"❌ 解析付款确认邮件失败: {e}")
            return None

    @email_parsing_retry
    def _parse_result_email(
        self, content: str, raw_email=None
    ) -> dict[str, Any] | None:
        """解析出签结果邮件"""
        self.logger.debug("开始解析出签结果邮件")

        try:
            # 使用BeautifulSoup提取纯文本
            soup = BeautifulSoup(content, "html.parser")
            text = soup.get_text(separator=" ", strip=True)

            self.logger.info("📨 ✅成功识别出签通知邮件")

            # 提取申请编号
            app_number_match = re.search(
                r"e-visa application number\s*([A-Z0-9]+)", text, re.IGNORECASE
            )
            application_number = app_number_match.group(1) if app_number_match else None

            # 提取下载链接（从原始HTML中查找）
            download_url_match = re.search(
                r"https://evisa\.gov\.vn/e-visa/search\?[^\"'\s]+", content
            )
            download_url = download_url_match.group(0) if download_url_match else None

            # 如果从原始邮件对象中提取信息
            if raw_email and not (application_number and download_url):
                app_number_alt, download_url_alt = self._extract_from_raw_email(
                    raw_email
                )
                application_number = application_number or app_number_alt
                download_url = download_url or download_url_alt

            if application_number and download_url:
                result = {
                    "application_number": application_number,
                    "download_url": download_url,
                }

                self.logger.info(f"✅ 出签结果邮件解析成功: {result}")
                return result
            else:
                self.logger.warning("⚠️ 出签结果邮件缺少必要信息")
                return None

        except Exception as e:
            self.logger.error(f"❌ 解析出签结果邮件失败: {e}")
            return None

    def _extract_from_raw_email(self, raw_email) -> tuple[str | None, str | None]:
        """从原始邮件对象中提取信息"""
        try:
            content = None
            for part in raw_email.walk():
                if part.get_content_type() == "text/html":
                    content = part.get_payload(decode=True).decode(errors="ignore")
                    break

            if not content:
                return None, None

            soup = BeautifulSoup(content, "html.parser")

            # 提取跳转链接
            link_tag = soup.find("a", href=re.compile(r"evisa\.gov\.vn/.+search", re.I))
            download_url = link_tag["href"] if link_tag else None  # type: ignore[index]

            # 提取编号
            match = re.search(r"[A-Z0-9]{20,}", soup.get_text())
            application_number = match.group(0) if match else None

            return application_number, download_url

        except Exception as e:
            self.logger.error(f"❌ 从原始邮件提取信息失败: {e}")
            return None, None
