"""
统一邮件处理器
==============

处理解析后的邮件数据，与FastAPI后端进行通信，触发相应的业务逻辑

功能：
- 提交确认邮件处理
- 付款确认邮件处理
- 出签结果邮件处理（包括PDF下载）
- 统一的API通信接口
- 错误处理和重试机制
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Any

import requests

from app.downloader.vietnam_evisa_downloader import download_pdf_for_application
from app.email.unified_email_parser import EmailType, ParsedEmailData
from app.utils.logger_config import get_logger

logger = get_logger()


@dataclass
class ProcessingResult:
    """邮件处理结果"""

    success: bool
    message: str
    data: dict[str, Any] | None = None
    error: str | None = None


class UnifiedEmailProcessor:
    """统一邮件处理器"""

    def __init__(self, fastapi_base_url: str = "http://localhost:8000"):
        self.fastapi_base_url = fastapi_base_url
        self.logger = logger

    def process_parsed_email(
        self, parsed_data: ParsedEmailData, email_metadata: dict[str, Any]
    ) -> ProcessingResult:
        """
        处理解析后的邮件数据

        参数:
            parsed_data: 解析后的邮件数据
            email_metadata: 邮件元数据（发件人、主题、接收时间等）

        返回:
            ProcessingResult: 处理结果
        """
        if not parsed_data.success:
            return ProcessingResult(
                success=False, message="邮件解析失败", error=parsed_data.error
            )

        try:
            if parsed_data.email_type == EmailType.SUBMISSION:
                return self._process_submission_email(
                    parsed_data.data or {}, email_metadata
                )  # type: ignore[arg-type]
            elif parsed_data.email_type == EmailType.PAYMENT:
                return self._process_payment_email(
                    parsed_data.data or {}, email_metadata
                )  # type: ignore[arg-type]
            elif parsed_data.email_type == EmailType.RESULT:
                return self._process_result_email(
                    parsed_data.data or {}, email_metadata
                )  # type: ignore[arg-type]
            else:
                return ProcessingResult(
                    success=False, message=f"不支持的邮件类型: {parsed_data.email_type}"
                )

        except Exception as e:
            self.logger.error(f"❌ 邮件处理失败: {e}", exc_info=True)
            return ProcessingResult(success=False, message="邮件处理异常", error=str(e))

    def _process_submission_email(
        self, data: dict[str, Any], metadata: dict[str, Any]
    ) -> ProcessingResult:
        """处理提交确认邮件"""
        try:
            self.logger.info(f"📨 处理提交确认邮件: {data['application_number']}")

            payload = {
                "full_name": data["full_name"],
                "date_of_birth": data["dob"],
                "application_number": data["application_number"],
                "email_from": metadata.get("sender", ""),
                "email_subject": metadata.get("subject", ""),
                "received_time": datetime.now().isoformat(),
            }

            response = requests.post(
                f"{self.fastapi_base_url}/api/email-processing/submission-confirmation",
                json=payload,
                timeout=10,
            )

            if response.status_code == 200:
                result = response.json()
                self.logger.info(
                    f"✅ 提交确认邮件处理成功: {result.get('application_id', 'unknown')}"
                )
                return ProcessingResult(
                    success=True, message="提交确认邮件处理成功", data=result
                )
            else:
                self.logger.warning(
                    f"⚠️ 提交确认邮件处理失败: {response.status_code} - {response.text}"
                )
                return ProcessingResult(
                    success=False,
                    message=f"API调用失败: {response.status_code}",
                    error=response.text,
                )

        except Exception as e:
            self.logger.error(f"❌ 处理提交确认邮件异常: {e}")
            return ProcessingResult(
                success=False, message="处理提交确认邮件异常", error=str(e)
            )

    def _process_payment_email(
        self, data: dict[str, Any], metadata: dict[str, Any]
    ) -> ProcessingResult:
        """处理付款确认邮件"""
        try:
            self.logger.info(f"💰 处理付款确认邮件: {data['application_number']}")

            payload = {
                "application_number": data["application_number"],
                "payment_amount": data.get("payment_amount", "unknown"),
                "payment_time": data.get("payment_time", datetime.now().isoformat()),
                "email_from": metadata.get("sender", ""),
                "email_subject": metadata.get("subject", ""),
                "received_time": datetime.now().isoformat(),
            }

            response = requests.post(
                f"{self.fastapi_base_url}/api/email-processing/payment-confirmation",
                json=payload,
                timeout=10,
            )

            if response.status_code == 200:
                result = response.json()
                self.logger.info(
                    f"✅ 付款确认邮件处理成功: {result.get('application_id', 'unknown')}"
                )
                return ProcessingResult(
                    success=True, message="付款确认邮件处理成功", data=result
                )
            else:
                self.logger.warning(
                    f"⚠️ 付款确认邮件处理失败: {response.status_code} - {response.text}"
                )
                return ProcessingResult(
                    success=False,
                    message=f"API调用失败: {response.status_code}",
                    error=response.text,
                )

        except Exception as e:
            self.logger.error(f"❌ 处理付款确认邮件异常: {e}")
            return ProcessingResult(
                success=False, message="处理付款确认邮件异常", error=str(e)
            )

    def _process_result_email(
        self, data: dict[str, Any], metadata: dict[str, Any]
    ) -> ProcessingResult:
        """处理出签结果邮件"""
        try:
            application_number = data["application_number"]
            download_url = data["download_url"]

            self.logger.info(f"🎉 处理出签结果邮件: {application_number}")

            # 执行PDF下载
            pdf_save_path = None
            try:
                self.logger.info(f"📥 开始下载签证PDF: {application_number}")
                pdf_save_path = download_pdf_for_application(
                    application_number, download_url
                )

                if pdf_save_path:
                    self.logger.info(f"✅ 签证PDF下载成功: {pdf_save_path}")
                else:
                    self.logger.warning(f"⚠️ 签证PDF下载失败: {application_number}")

            except Exception as e:
                self.logger.error(f"❌ 签证PDF下载异常: {e}")
                # 继续处理，不因为下载失败而中断邮件处理

            # 通知FastAPI更新数据库
            payload = {
                "application_number": application_number,
                "download_url": download_url,
                "pdf_save_path": pdf_save_path,
                "email_from": metadata.get("sender", ""),
                "email_subject": metadata.get("subject", ""),
                "received_time": datetime.now().isoformat(),
            }

            response = requests.post(
                f"{self.fastapi_base_url}/api/email-processing/result-notification",
                json=payload,
                timeout=10,
            )

            if response.status_code == 200:
                result = response.json()
                self.logger.info(f"✅ 出签结果邮件处理成功: {application_number}")
                return ProcessingResult(
                    success=True,
                    message="出签结果邮件处理成功",
                    data={
                        **result,
                        "pdf_downloaded": pdf_save_path is not None,
                        "pdf_path": pdf_save_path,
                    },
                )
            else:
                self.logger.warning(
                    f"⚠️ 出签结果邮件处理失败: {response.status_code} - {response.text}"
                )
                return ProcessingResult(
                    success=False,
                    message=f"API调用失败: {response.status_code}",
                    error=response.text,
                )

        except Exception as e:
            self.logger.error(f"❌ 处理出签结果邮件异常: {e}")
            return ProcessingResult(
                success=False, message="处理出签结果邮件异常", error=str(e)
            )

    def health_check(self) -> dict[str, Any]:
        """健康检查"""
        try:
            # 测试FastAPI连接
            response = requests.get(
                f"{self.fastapi_base_url}/api/email-processing/health", timeout=5
            )
            api_healthy = response.status_code == 200

            return {
                "service": "unified_email_processor",
                "status": "healthy" if api_healthy else "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "fastapi_connection": "healthy" if api_healthy else "failed",
                "fastapi_url": self.fastapi_base_url,
            }

        except Exception as e:
            return {
                "service": "unified_email_processor",
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "fastapi_url": self.fastapi_base_url,
            }
