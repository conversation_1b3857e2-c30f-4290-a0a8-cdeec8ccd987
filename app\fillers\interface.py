from abc import ABC, abstractmethod

from playwright.sync_api import Page

from app.data.model import VietnamEVisaApplicant


# 定义一个“签证填充器”应该具备哪些基本功能的“模板”（接口）
class VisaFillerInterface(ABC):
    @abstractmethod
    def prepare(self, locators: dict, settings: dict):
        """加载此填充器所需的定位器和设置"""
        pass

    @abstractmethod
    def fill_step1_personal_info(self, page: Page, applicant: VietnamEVisaApplicant):
        """填充个人信息表单的主要逻辑"""
        pass

    # 未来可以添加更多步骤或功能，例如：
    # @abstractmethod
    # def upload_photos(self, page: Page, applicant: Applicant):
    #     pass
    #
    # @abstractmethod
    # def fill_travel_info(self, page: Page, applicant: Applicant):
    #     pass
    #
    # @abstractmethod
    # def submit_form(self, page: Page):
    #     pass
