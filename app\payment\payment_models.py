"""
Payment Data Models
------------------
包含支付处理所需的数据结构、数据类和常量定义

此模块允许在整个项目中一致地使用信用卡信息结构
"""

from dataclasses import dataclass, field
from typing import Any

from app.utils.env_loader import load_credit_cards_from_env
from app.utils.logger_config import get_logger

logger = get_logger()


@dataclass
class CreditCardInfo:
    """
    信用卡信息 - 严格匹配payment_cards.json的格式和字段顺序
    """

    card_type: str
    card_number: str
    first_name: str
    last_name: str
    billing_address: str
    city: str
    country: str
    exp_month: str
    exp_year: str
    cvv: str
    note: str = ""  # 添加note字段，默认为空字符串


@dataclass
class PaymentSettings:
    """
    支付处理相关的设置
    """

    # 必选字段
    credit_cards: list[CreditCardInfo] = field(default_factory=list)

    # 可选设置
    timeout_ms: int = 30000  # 支付操作超时时间(毫秒)
    screenshots_dir: str = "payment_screenshots"  # 截图保存目录
    retry_count: int = 2  # 支付失败重试次数
    prefer_browser: str = "edge"  # 首选浏览器(edge/chrome/firefox)
    headless: bool = False  # 是否使用无头模式

    # 自定义设置
    custom_settings: dict[str, Any] = field(default_factory=dict)


def get_card_last_digits(card: CreditCardInfo) -> str:
    """
    获取卡号后四位，用于日志记录和显示

    Args:
        card: 信用卡信息

    Returns:
        str: 卡号后四位，若卡号无效则返回"????"
    """
    if not card.card_number or len(card.card_number) < 4:
        return "????"
    return card.card_number[-4:]


def load_credit_cards() -> list[CreditCardInfo]:
    """
    从环境变量加载信用卡信息

    Returns:
        List[CreditCardInfo]: 信用卡信息列表
    """
    cards_data = load_credit_cards_from_env()
    credit_cards = []

    for card_data in cards_data:
        card = CreditCardInfo(
            card_type=card_data["card_type"],
            card_number=card_data["card_number"],
            first_name=card_data["first_name"],
            last_name=card_data["last_name"],
            billing_address=card_data["billing_address"],
            city=card_data["city"],
            country=card_data["country"],
            exp_month=card_data["exp_month"],
            exp_year=card_data["exp_year"],
            cvv=card_data["cvv"],
            note=card_data.get("note", ""),
        )
        credit_cards.append(card)
        logger.info(f"已加载信用卡: ****{get_card_last_digits(card)}")

    return credit_cards


def create_credit_card(
    card_type: str,
    card_number: str,
    first_name: str,
    last_name: str,
    billing_address: str,
    city: str,
    country: str,
    exp_month: str,
    exp_year: str,
    cvv: str,
    note: str = "",
) -> CreditCardInfo:
    """
    创建信用卡信息对象的便捷函数

    Args:
        card_type: 卡类型
        card_number: 卡号
        first_name: 名
        last_name: 姓
        billing_address: 账单地址
        city: 账单城市
        country: 账单国家
        exp_month: 到期月份
        exp_year: 到期年份
        cvv: 安全码
        note: 备注

    Returns:
        CreditCardInfo: 信用卡信息对象
    """
    return CreditCardInfo(
        card_type=card_type,
        card_number=card_number,
        first_name=first_name,
        last_name=last_name,
        billing_address=billing_address,
        city=city,
        country=country,
        exp_month=exp_month,
        exp_year=exp_year,
        cvv=cvv,
        note=note,
    )
