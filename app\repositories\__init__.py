"""
数据访问层 (Repository Pattern)
===============================

职责：
- 封装对数据库的CRUD操作
- 提供领域对象的持久化接口
- 隔离业务逻辑与数据访问技术
- 支持单元测试的Mock替换

原则：
- 每个聚合根一个Repository
- 返回领域对象，不返回ORM对象
- 使用统一的数据库会话管理
- 支持事务边界控制
"""

from .applicant_repository import ApplicantRepository
from .application_repository import ApplicationRepository
from .automation_logs_repository import AutomationLogsRepository
from .base import BaseRepository, SQLAlchemyRepository
from .order_repository import OrderRepository

__all__ = [
    "BaseRepository",
    "SQLAlchemyRepository",
    "OrderRepository",
    "ApplicationRepository",
    "ApplicantRepository",
    "AutomationLogsRepository",
]
