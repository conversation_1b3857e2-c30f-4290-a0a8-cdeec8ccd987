"""
邮箱轮询和解析的重试策略
专注于处理各种网络和服务器问题，保持代码简洁优雅
"""

from email.errors import MessageParseError
import logging
import ssl
from typing import Any

from imapclient.exceptions import IMAPClientAbortError, IMAPClientError
from playwright.sync_api import TimeoutError as PlaywrightTimeoutError
from tenacity import (
    RetryCallState,
    before_sleep_log,
    retry,
    retry_if_exception_type,
    retry_if_result,
    stop_after_attempt,
    wait_exponential,
)

from app.utils.logger_config import get_logger

logger = get_logger()

# 定义可能的邮件操作和签证下载异常类型
EMAIL_EXCEPTIONS = (
    # 网络相关异常
    ConnectionError,
    TimeoutError,
    ssl.SSLError,
    # IMAP客户端异常
    IMAPClientError,
    IMAPClientAbortError,
    # 邮件解析异常
    MessageParseError,
    # 其他可能的异常
    OSError,  # 文件操作异常
    IOError,  # IO操作异常
    PlaywrightTimeoutError,  # Playwright超时，用于签证下载模块
    PermissionError,  # 文件权限异常，用于签证下载模块
)


# 自定义日志函数，提供更详细的重试信息
def log_email_retry_attempt(retry_state: RetryCallState):
    """记录邮件操作重试的详细信息"""
    if retry_state.outcome and retry_state.outcome.failed:
        exc = retry_state.outcome.exception()
        exc_name = exc.__class__.__name__ if exc else "Unknown"
        exc_msg = str(exc) if exc else "Unknown error"

        # 安全获取最大尝试次数
        max_attempts = "Unknown"
        if (
            retry_state.retry_object
            and hasattr(retry_state.retry_object, "stop")
            and hasattr(retry_state.retry_object.stop, "max_attempt_number")
        ):
            max_attempts = retry_state.retry_object.stop.max_attempt_number

        # 安全获取睡眠时间
        sleep_time: Any = "Unknown"
        if retry_state.next_action and hasattr(retry_state.next_action, "sleep"):
            sleep_time = retry_state.next_action.sleep

        # 根据异常类型提供更具体的信息
        if isinstance(exc, ConnectionError):
            logger.warning(
                f"📬 邮箱连接失败 (尝试 {retry_state.attempt_number}/{max_attempts}): "
                f"{exc_name}: {exc_msg}. 将在 {sleep_time} 秒后重试..."
            )
        elif isinstance(exc, TimeoutError):
            logger.warning(
                f"⏱️ 邮箱操作超时 (尝试 {retry_state.attempt_number}/{max_attempts}): "
                f"操作耗时过长. 将在 {sleep_time} 秒后重试..."
            )
        elif isinstance(exc, ssl.SSLError):
            logger.warning(
                f"🔒 邮箱SSL连接错误 (尝试 {retry_state.attempt_number}/{max_attempts}): "
                f"{exc_msg}. 将在 {sleep_time} 秒后重试..."
            )
        elif isinstance(exc, IMAPClientError | IMAPClientAbortError):
            logger.warning(
                f"📨 IMAP协议错误 (尝试 {retry_state.attempt_number}/{max_attempts}): "
                f"{exc_msg}. 将在 {sleep_time} 秒后重试..."
            )
        elif isinstance(exc, MessageParseError):
            logger.warning(
                f"📝 邮件解析错误 (尝试 {retry_state.attempt_number}/{max_attempts}): "
                f"{exc_msg}. 将在 {sleep_time} 秒后重试..."
            )
        else:
            logger.warning(
                f"⚠️ 邮箱操作失败 (尝试 {retry_state.attempt_number}/{max_attempts}): "
                f"{exc_name}: {exc_msg}. 将在 {sleep_time} 秒后重试..."
            )
    else:
        # 如果是基于结果的重试
        result = retry_state.outcome.result() if retry_state.outcome else "Unknown"

        # 安全获取最大尝试次数
        max_attempts = "Unknown"
        if (
            retry_state.retry_object
            and hasattr(retry_state.retry_object, "stop")
            and hasattr(retry_state.retry_object.stop, "max_attempt_number")
        ):
            max_attempts = retry_state.retry_object.stop.max_attempt_number

        # 安全获取睡眠时间
        sleep_time = "Unknown"
        if retry_state.next_action and hasattr(retry_state.next_action, "sleep"):
            sleep_time = retry_state.next_action.sleep

        logger.warning(
            f"📬 邮箱操作返回需要重试的结果 (尝试 {retry_state.attempt_number}/{max_attempts}): "
            f"结果: {result}. 将在 {sleep_time} 秒后重试..."
        )


def log_pdf_retry_attempt(retry_state: RetryCallState):
    """记录PDF下载重试的详细信息"""
    if retry_state.outcome and retry_state.outcome.failed:
        exc = retry_state.outcome.exception()
        exc_name = exc.__class__.__name__ if exc else "Unknown"
        exc_msg = str(exc) if exc else "Unknown error"

        # 安全获取最大尝试次数
        max_attempts = "Unknown"
        if (
            retry_state.retry_object
            and hasattr(retry_state.retry_object, "stop")
            and hasattr(retry_state.retry_object.stop, "max_attempt_number")
        ):
            max_attempts = retry_state.retry_object.stop.max_attempt_number

        # 安全获取睡眠时间
        sleep_time: Any = "Unknown"
        if retry_state.next_action and hasattr(retry_state.next_action, "sleep"):
            sleep_time = retry_state.next_action.sleep

        # 根据异常类型提供更具体的信息
        if isinstance(exc, ConnectionError):
            logger.warning(
                f"📬 PDF下载失败 (尝试 {retry_state.attempt_number}/{max_attempts}): "
                f"{exc_name}: {exc_msg}. 将在 {sleep_time} 秒后重试..."
            )
        elif isinstance(exc, TimeoutError):
            logger.warning(
                f"⏱️ PDF下载超时 (尝试 {retry_state.attempt_number}/{max_attempts}): "
                f"操作耗时过长. 将在 {sleep_time} 秒后重试..."
            )
        elif isinstance(exc, ssl.SSLError):
            logger.warning(
                f"🔒 PDF下载SSL连接错误 (尝试 {retry_state.attempt_number}/{max_attempts}): "
                f"{exc_msg}. 将在 {sleep_time} 秒后重试..."
            )
        else:
            logger.warning(
                f"⚠️ PDF下载失败 (尝试 {retry_state.attempt_number}/{max_attempts}): "
                f"{exc_name}: {exc_msg}. 将在 {sleep_time} 秒后重试..."
            )
    else:
        # 如果是基于结果的重试
        result = retry_state.outcome.result() if retry_state.outcome else "Unknown"

        # 安全获取最大尝试次数
        max_attempts = "Unknown"
        if (
            retry_state.retry_object
            and hasattr(retry_state.retry_object, "stop")
            and hasattr(retry_state.retry_object.stop, "max_attempt_number")
        ):
            max_attempts = retry_state.retry_object.stop.max_attempt_number

        # 安全获取睡眠时间
        sleep_time = "Unknown"
        if retry_state.next_action and hasattr(retry_state.next_action, "sleep"):
            sleep_time = retry_state.next_action.sleep

        logger.warning(
            f"📬 PDF下载返回需要重试的结果 (尝试 {retry_state.attempt_number}/{max_attempts}): "
            f"结果: {result}. 将在 {sleep_time} 秒后重试..."
        )

    # logger.warning(f"⚠️ PDF下载失败 (尝试 {attempt}/{max_retries}): {exc.__class__.__name__}: {exc}. 将在 {delay:.1f} 秒后重试...")


# 判断结果是否需要重试的函数
def should_retry_email_result(result: Any) -> bool:
    """
    判断邮件操作结果是否需要重试

    参数:
        result: 函数返回的结果

    返回:
        bool: 如果需要重试返回True，否则返回False
    """
    # 如果结果是False或None，则需要重试
    if result is False or result is None:
        return True

    # 如果结果是字典，检查是否包含错误标志
    return bool(isinstance(result, dict) and result.get("success") is False)


# 邮箱轮询重试装饰器
email_polling_retry = retry(
    # 组合重试条件：异常或返回值为False/None
    retry=(
        retry_if_exception_type(EMAIL_EXCEPTIONS)
        | retry_if_result(should_retry_email_result)
    ),
    # 最多尝试3次（1次初始 + 5次重试）
    stop=stop_after_attempt(6),
    # 指数退避：10秒, 20秒, 40秒...但最多300秒
    wait=wait_exponential(multiplier=1, min=10, max=300),
    # 重试前记录详细日志
    before_sleep=log_email_retry_attempt,
)

# 邮件解析重试装饰器 - 更宽松的策略，因为解析不涉及网络
email_parsing_retry = retry(
    # 主要处理解析异常
    retry=(
        retry_if_exception_type(
            (MessageParseError, ValueError, KeyError, IndexError, AttributeError)
        )
        | retry_if_result(lambda x: x is None)
    ),
    # 最多尝试2次（1次初始 + 1次重试）
    stop=stop_after_attempt(2),
    # 固定等待1秒
    wait=wait_exponential(multiplier=0.5, min=1, max=2),
    # 重试前记录日志
    before_sleep=before_sleep_log(logger, logging.WARNING),
)

# 邮件下载附件重试装饰器
email_download_retry = retry(
    # 主要处理网络和IO异常
    retry=retry_if_exception_type(
        (ConnectionError, TimeoutError, ssl.SSLError, OSError, IOError)
    ),
    # 最多尝试4次（1次初始 + 3次重试）
    stop=stop_after_attempt(3),
    # 指数退避：5秒, 10秒, 20秒...
    wait=wait_exponential(multiplier=1, min=5, max=30),
    # 重试前记录日志
    before_sleep=log_email_retry_attempt,
)


# 电子签pdf文件下载附件重试装饰器
evisa_pdf_download_retry = retry(
    # 主要处理网络和IO异常
    retry=retry_if_exception_type(
        (
            ConnectionError,
            TimeoutError,
            ssl.SSLError,
            OSError,
            IOError,
            PlaywrightTimeoutError,
            PermissionError,
        )
    ),
    # 最多尝试4次（1次初始 + 3次重试）
    stop=stop_after_attempt(4),
    # 指数退避：5秒, 10秒, 20秒...
    wait=wait_exponential(multiplier=1, min=5, max=30),
    # 重试前记录日志
    before_sleep=log_pdf_retry_attempt,
)
