# 文件路径建议：app/utils/security.py
# 安全工具模块 - 用于处理敏感信息的掩码和加密


def mask_sensitive_data(data: str, show_chars: int = 4) -> str:
    """
    对敏感数据进行掩码处理，只显示最后几个字符

    Args:
        data: 需要掩码的敏感数据
        show_chars: 显示的字符数量

    Returns:
        掩码后的字符串
    """
    if not data:
        return ""

    if len(data) <= show_chars:
        return "*" * len(data)

    return "*" * (len(data) - show_chars) + data[-show_chars:]


def mask_card_number(card_number: str) -> str:
    """
    对信用卡号进行掩码处理，格式为: **** **** **** 1234

    Args:
        card_number: 完整的信用卡号

    Returns:
        掩码后的信用卡号
    """
    # 移除所有空格
    card_number = card_number.replace(" ", "")

    if len(card_number) < 4:
        return "*" * len(card_number)

    # 保留最后4位，其余用*替代
    masked = "*" * (len(card_number) - 4) + card_number[-4:]

    # 每4位添加一个空格，提高可读性
    chunks = [masked[i : i + 4] for i in range(0, len(masked), 4)]
    return " ".join(chunks)


def mask_email(email: str) -> str:
    """
    对电子邮件地址进行掩码处理，格式为: u***@example.com

    Args:
        email: 完整的电子邮件地址

    Returns:
        掩码后的电子邮件地址
    """
    if not email or "@" not in email:
        return email

    username, domain = email.split("@", 1)

    if len(username) <= 1:
        masked_username = username
    else:
        masked_username = username[0] + "*" * (len(username) - 1)

    return f"{masked_username}@{domain}"
