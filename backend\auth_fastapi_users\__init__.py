"""
FastAPI Users 认证模块
基于fastapi-users官方库实现现代JWT认证体系
"""

from backend.auth_fastapi_users.auth import (
    auth_backend,
    current_superuser,
    current_user,
    current_user_optional,
    fastapi_users,
)
from backend.auth_fastapi_users.database import close_database, get_user_db
from backend.auth_fastapi_users.dependencies import (
    optional_auth,
    require_admin_auth,
    require_auth,
)
from backend.auth_fastapi_users.manager import get_user_manager
from backend.auth_fastapi_users.models import User
from backend.auth_fastapi_users.routes import router

__all__ = [
    "fastapi_users",
    "auth_backend",
    "current_user",
    "current_superuser",
    "current_user_optional",
    "User",
    "get_user_db",
    "close_database",
    "get_user_manager",
    "require_auth",
    "require_admin_auth",
    "optional_auth",
    "router",
]
