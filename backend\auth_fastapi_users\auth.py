"""
FastAPI Users JWT认证后端配置
严格按照官方文档规范，配置JWT认证策略
"""

import uuid

from fastapi_users import FastAPIUsers
from fastapi_users.authentication import (
    AuthenticationBackend,
    BearerTransport,
    JWTStrategy,
)

from backend.auth_fastapi_users.manager import get_user_manager_unified
from backend.auth_fastapi_users.models import User
from backend.config.settings import settings

# JWT配置
SECRET = settings.secret_key
LIFETIME_SECONDS = 3600  # 1小时，与原系统保持一致


def get_jwt_strategy() -> JWTStrategy:
    """
    获取JWT策略 - fastapi-users官方实现
    不手写JWT生成和验证逻辑
    """
    return JWTStrategy(
        secret=SECRET,
        lifetime_seconds=LIFETIME_SECONDS,
        token_audience=["fastapi-users:auth"],  # 官方推荐audience
        algorithm="HS256",  # 与原系统保持一致
    )


# Bearer Token传输配置
bearer_transport = BearerTransport(tokenUrl="auth/jwt/login")

# JWT认证后端
auth_backend = AuthenticationBackend(
    name="jwt",
    transport=bearer_transport,
    get_strategy=get_jwt_strategy,
)

# FastAPI Users主实例
fastapi_users = FastAPIUsers[User, uuid.UUID](
    get_user_manager_unified,
    [auth_backend],
)

# 认证依赖 - 替换原有的require_auth等
current_user = fastapi_users.current_user(active=True)
current_superuser = fastapi_users.current_user(active=True, superuser=True)

# 可选认证依赖
current_user_optional = fastapi_users.current_user(optional=True)
