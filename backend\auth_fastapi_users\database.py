"""
FastAPI Users 数据库配置
集成现有PostgreSQL连接，严格按照官方文档规范
"""

from collections.abc import AsyncGenerator
from contextlib import asynccontextmanager

from fastapi_users.db import SQLAlchemyUserDatabase
from sqlalchemy.ext.asyncio import AsyncSession

from backend.auth_fastapi_users.models import User
from backend.db_config.unified_connection import get_unified_db


@asynccontextmanager
async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话"""
    unified_db = await get_unified_db()
    async with unified_db.get_session() as session:
        yield session


async def get_user_db() -> AsyncGenerator[SQLAlchemyUserDatabase, None]:
    """
    获取用户数据库实例 - fastapi-users官方依赖
    严格按照官方文档规范实现
    """
    async with get_async_session() as session:
        yield SQLAlchemyUserDatabase(session, User)


async def close_database():
    """关闭数据库连接 统一连接池无需手动关闭"""
    print("📴 FastAPI Users 使用统一连接池，无需手动关闭")
