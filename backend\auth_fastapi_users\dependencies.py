"""
FastAPI Users 认证依赖
替换原有的require_auth等依赖，使用fastapi-users官方实现
"""

from fastapi import Depends, HTTPException

from backend.auth_fastapi_users.auth import (
    current_superuser,
    current_user,
    current_user_optional,
)
from backend.auth_fastapi_users.models import User


def get_current_user(user: User = Depends(current_user)) -> User:
    """
    获取当前用户（current_user的别名）
    """
    return user


def require_auth(user: User = Depends(current_user)) -> User:
    """
    需要认证的依赖 - 替换原有的require_auth
    使用fastapi-users的current_user依赖
    """
    return user


def require_admin_auth(user: User = Depends(current_superuser)) -> User:
    """
    需要管理员认证的依赖 - 替换原有的require_admin_auth
    使用fastapi-users的current_superuser依赖
    """
    return user


def require_active_user(user: User = Depends(current_user)) -> User:
    """
    需要激活用户的依赖
    """
    if not user.is_active:
        raise HTTPException(status_code=403, detail="User account is inactive")
    return user


def require_verified_user(user: User = Depends(current_user)) -> User:
    """
    需要已验证用户的依赖 - 预留扩展
    """
    if not user.is_verified:
        raise HTTPException(status_code=403, detail="User email not verified")
    return user


def optional_auth(user: User = Depends(current_user_optional)) -> User:
    """
    可选认证依赖
    如果提供了valid token则返回用户，否则返回None
    """
    return user


def get_user_permissions(user: User = Depends(current_user)) -> list[str]:
    """
    获取当前用户权限列表
    """
    if user.is_superuser:
        return ["admin"]
    return ["user"]


def check_permission(required_permission: str):
    """
    检查特定权限的依赖装饰器
    """

    def permission_checker(user: User = Depends(current_user)):
        user_permissions = get_user_permissions(user)

        if required_permission not in user_permissions and not user.is_superuser:
            raise HTTPException(
                status_code=403, detail=f"Permission '{required_permission}' required"
            )
        return user

    return permission_checker


def check_role(required_role: str):
    """
    检查特定角色的依赖装饰器 - 预留扩展
    """

    def role_checker(user: User = Depends(current_user)):
        if user.role != required_role and not user.is_superuser:
            raise HTTPException(
                status_code=403, detail=f"Role '{required_role}' required"
            )
        return user

    return role_checker
