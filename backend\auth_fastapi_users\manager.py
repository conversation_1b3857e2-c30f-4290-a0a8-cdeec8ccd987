"""
FastAPI Users 用户管理器
严格按照官方文档规范实现，不手写认证逻辑
"""

import uuid

from fastapi import Depends, Request
from fastapi_users import BaseUserManager, UUIDIDMixin
from fastapi_users.password import PasswordHelper

from backend.auth_fastapi_users.database import get_user_db
from backend.auth_fastapi_users.models import User
from backend.config.settings import settings

# 密钥配置 - 使用现有配置
SECRET = settings.secret_key


class UserManager(UUIDIDMixin, BaseUserManager[User, uuid.UUID]):
    """
    用户管理器 - fastapi-users官方基类
    处理用户认证、注册、验证等逻辑
    """

    reset_password_token_secret = SECRET
    verification_token_secret = SECRET

    def __init__(self, user_db, password_helper: PasswordHelper | None = None):
        super().__init__(user_db, password_helper)

    async def on_after_register(self, user: User, request: Request | None = None):
        """用户注册后回调 - 预留扩展点"""
        print(f"🎉 用户注册成功: {user.email}")

    async def on_after_forgot_password(
        self, user: User, token: str, request: Request | None = None
    ):
        """忘记密码后回调 - 预留扩展点"""
        print(f"📧 用户 {user.email} 请求重置密码: token={token}")

    async def on_after_request_verify(
        self, user: User, token: str, request: Request | None = None
    ):
        """验证邮箱请求后回调 - 预留扩展点"""
        print(f"📨 用户 {user.email} 请求邮箱验证: token={token}")

    async def on_after_verify(self, user: User, request: Request | None = None):
        """邮箱验证成功后回调 - 预留扩展点"""
        print(f"✅ 用户 {user.email} 邮箱验证成功")

    async def authenticate(  # type: ignore[override]
        self,
        credentials: str | None,
        password: str,
        request: Request | None = None,
    ) -> User | None:
        """
        用户认证 - 支持邮箱或用户名登录
        优先使用email，兼容username登录
        """
        if credentials is None:
            return None

        # 首先尝试通过邮箱登录
        user = await self.user_db.get_by_email(credentials)

        # 如果邮箱找不到，尝试通过用户名登录
        if user is None:
            # 🔥 修复：使用user_db的内置查询方法，避免手动管理会话
            try:
                # 直接使用SQLAlchemy查询，但通过user_db的session
                from sqlalchemy import select

                stmt = select(User).where(User.username == credentials)
                result = await self.user_db.session.execute(stmt)  # type: ignore[attr-defined]
                user = result.scalar_one_or_none()
            except Exception as e:
                print(f"❌ 用户名登录查询失败: {e}")
                user = None

        if user is None:
            # 运行密码验证避免时序攻击
            self.password_helper.hash("password")
            return None

        # 验证密码
        verified, updated_password_hash = self.password_helper.verify_and_update(
            password,
            user.hashed_password,  # type: ignore[arg-type]
        )
        if not verified:
            return None

        # 如果密码哈希需要更新（算法升级等）
        if updated_password_hash is not None:
            await self.user_db.update(user, {"hashed_password": updated_password_hash})

        return user


async def get_user_manager(user_db=Depends(get_user_db)):
    """获取用户管理器实例 - fastapi-users官方依赖"""
    yield UserManager(user_db)


# ✅ 新增：统一连接池版本的用户管理器
async def get_user_manager_unified():
    """
    获取用户管理器实例 - 统一连接池版本
    ✅ 渐进式迁移：新功能使用，旧功能保持不变
    """
    # 使用现有的get_user_db，它已经使用了统一连接池
    async for user_db in get_user_db():
        yield UserManager(user_db)
