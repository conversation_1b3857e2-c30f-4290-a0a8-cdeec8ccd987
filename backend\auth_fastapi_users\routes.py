"""
FastAPI Users 兼容路由
包装fastapi-users功能，保持原有API路径和响应格式不变
"""

from fastapi import APIRouter, Depends, Form, HTTPException, Request

from backend.auth_fastapi_users.auth import current_user, get_jwt_strategy
from backend.auth_fastapi_users.manager import User<PERSON>anager, get_user_manager
from backend.auth_fastapi_users.models import User

router = APIRouter()


@router.post("/api/login")
async def compatible_login(
    request: Request,
    username: str = Form(...),
    password: str = Form(...),
    user_manager: UserManager = Depends(get_user_manager),
):
    """
    兼容原有/api/login路径
    使用fastapi-users认证，保持响应格式一致
    """
    try:
        # 使用fastapi-users的认证流程
        user = await user_manager.authenticate(
            username.strip(), password.strip(), request
        )

        if user is None:
            raise HTTPException(status_code=401, detail="Invalid credentials")

        if not user.is_active:
            raise HTTPException(status_code=401, detail="Account is inactive")

        # 生成JWT token
        strategy = get_jwt_strategy()
        token = await strategy.write_token(user)

        # 保持原有响应格式
        return {
            "success": True,
            "message": "Login successful",
            "access_token": token,
            "token_type": "bearer",
            "expires_in": 3600,
            "user": {
                "username": user.username or user.email,
                "permissions": ["admin"] if user.is_superuser else ["user"],
                "admin": user.is_superuser,
            },
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 登录错误: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/api/logout")
async def compatible_logout():
    """
    兼容原有/api/logout路径
    JWT是无状态的，只需要客户端删除token
    """
    return {
        "success": True,
        "message": "Logout successful. Please remove the token from client storage.",
    }


@router.get("/api/session-status")
async def compatible_session_status(user: User = Depends(current_user)):
    """
    兼容原有/api/session-status路径
    使用fastapi-users的current_user依赖
    """
    return {
        "success": True,
        "message": "Token valid",
        "username": user.username or user.email,
        "auth_type": "jwt_bearer",
        "permissions": ["admin"] if user.is_superuser else ["user"],
        "admin": user.is_superuser,
        "email": user.email,
        "is_active": user.is_active,
        "is_verified": user.is_verified,
    }


@router.get("/api/user/profile")
async def compatible_user_profile(user: User = Depends(current_user)):
    """
    兼容原有/api/user/profile路径
    获取当前用户资料
    """
    return {
        "success": True,
        "user": {
            "username": user.username or user.email,
            "email": user.email,
            "permissions": ["admin"] if user.is_superuser else ["user"],
            "admin": user.is_superuser,
            "is_active": user.is_active,
            "is_verified": user.is_verified,
            "created_at": user.created_at.isoformat() if user.created_at else None,
            "role": user.role,
        },
    }


@router.post("/api/refresh-token")
async def compatible_refresh_token(user: User = Depends(current_user)):
    """
    兼容原有/api/refresh-token路径
    刷新访问令牌
    """
    try:
        # 生成新的JWT token
        strategy = get_jwt_strategy()
        new_token = await strategy.write_token(user)

        return {
            "success": True,
            "message": "Token refreshed successfully",
            "access_token": new_token,
            "token_type": "bearer",
            "expires_in": 3600,
        }
    except Exception as e:
        print(f"❌ 刷新token错误: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# 预留扩展路由（初期注释掉）
"""
# 用户注册 - 预留接口
@router.post("/api/register")
async def register_user():
    # 暂不开放注册功能
    raise HTTPException(status_code=404, detail="Registration not available")

# 密码重置 - 预留接口
@router.post("/api/forgot-password")
async def forgot_password():
    # 暂不开放密码重置功能
    raise HTTPException(status_code=404, detail="Password reset not available")

# 邮箱验证 - 预留接口
@router.post("/api/verify-email")
async def verify_email():
    # 暂不开放邮箱验证功能
    raise HTTPException(status_code=404, detail="Email verification not available")
"""
