# api/database/__init__.py

"""
统一数据库配置模块
===============================

- unified_connection.py: 统一连接池管理器
- initializer.py: 统一初始化器

🚫 已删除的旧架构文件
- base.py: 过度设计的抽象接
- factory.py: 冗余的工厂模
- postgresql_adapter.py: 历史包袱适配
- order_schema.py: 冗余的包装函

📋 使用规范:
```python
# 正确用法 - 获取统一数据库连接
from backend.db_config.unified_connection import get_unified_db

async def example():
    unified_db = await get_unified_db()

    # 使用会话
    async with unified_db.get_session() as session:
        # SQLAlchemy ORM操作
        pass

    # 使用原始连接
    async with unified_db.get_connection() as conn:
        # 原始SQL操作
        result = await conn.execute(text("SELECT 1"))

# 正确用法 - 数据库初始化
from backend.db_config.initializer import DatabaseInitializer

async def init_database():
    unified_db = await get_unified_db()
    success = await DatabaseInitializer.initialize_once(unified_db.engine)
```

🚫 禁止使用已删除的模块:
- from backend.db_config.factory import *  # 已删除
- from backend.db_config.base import *      # 已删除
- from backend.db_config.postgresql_adapter import *  # 已删除
"""
