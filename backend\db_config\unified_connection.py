"""
统一数据库连接管理器
按照GG Notepad要求，统一使用SQLAlchemy连接池，替换asyncpg连接
单一数据源，避免连接池竞争和连接泄漏

核心原则
- 全局单例SQLAlchemy引擎
- 动态调整连接池参数
- 环境隔离配置
- 连接健康检
- 资源自动清理
"""

from contextlib import asynccontextmanager
import logging
from typing import Any, Optional

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from backend.config.settings import settings

logger = logging.getLogger(__name__)


class UnifiedDatabaseManager:
    """
    统一数据库连接管理器 - 单例模式
    替换所有asyncpg和多SQLAlchemy引擎
    """

    _instance: Optional["UnifiedDatabaseManager"] = None
    _initialized: bool = False

    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化管理器 - 只初始化一次"""
        if self._initialized:
            return
        self.engine: AsyncEngine | None = None
        self.async_session: sessionmaker | None = None
        self.connection_string: str = self._build_connection_string()

        # 连接池配置 - 根据环境动态调整
        self.pool_config = self._get_pool_config()

        self._initialized = True
        logger.info("🚀 统一数据库管理器初始化完成")

    def _build_connection_string(self) -> str:
        """构建异步连接字符串"""
        return (
            f"postgresql+asyncpg://{settings.postgres_user}:{settings.postgres_password}"
            f"@{settings.postgres_host}:{settings.postgres_port}/{settings.postgres_db}"
        )

    def _get_pool_config(self) -> dict[str, Any]:
        """获取连接池配置 根据环境优化"""

        # 检测运行环境
        is_production = (
            getattr(settings, "environment", "development").lower() == "production"
        )
        is_testing = (
            getattr(settings, "environment", "development").lower() == "testing"
        )

        if is_production:
            # 生产环境：更大的连接池，更长的超时
            return {
                "pool_size": 30,
                "max_overflow": 50,
                "pool_timeout": 60,
                "pool_recycle": 3600,
                "pool_pre_ping": True,
                "echo": False,
            }
        elif is_testing:
            # 测试环境：更小的连接池，快速回滚
            return {
                "pool_size": 5,
                "max_overflow": 10,
                "pool_timeout": 30,
                "pool_recycle": 900,  # 15分钟回收
                "pool_pre_ping": True,
                "echo": False,
            }
        else:
            # 开发环境：中等连接池，启用调试
            return {
                "pool_size": 10,
                "max_overflow": 20,
                "pool_timeout": 30,
                "pool_recycle": 1800,  # 30分钟回收
                "pool_pre_ping": True,
                "echo": False,  # 关闭SQLAlchemy内置日志，使用我们的统一日志系统
            }

    async def initialize(self) -> bool:
        """
        初始化数据库引擎和会话
        确保资源正确初始化
        """
        try:
            logger.info(f"🔗 初始化统一数据库连接 {self.connection_string}")

            # 创建异步引擎
            self.engine = create_async_engine(
                self.connection_string, **self.pool_config
            )

            # 创建异步会话工厂
            self.async_session = sessionmaker(  # type: ignore[call-overload]
                bind=self.engine, class_=AsyncSession, expire_on_commit=False
            )

            # 测试连接
            await self._test_connection()

            logger.info(f"🔗 统一数据库连接初始化成功，连接池配置: {self.pool_config}")
            return True
        except Exception as e:
            logger.error(f"🔗 统一数据库连接初始化失败: {e}")
            return False

    async def _test_connection(self):
        """测试数据库连接"""
        if not self.engine:
            raise Exception("数据库引擎未初始化")

        async with self.engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            if result.scalar() != 1:
                raise ConnectionError("数据库连接测试失败")

        logger.info("🔗 数据库连接测试通过")

    @asynccontextmanager
    async def get_session(self):
        """获取数据库会话 上下文管理器
        自动清理资源，符合GG Notepad要求"""
        if not self.async_session:
            raise RuntimeError("数据库会话工厂未初始化")

        async with self.async_session() as session:
            try:
                yield session
            except Exception as e:
                logger.error(f"🔗 数据库会话异常: {e}")
                await session.rollback()
                raise
            finally:
                await session.close()

    @asynccontextmanager
    async def get_connection(self):
        """获取原始数据库连接 用于兼容asyncpg风格的代码
        提供兼容性接口，方便迁移"""
        if not self.engine:
            raise RuntimeError("数据库引擎未初始化")

        async with self.engine.begin() as conn:
            try:
                yield conn
            except Exception as e:
                logger.error(f"🔗 数据库连接异常: {e}")
                raise

    async def execute_query(self, query: str, *args) -> Any:
        """执行单个查询 简化接口
        提供简单的查询接口"""
        async with self.get_connection() as conn:
            result = await conn.execute(text(query), list(args))
            return result

    async def execute(self, query: str, params=None) -> Any:
        """执行查询 - 兼容参数格式"""
        async with self.get_connection() as conn:
            if params is None:
                result = await conn.execute(text(query))
            else:
                result = await conn.execute(text(query), params)
            return result

    async def fetch_one(self, query: str, *args) -> dict | None:
        """获取单行记录"""
        async with self.get_connection() as conn:
            result = await conn.execute(text(query), list(args))
            row = result.fetchone()
            return dict(row._mapping) if row else None

    async def fetch_all(self, query: str, params: dict | None = None) -> list:
        """获取所有记录"""
        async with self.get_connection() as conn:
            if params is None:
                result = await conn.execute(text(query))
            else:
                result = await conn.execute(text(query), params)
            rows = result.fetchall()
            return [dict(row._mapping) for row in rows]

    async def fetch_val(self, query: str, *args) -> Any:
        """获取单个值"""
        async with self.get_connection() as conn:
            result = await conn.execute(text(query), list(args))
            row = result.fetchone()
            return row[0] if row else None

    def get_stats(self) -> dict[str, Any]:
        """获取连接池统计信息 用于监控和调优"""
        if not self.engine or not self.engine.pool:
            return {"status": "not_initialized"}

        pool = self.engine.pool
        # SQLAlchemy 2.0 移除了Pool的统计方法，使用可用的属性
        try:
            # 获取基本池配置信息
            stats = {
                "status": "active",
            }

            # 尝试获取池的基本属性
            if hasattr(pool, "_pool"):
                # QueuePool 的内部属性
                stats["pool_size"] = getattr(pool, "_pool_size", "unknown")
                stats["max_overflow"] = getattr(pool, "_max_overflow", "unknown")
                if hasattr(pool, "_pool"):
                    stats["checked_in"] = str(pool._pool.qsize()) if pool._pool else "0"
                else:
                    stats["checked_in"] = "unknown"
                stats["checked_out"] = getattr(pool, "_checked_out", "unknown")
                stats["overflow"] = getattr(pool, "_overflow", "unknown")
                stats["invalid"] = getattr(pool, "_invalidated", "unknown")
            else:
                # 如果无法获取详细信息，返回基本状态
                stats.update(
                    {
                        "pool_size": "unknown",
                        "checked_in": "unknown",
                        "checked_out": "unknown",
                        "overflow": "unknown",
                        "invalid": "unknown",
                    }
                )

            return stats

        except Exception as e:
            # 如果获取统计信息失败，返回基本状态
            return {
                "status": "active_but_stats_unavailable",
                "error": str(e),
                "pool_size": "unknown",
                "checked_in": "unknown",
                "checked_out": "unknown",
                "overflow": "unknown",
                "invalid": "unknown",
            }

    async def get_orders_based_count_records(self, user_id: str) -> dict[str, Any]:
        """获取基于订单的统计记录 - 强制按用户过滤，确保数据安全"""
        try:
            query = """
            WITH latest_visa_status AS (
                SELECT DISTINCT ON (order_id)
                    order_id,
                    visa_status,
                    created_at
                FROM visa_status_history
                WHERE user_id = :user_id
                ORDER BY order_id, created_at DESC
            )
            SELECT
                COUNT(DISTINCT o.id) as total,
                COUNT(CASE WHEN DATE(o.created_at AT TIME ZONE 'Asia/Shanghai') = CURRENT_DATE THEN 1 END) as today_total,
                COUNT(CASE WHEN lvs.visa_status = 'approved' THEN 1 END) as success,
                COUNT(CASE WHEN lvs.visa_status = 'denied' THEN 1 END) as failed,
                COUNT(CASE WHEN lvs.visa_status IN ('submitted', 'additional_info_required') THEN 1 END) as pending,
                COUNT(CASE WHEN lvs.visa_status = 'submit_failure' THEN 1 END) as submit_failures,
                COUNT(CASE WHEN o.order_status = 'created' AND lvs.visa_status IS NULL THEN 1 END) as not_submitted,
                COUNT(CASE WHEN o.order_status = 'cancelled' THEN 1 END) as cancelled
            FROM "order" o
            LEFT JOIN latest_visa_status lvs ON o.id = lvs.order_id
            WHERE o.order_type = 'visa_application' AND o.user_id = :user_id
            """

            result = await self.fetch_one(query, {"user_id": user_id})

            if result:
                # 格式化为前端期望的字段名
                formatted_result = {
                    "total_applications": result.get("total", 0),
                    "today_applications": result.get("today_total", 0),
                    "successful_applications": result.get("success", 0),
                    "failed_applications": result.get("failed", 0),
                    "pending_applications": result.get("pending", 0),
                    "submit_failures": result.get("submit_failures", 0),
                    "not_submitted": result.get("not_submitted", 0),
                    "cancelled_orders": result.get("cancelled", 0),
                }

                # 计算成功率
                total_completed = (
                    formatted_result["successful_applications"]
                    + formatted_result["failed_applications"]
                )
                if total_completed > 0:
                    formatted_result["success_rate"] = round(
                        (formatted_result["successful_applications"] / total_completed)
                        * 100,
                        2,
                    )
                else:
                    formatted_result["success_rate"] = 0

                return formatted_result

            return {}

        except Exception as e:
            logger.error(f"❌ 获取用户 {user_id} 的统计失败: {e}")
            return {}

    async def get_orders_based_time_series_stats(
        self, period: str = "7d", user_id: str = None
    ) -> dict[str, Any]:
        """获取基于订单的时间序列统计 - 强制用户过滤，确保数据安全"""
        try:
            # 根据周期确定时间范围
            if period == "1d":
                interval = "1 day"
                date_format = "HH24:00"
            elif period == "7d":
                interval = "7 days"
                date_format = "YYYY-MM-DD"
            elif period == "30d":
                interval = "30 days"
                date_format = "YYYY-MM-DD"
            elif period == "90d":
                interval = "90 days"
                date_format = "YYYY-MM-DD"
            elif period == "1y":
                interval = "365 days"
                date_format = "YYYY-MM"
            else:
                interval = "7 days"
                date_format = "YYYY-MM-DD"

            # 构建查询条件
            where_conditions = ["created_at >= NOW() - INTERVAL '{interval}'"]
            params = {"date_format": date_format}

            if user_id:
                where_conditions.append("user_id = :user_id")
                params["user_id"] = user_id

            query = f"""
            SELECT
                TO_CHAR(created_at, :date_format) as date_key,
                COUNT(*) as total,
                COUNT(CASE WHEN order_status = 'created' THEN 1 END) as created,
                COUNT(CASE WHEN order_status = 'cancelled' THEN 1 END) as cancelled,
                0 as success,
                0 as failed
            FROM "order"
            WHERE {" AND ".join(where_conditions)}
            GROUP BY TO_CHAR(created_at, :date_format)
            ORDER BY date_key
            """

            result = await self.fetch_all(query.format(interval=interval), params)
            return {"data": result or [], "period": period}

        except Exception as e:
            logger.error(f"❌ 获取时间序列统计失败: {e}")
            return {"data": [], "period": period}

    async def get_orders_based_status_distribution(
        self, user_id: str = None
    ) -> dict[str, Any]:
        """获取订单状态分布统计 - 支持用户过滤"""
        try:
            where_condition = ""
            params = {}

            if user_id:
                where_condition = "WHERE user_id = :user_id"
                params["user_id"] = user_id

            query = f"""
            SELECT
                order_status as status,
                COUNT(*) as count
            FROM "order"
            {where_condition}
            GROUP BY order_status
            ORDER BY count DESC
            """

            result = await self.fetch_all(query, params)
            return {"data": result or []}

        except Exception as e:
            logger.error(f"❌ 获取状态分布统计失败: {e}")
            return {"data": []}

    async def get_orders_based_entry_gate_stats(
        self, user_id: str = None
    ) -> dict[str, Any]:
        """获取入境口岸统计 - 支持用户过滤"""
        try:
            where_conditions = ["ap.form_snapshot IS NOT NULL"]
            params = {}

            if user_id:
                where_conditions.append("o.user_id = :user_id")
                params["user_id"] = user_id

            query = f"""
            SELECT
                COALESCE(
                    ap.form_snapshot->>'intended_entry_gate',
                    '未指定'
                ) as entry_gate,
                COUNT(*) as count
            FROM "order" o
            LEFT JOIN application ap ON o.id = ap.order_id
            WHERE {" AND ".join(where_conditions)}
            GROUP BY ap.form_snapshot->>'intended_entry_gate'
            ORDER BY count DESC
            LIMIT 10
            """

            result = await self.fetch_all(query, params)
            return {"data": result or []}

        except Exception as e:
            logger.error(f"❌ 获取入境口岸统计失败: {e}")
            return {"data": []}

    async def get_orders_based_visa_type_stats(
        self, user_id: str = None
    ) -> dict[str, Any]:
        """获取签证类型统计 - 强制用户过滤，确保数据安全"""
        try:
            where_conditions = ["ap.form_snapshot IS NOT NULL"]
            params = {}

            if user_id:
                where_conditions.append("o.user_id = :user_id")
                params["user_id"] = user_id

            query = f"""
            SELECT
                COALESCE(
                    ap.form_snapshot->>'visa_entry_type',
                    '未指定'
                ) as visa_type,
                COUNT(*) as count
            FROM "order" o
            LEFT JOIN application ap ON o.id = ap.order_id
            WHERE {" AND ".join(where_conditions)}
            GROUP BY ap.form_snapshot->>'visa_entry_type'
            ORDER BY count DESC
            """

            result = await self.fetch_all(query, params)
            return {"data": result or []}

        except Exception as e:
            logger.error(f"❌ 获取签证类型统计失败: {e}")
            return {"data": []}

    async def get_orders_based_processing_time_stats(
        self, user_id: str = None
    ) -> dict[str, Any]:
        """获取处理时间统计 - 支持用户过滤"""
        try:
            where_conditions = [
                "order_status IN ('created', 'cancelled')",
                "updated_at > created_at",
            ]
            params = {}

            if user_id:
                where_conditions.append("user_id = :user_id")
                params["user_id"] = user_id

            query = f"""
            SELECT
                AVG(EXTRACT(EPOCH FROM (updated_at - created_at))/3600) as avg_hours,
                MIN(EXTRACT(EPOCH FROM (updated_at - created_at))/3600) as min_hours,
                MAX(EXTRACT(EPOCH FROM (updated_at - created_at))/3600) as max_hours,
                COUNT(*) as total_processed
            FROM "order"
            WHERE {" AND ".join(where_conditions)}
            """

            result = await self.fetch_one(query, params)
            return result or {}

        except Exception as e:
            logger.error(f"❌ 获取处理时间统计失败: {e}")
            return {}

    # 业务查询方法已迁移到相应的Repository中
    # query_orders_with_filters -> OrderRepository.query_orders_with_filters

    async def health_check(self) -> bool:
        """健康检查"""
        try:
            await self._test_connection()
            return True
        except Exception as e:
            logger.error(f"🔗 数据库健康检查失败: {e}")
            return False

    async def cleanup(self):
        """
        清理资源，应用关闭时调用
        确保连接池正确关闭，避免事件循环冲突
        """
        if self.engine:
            try:
                # 优雅关闭连接池
                await self.engine.dispose()
                logger.info("✅ 统一数据库连接池已清理")
            except Exception as e:
                logger.warning(f"⚠️ 清理数据库连接池时出现警告: {e}")
            finally:
                self.engine = None
                self.async_session = None

    # ===== 新增：申请查询方法 - 修复MyPy错误 =====

    async def query_applications_by_identifier(
        self, identifier: str
    ) -> list[dict[str, Any]]:
        """
        根据标识符查询申请（支持申请编号、护照号码、越南申请编号）

        Args:
            identifier: 标识符（申请编号/护照号码/越南申请编号）

        Returns:
            申请记录列表，包含路由期望的字段格式
        """
        try:
            query = """
            SELECT
                a.id,
                a.status,
                a.vietnam_application_number,
                a.created_at,
                a.updated_at,
                o.order_no,
                o.order_status,
                ap.passport_number,
                ap.surname,
                ap.given_name,
                ap.chinese_name,
                a.form_snapshot
            FROM application a
            JOIN "order" o ON a.order_id = o.id
            JOIN applicant ap ON a.applicant_id = ap.id
            WHERE a.vietnam_application_number = :identifier
               OR ap.passport_number = :identifier
               OR o.order_no = :identifier
            ORDER BY a.created_at DESC
            """

            rows = await self.fetch_all(query, {"identifier": identifier})

            # 转换为路由期望的字典格式
            results = []
            for row in rows:
                # 构建申请人姓名
                applicant_name = (
                    row.get("chinese_name")
                    or f"{row.get('surname', '')} {row.get('given_name', '')}".strip()
                )

                # 字段映射转换
                application_dict = {
                    "id": str(row["id"]) if row.get("id") else None,
                    "application_number": row.get("vietnam_application_number")
                    or row.get("order_no"),
                    "submit_status": self._map_application_status(row.get("status")),
                    "approval_status": "WAITING",  # 保持兼容性
                    "created_at": row.get("created_at"),
                    "updated_at": row.get("updated_at"),
                    "passport_number": row.get("passport_number"),
                    "surname": row.get("surname"),
                    "given_name": row.get("given_name"),
                    "chinese_name": row.get("chinese_name"),
                    "applicant_name": applicant_name,
                    "order_no": row.get("order_no"),
                    "order_status": row.get("order_status"),
                }

                # 从form_snapshot中提取签证信息
                if row.get("form_snapshot"):
                    form_data = row["form_snapshot"]
                    application_dict.update(
                        {
                            "visa_type": form_data.get("visa_entry_type"),
                            "visa_validity_days": form_data.get("visa_validity_days"),
                            "visa_start_date": form_data.get("visa_start_date"),
                            "intended_entry_gate": form_data.get("intended_entry_gate"),
                        }
                    )

                results.append(application_dict)

            return results

        except Exception as e:
            logger.error(f"❌ 根据标识符查询申请失败: {identifier} - {e}")
            return []

    async def get_user_applications(self, passport_number: str) -> list[dict[str, Any]]:
        """
        根据护照号获取用户的所有申请记录

        Args:
            passport_number: 护照号码

        Returns:
            用户申请记录列表
        """
        try:
            query = """
            SELECT
                a.id,
                a.status,
                a.vietnam_application_number,
                a.created_at,
                a.updated_at,
                o.order_no,
                o.order_status,
                ap.passport_number,
                ap.surname,
                ap.given_name,
                ap.chinese_name,
                a.form_snapshot
            FROM application a
            JOIN "order" o ON a.order_id = o.id
            JOIN applicant ap ON a.applicant_id = ap.id
            WHERE ap.passport_number = :passport_number
            ORDER BY a.created_at DESC
            """

            rows = await self.fetch_all(query, {"passport_number": passport_number})

            # 转换为路由期望的字典格式
            results = []
            for row in rows:
                # 构建申请人姓名
                applicant_name = (
                    row.get("chinese_name")
                    or f"{row.get('surname', '')} {row.get('given_name', '')}".strip()
                )

                # 字段映射转换
                application_dict = {
                    "id": str(row["id"]) if row.get("id") else None,
                    "application_number": row.get("vietnam_application_number")
                    or row.get("order_no"),
                    "submit_status": self._map_application_status(row.get("status")),
                    "approval_status": "WAITING",  # 保持兼容性
                    "created_at": row.get("created_at"),
                    "updated_at": row.get("updated_at"),
                    "passport_number": row.get("passport_number"),
                    "surname": row.get("surname"),
                    "given_name": row.get("given_name"),
                    "chinese_name": row.get("chinese_name"),
                    "applicant_name": applicant_name,
                    "order_no": row.get("order_no"),
                    "order_status": row.get("order_status"),
                }

                # 从form_snapshot中提取签证信息
                if row.get("form_snapshot"):
                    form_data = row["form_snapshot"]
                    application_dict.update(
                        {
                            "visa_type": form_data.get("visa_entry_type"),
                            "visa_validity_days": form_data.get("visa_validity_days"),
                            "visa_start_date": form_data.get("visa_start_date"),
                            "intended_entry_gate": form_data.get("intended_entry_gate"),
                        }
                    )

                results.append(application_dict)

            return results

        except Exception as e:
            logger.error(f"❌ 获取用户申请记录失败: {passport_number} - {e}")
            return []

    async def query_applications_with_filters(
        self, filters: dict[str, Any], limit: int = 20, offset: int = 0
    ) -> list[dict[str, Any]]:
        """
        根据过滤条件查询申请记录（支持分页）

        Args:
            filters: 过滤条件字典，支持status, date_from, date_to, search
            limit: 每页记录数
            offset: 偏移量

        Returns:
            申请记录列表
        """
        try:
            # 构建基础查询
            base_query = """
            SELECT
                a.id,
                a.status,
                a.vietnam_application_number,
                a.created_at,
                a.updated_at,
                o.order_no,
                o.order_status,
                ap.passport_number,
                ap.surname,
                ap.given_name,
                ap.chinese_name,
                a.form_snapshot
            FROM application a
            JOIN "order" o ON a.order_id = o.id
            JOIN applicant ap ON a.applicant_id = ap.id
            WHERE 1=1
            """

            # 构建WHERE条件和参数
            where_conditions = []
            params = {}

            if filters.get("status"):
                where_conditions.append("a.status = :status")
                params["status"] = filters["status"]

            if filters.get("date_from"):
                where_conditions.append("a.created_at >= :date_from")
                params["date_from"] = filters["date_from"]

            if filters.get("date_to"):
                where_conditions.append("a.created_at <= :date_to")
                params["date_to"] = filters["date_to"]

            if filters.get("search"):
                search_term = f"%{filters['search']}%"
                where_conditions.append("""
                    (ap.passport_number ILIKE :search
                     OR ap.chinese_name ILIKE :search
                     OR ap.surname ILIKE :search
                     OR ap.given_name ILIKE :search
                     OR a.vietnam_application_number ILIKE :search
                     OR o.order_no ILIKE :search)
                """)
                params["search"] = search_term

            # 组装完整查询
            if where_conditions:
                base_query += " AND " + " AND ".join(where_conditions)

            base_query += " ORDER BY a.created_at DESC LIMIT :limit OFFSET :offset"
            params.update({"limit": limit, "offset": offset})

            rows = await self.fetch_all(base_query, params)

            # 转换为路由期望的字典格式
            results = []
            for row in rows:
                # 构建申请人姓名
                applicant_name = (
                    row.get("chinese_name")
                    or f"{row.get('surname', '')} {row.get('given_name', '')}".strip()
                )

                # 字段映射转换
                application_dict = {
                    "id": str(row["id"]) if row.get("id") else None,
                    "application_number": row.get("vietnam_application_number")
                    or row.get("order_no"),
                    "submit_status": self._map_application_status(row.get("status")),
                    "approval_status": "WAITING",  # 保持兼容性
                    "created_at": row.get("created_at"),
                    "updated_at": row.get("updated_at"),
                    "passport_number": row.get("passport_number"),
                    "surname": row.get("surname"),
                    "given_name": row.get("given_name"),
                    "chinese_name": row.get("chinese_name"),
                    "applicant_name": applicant_name,
                    "order_no": row.get("order_no"),
                    "order_status": row.get("order_status"),
                }

                # 从form_snapshot中提取签证信息
                if row.get("form_snapshot"):
                    form_data = row["form_snapshot"]
                    application_dict.update(
                        {
                            "visa_type": form_data.get("visa_entry_type"),
                            "visa_validity_days": form_data.get("visa_validity_days"),
                            "visa_start_date": form_data.get("visa_start_date"),
                            "intended_entry_gate": form_data.get("intended_entry_gate"),
                        }
                    )

                results.append(application_dict)

            return results

        except Exception as e:
            logger.error(f"❌ 根据过滤条件查询申请失败: {filters} - {e}")
            return []

    async def count_applications_with_filters(self, filters: dict[str, Any]) -> int:
        """
        统计符合过滤条件的申请记录数量

        Args:
            filters: 过滤条件字典，支持status, date_from, date_to, search

        Returns:
            符合条件的记录数量
        """
        try:
            # 构建基础计数查询
            base_query = """
            SELECT COUNT(*)
            FROM application a
            JOIN "order" o ON a.order_id = o.id
            JOIN applicant ap ON a.applicant_id = ap.id
            WHERE 1=1
            """

            # 构建WHERE条件和参数（与query_applications_with_filters保持一致）
            where_conditions = []
            params = {}

            if filters.get("status"):
                where_conditions.append("a.status = :status")
                params["status"] = filters["status"]

            if filters.get("date_from"):
                where_conditions.append("a.created_at >= :date_from")
                params["date_from"] = filters["date_from"]

            if filters.get("date_to"):
                where_conditions.append("a.created_at <= :date_to")
                params["date_to"] = filters["date_to"]

            if filters.get("search"):
                search_term = f"%{filters['search']}%"
                where_conditions.append("""
                    (ap.passport_number ILIKE :search
                     OR ap.chinese_name ILIKE :search
                     OR ap.surname ILIKE :search
                     OR ap.given_name ILIKE :search
                     OR a.vietnam_application_number ILIKE :search
                     OR o.order_no ILIKE :search)
                """)
                params["search"] = search_term

            # 组装完整查询
            if where_conditions:
                base_query += " AND " + " AND ".join(where_conditions)

            result = await self.fetch_val(base_query, params)
            return int(result) if result else 0

        except Exception as e:
            logger.error(f"❌ 统计申请记录数量失败: {filters} - {e}")
            return 0

    def _map_application_status(self, status: str | None) -> str:
        """
        将Application.status映射为路由期望的submit_status格式

        Args:
            status: 原始状态值

        Returns:
            映射后的状态值
        """
        if not status:
            return "PENDING"

        # 状态映射表
        status_mapping = {
            "pending": "PENDING",
            "processing": "PROCESSING",
            "submitted": "SUCCESS",
            "approved": "SUCCESS",
            "denied": "FAILED",
            "cancelled": "CANCELLED",
            "failed": "FAILED",
        }

        return status_mapping.get(status.lower(), status.upper())

    # 业务查询方法已迁移到相应的Repository中


# 全局单例示例
_unified_db_manager: UnifiedDatabaseManager | None = None


async def get_unified_db() -> UnifiedDatabaseManager:
    """
    获取统一数据库管理实例
    全局访问
    """
    global _unified_db_manager

    if _unified_db_manager is None:
        _unified_db_manager = UnifiedDatabaseManager()

        # 确保初始化
        if not await _unified_db_manager.initialize():
            raise RuntimeError("统一数据库管理器初始化失败")

    return _unified_db_manager


# 便捷函数 向后兼容
async def get_db_session():
    """获取数据库会话"""
    db = await get_unified_db()
    return db.get_session()


async def get_db_connection():
    """获取原始数据库连接"""
    db = await get_unified_db()
    return db.get_connection()
