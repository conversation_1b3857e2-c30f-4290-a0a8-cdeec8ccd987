"""
FastAPI 依赖注入配置
==================

提供统一的数据库Session和Repository依赖注入
符合FastAPI最佳实践
"""

from collections.abc import AsyncGenerator

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.applicant_repository import ApplicantRepository
from app.repositories.application_repository import ApplicationRepository
from app.repositories.automation_logs_repository import AutomationLogsRepository
from app.repositories.order_repository import OrderRepository
from app.services.order_service import OrderService
from backend.auth_fastapi_users.database import get_async_session


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    获取数据库Session
    复用auth模块的session管理
    """
    async with get_async_session() as session:
        yield session


async def get_order_repository(
    session: AsyncSession = Depends(get_db_session),
) -> OrderRepository:
    """
    获取OrderRepository实例
    Session通过依赖注入传入
    """
    return OrderRepository(session)


async def get_application_repository(
    session: AsyncSession = Depends(get_db_session),
) -> ApplicationRepository:
    """
    获取ApplicationRepository实例
    Session通过依赖注入传入
    """
    return ApplicationRepository(session)


async def get_applicant_repository(
    session: AsyncSession = Depends(get_db_session),
) -> ApplicantRepository:
    """
    获取ApplicantRepository实例
    Session通过依赖注入传入
    """
    return ApplicantRepository(session)


async def get_automation_logs_repository(
    session: AsyncSession = Depends(get_db_session),
) -> AutomationLogsRepository:
    """
    获取AutomationLogsRepository实例
    Session通过依赖注入传入
    """
    return AutomationLogsRepository(session)


async def get_order_service(
    order_repository: OrderRepository = Depends(get_order_repository),
) -> OrderService:
    """
    获取OrderService实例
    Repository通过依赖注入传入
    """
    service = OrderService(order_repository)
    await service.initialize()
    return service
