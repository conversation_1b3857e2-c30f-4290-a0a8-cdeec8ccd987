# api/middleware/security.py
"""
安全中间件
"""

from collections import defaultdict
import time

from fastapi import HTTPEx<PERSON>, Request
from starlette.middleware.base import BaseHTTPMiddleware


class RateLimitMiddleware(BaseHTTPMiddleware):
    """简单的请求限制中间件"""

    def __init__(self, app, calls: int = 100, period: int = 60):
        super().__init__(app)
        self.calls = calls  # 允许的请求次数
        self.period = period  # 时间窗口（秒）
        self.requests: defaultdict[str, list[float]] = defaultdict(list)

    async def dispatch(self, request: Request, call_next):
        # 获取客户端IP
        client_ip = request.client.host if request.client else "unknown"

        # 清理过期的请求记录
        current_time = time.time()
        self.requests[client_ip] = [
            req_time
            for req_time in self.requests[client_ip]
            if current_time - req_time < self.period
        ]

        # 检查请求频率
        if len(self.requests[client_ip]) >= self.calls:
            raise HTTPException(status_code=429, detail="请求过于频繁，请稍后再试")

        # 记录当前请求
        self.requests[client_ip].append(current_time)

        response = await call_next(request)
        return response


class RequestSizeMiddleware(BaseHTTPMiddleware):
    """请求大小限制中间件"""

    def __init__(self, app, max_size: int = 10 * 1024 * 1024):  # 10MB
        super().__init__(app)
        self.max_size = max_size

    async def dispatch(self, request: Request, call_next):
        # 检查Content-Length头
        content_length_str = request.headers.get("content-length")
        if content_length_str:
            content_length = int(content_length_str)
            if content_length > self.max_size:
                raise HTTPException(
                    status_code=413, detail=f"请求体过大，最大允许 {self.max_size} 字节"
                )

        response = await call_next(request)
        return response


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""

    def __init__(self, app, debug: bool = False):
        super().__init__(app)
        self.debug = debug

    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)

        # 基础安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"

        # 生产环境安全头
        if not self.debug:
            response.headers["Strict-Transport-Security"] = (
                "max-age=31536000; includeSubDomains"
            )
            response.headers["Content-Security-Policy"] = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data:; "
                "font-src 'self'"
            )

        return response
