"""
订单管理模型 - 重构完成
==================

已重构：移除SQLAlchemy ORM模型重复定义
已重构：移除Pydantic API模型（已迁移到backend/api/schemas/order.py）
统一引用：使用app/data/models/order.py中的Order模型
保留兼容：保留枚举类供其他模块使用
"""

from enum import Enum

# 重新导出新架构的Order模型


# 保留枚举类供其他模块使用（专注于订单管理，不向后兼容）
class OrderStatus(str, Enum):
    """订单状态枚举 - 匹配数据库约束（简化版本）"""

    # 核心订单状态（匹配 simple_order_status_check 约束）
    CREATED = "created"  # 订单已创建
    CANCELLED = "cancelled"  # 订单已取消

    # 取消扩展状态，订单不涉及具体业务流程状态保持简洁，不存储在数据库中（不用于应用逻辑层）


# OperatorType枚举已删除 - 实际业务中直接使用字符串更简单
# 在实际调用中都是传入字符串 "user"，没有真正的系统操作场景

# 重构完成说明
# 1. SQLAlchemy ORM模型：统一使用 app/data/models/order.py 中的 Order ORM定义
# 2. Pydantic API模型：已迁移到 backend/api/schemas/order.py
# 3. 简化设计：删除过度设计的OperatorType枚举，实际业务中直接使用字符串
# 4. 导入建议:
#    - ORM操作：from app.data.models.order import Order
#    - API模型：from backend.api.schemas.order import CreateOrderRequest, OrderInfo
#    - 枚举类：from backend.models.order import OrderStatus
