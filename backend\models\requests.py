# api/models/requests.py

from pydantic import BaseModel, Field

# 兼容不同版本的Pydantic验证器
try:
    # from pydantic import field_validator  # 未使用，已注释

    PYDANTIC_V2 = True
except ImportError:
    PYDANTIC_V2 = False


class VisaFormTextFields(BaseModel):
    """签证表单文本字段"""

    surname: str = Field(..., description="姓")
    given_name: str = Field(..., description="名")
    chinese_name: str | None = Field(None, description="中文名")
    sex: str = Field(..., description="性别", pattern="^[MF]$")
    dob: str = Field(
        ..., description="出生日期", pattern=r"^(\d{2}/\d{2}/\d{4}|\d{8})$"
    )
    place_of_birth: str = Field(..., description="出生地")
    nationality: str = Field(default="CHINA", description="国籍")
    religion: str = Field(default="NO", description="宗教")
    passport_number: str = Field(..., description="护照号码")
    passport_type: str = Field(default="Ordinary passport", description="护照类型")
    place_of_issue: str = Field(..., description="签发地")
    date_of_issue: str = Field(
        ..., description="签发日期", pattern=r"^(\d{2}/\d{2}/\d{4}|\d{8})$"
    )
    passport_expiry: str = Field(
        ..., description="护照有效期", pattern=r"^(\d{2}/\d{2}/\d{4}|\d{8})$"
    )
    email: str = Field(..., description="邮箱")
    telephone_number: str = Field(..., description="电话号码")
    permanent_address: str | None = Field(None, description="永久地址")
    contact_address: str | None = Field(None, description="联系地址")
    emergency_contact_name: str | None = Field(None, description="紧急联系人")
    emergency_address: str | None = Field(None, description="紧急地址")
    emergency_contact_phone: str | None = Field(None, description="紧急联系人电话")
    visa_entry_type: str = Field(..., description="签证类型")
    visa_validity_duration: str = Field(..., description="签证有效期")
    visa_start_date: str = Field(
        ..., description="签证生效日期", pattern=r"^\d{2}/\d{2}/\d{4}$"
    )
    intended_entry_gate: str = Field(..., description="入境口岸")

    # 暂时移除复杂的验证器，确保兼容性
    # 可以在后续版本中重新添加

    class Config:
        json_schema_extra = {
            "example": {
                "surname": "ZHANG",
                "given_name": "SAN",
                "chinese_name": "张三",
                "sex": "M",
                "dob": "01/01/1990",
                "place_of_birth": "Beijing",
                "nationality": "CHINA",
                "religion": "NO",
                "passport_number": "E12345678",
                "passport_type": "Ordinary passport",
                "place_of_issue": "Beijing",
                "date_of_issue": "01/01/2020",
                "passport_expiry": "01/01/2030",
                "email": "<EMAIL>",
                "telephone_number": "+86 138 0013 8000",
                "visa_entry_type": "Single-entry",
                "visa_validity_duration": "30天",
                "visa_start_date": "01/06/2025",
                "intended_entry_gate": "Tan Son Nhat Int Airport (Ho Chi Minh City)",
            }
        }


class LoginRequest(BaseModel):
    """登录请求"""

    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")

    class Config:
        json_schema_extra = {
            "example": {"username": "admin", "password": "password123"}
        }
