# backend/routes/visa/admin.py
"""
签证申请管理员功能模块

遵循PP和QQ Notepad要求：
- 单一职责：专注于管理员功能
- API兼容性：保持现有接口完全兼容
- 异常处理：全链路日志与异常兜底
- 权限控制：管理员认证
"""

from fastapi import APIRouter, Depends, HTTPException, Query

from app.utils.logger_config import get_logger
from backend.auth_fastapi_users.dependencies import require_auth
from backend.db_config.unified_connection import get_unified_db

logger = get_logger()

router = APIRouter()


@router.get("/admin/dashboard")
async def admin_dashboard(user=Depends(require_auth)):
    """
    管理员仪表板

    - **返回**: 统计信息和仪表板数据
    """
    try:
        logger.info(
            f"📊 管理员仪表板访问: {user.email if hasattr(user, 'email') else 'unknown'}"
        )

        db = await get_unified_db()
        # ✅ 使用基于orders表的统计（替换旧的visa_task查询）
        stats = await db.get_orders_based_dashboard_stats()

        response = {"success": True, "message": "仪表板数据获取成功", "data": stats}

        logger.info("✅ 仪表板数据获取成功")
        return response

    except Exception as e:
        logger.error(f"❌ 获取仪表板数据失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取仪表板数据失败: {str(e)}")


@router.get("/admin/query")
async def admin_query_applications(
    page: int = Query(1, description="页码"),
    limit: int = Query(20, description="每页记录数"),
    status: str | None = Query(None, description="状态筛选"),
    date_from: str | None = Query(None, description="开始日期"),
    date_to: str | None = Query(None, description="结束日期"),
    search: str | None = Query(None, description="搜索关键词"),
    user=Depends(require_auth),
):
    """
    管理员查询申请记录

    - **page**: 页码 (默认: 1)
    - **limit**: 每页记录数 (默认: 20, 最大: 100)
    - **status**: 申请状态筛选 (SUCCESS, FAILED, PENDING)
    - **date_from**: 开始日期 (YYYY-MM-DD)
    - **date_to**: 结束日期 (YYYY-MM-DD)
    - **search**: 搜索关键词 (姓名、护照号码、申请编号)
    - **返回**: 分页的申请记录列表
    """
    try:
        logger.info(
            f"🔍 管理员查询申请记录: page={page}, limit={limit}, status={status}"
        )

        # 参数验证
        if limit > 100:
            limit = 100
        if page < 1:
            page = 1

        db = await get_unified_db()

        # 构建查询条件
        filters = {}
        if status:
            filters["status"] = status
        if date_from:
            filters["date_from"] = date_from
        if date_to:
            filters["date_to"] = date_to
        if search:
            filters["search"] = search

        # 获取分页数据
        offset = (page - 1) * limit
        applications = await db.query_applications_with_filters(
            filters=filters, limit=limit, offset=offset
        )

        # 获取总数
        total_count = await db.count_applications_with_filters(filters)
        total_pages = (total_count + limit - 1) // limit

        response = {
            "success": True,
            "message": f"查询成功，共找到 {total_count} 条记录",
            "data": {
                "applications": applications,
                "pagination": {
                    "current_page": page,
                    "total_pages": total_pages,
                    "total_count": total_count,
                    "page_size": limit,
                    "has_next": page < total_pages,
                    "has_prev": page > 1,
                },
            },
        }

        logger.info(
            f"✅ 管理员查询成功: 返回{len(applications)}条记录，总计{total_count}条"
        )
        return response

    except Exception as e:
        logger.error(f"❌ 查询申请记录失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")


@router.get("/admin/statistics")
async def admin_statistics(
    period: str = Query("7d", description="统计周期"), user=Depends(require_auth)
):
    """
    管理员统计数据

    - **period**: 统计周期 (1d, 7d, 30d, 90d, 1y)
    - **返回**: 详细统计数据
    """
    try:
        logger.info(f"📈 管理员统计数据查询: period={period}")

        db = await get_unified_db()

        # ✅ 使用基于orders表的统计，传入用户ID确保数据安全
        basic_stats = await db.get_orders_based_count_records(user_id=user.id)

        # 获取时间序列统计
        time_series = await db.get_orders_based_time_series_stats(
            period, user_id=user.id
        )

        # 获取状态分布
        status_distribution = await db.get_orders_based_status_distribution(
            user_id=user.id
        )

        # 获取入境口岸统计
        entry_gate_stats = await db.get_orders_based_entry_gate_stats(user_id=user.id)

        # 获取签证类型统计
        visa_type_stats = await db.get_orders_based_visa_type_stats(user_id=user.id)

        # 获取处理时间统计
        processing_time_stats = await db.get_orders_based_processing_time_stats(
            user_id=user.id
        )

        response = {
            "success": True,
            "message": "统计数据获取成功",
            "data": {
                "basic_stats": basic_stats,
                "time_series": time_series,
                "status_distribution": status_distribution,
                "entry_gate_stats": entry_gate_stats,
                "visa_type_stats": visa_type_stats,
                "processing_time_stats": processing_time_stats,
                "period": period,
            },
        }

        logger.info(f"✅ 统计数据获取成功: period={period}")
        return response

    except Exception as e:
        logger.error(f"❌ 获取统计数据失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取统计数据失败: {str(e)}")


@router.get("/admin/application/{application_number}")
async def get_application_details(application_number: str, user=Depends(require_auth)):
    """
    获取申请详情

    - **application_number**: 申请编号
    - **返回**: 申请详细信息
    """
    try:
        logger.info(f"🔍 获取申请详情: {application_number}")

        db = await get_unified_db()

        # 查询申请详情
        applications = await db.query_applications_by_identifier(application_number)

        if not applications:
            logger.warning(f"⚠️ 未找到申请记录: {application_number}")
            raise HTTPException(status_code=404, detail="未找到相关申请记录")

        application = applications[0]

        response = {"success": True, "message": "获取申请详情成功", "data": application}

        logger.info(f"✅ 申请详情获取成功: {application_number}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"❌ 获取申请详情失败: {application_number} - {str(e)}", exc_info=True
        )
        raise HTTPException(status_code=500, detail=f"获取申请详情失败: {str(e)}")


@router.get("/admin/realtime-stats")
async def admin_realtime_stats(user=Depends(require_auth)):
    """
    用户统计数据 - 只显示当前用户的数据，确保数据安全
    """
    try:
        logger.info(f"📊 获取用户 {user.id} 的统计数据")

        db = await get_unified_db()

        # 强制传入用户ID，确保数据安全
        db_stats = await db.get_orders_based_count_records(user_id=user.id)

        # 如果没有数据，返回默认值
        if not db_stats:
            db_stats = {
                "total_applications": 0,
                "successful_applications": 0,
                "failed_applications": 0,
                "pending_applications": 0,
                "submit_failures": 0,
                "not_submitted": 0,
                "cancelled_orders": 0,
                "success_rate": 0,
            }

        # 包装为前端期望的格式
        response = {
            "success": True,
            "message": "实时统计数据获取成功",
            "data": db_stats,
        }

        logger.info(f"✅ 用户 {user.id} 的统计数据: {db_stats}")
        return response

    except Exception as e:
        logger.error(f"❌ 获取实时统计数据失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取实时统计数据失败: {str(e)}")


@router.get("/admin/basic-stats")
async def admin_basic_stats(user=Depends(require_auth)):
    """
    管理员基础统计数据

    - **返回**: 基础统计数据
    """
    try:
        logger.info("📊 获取基础统计数据")

        db = await get_unified_db()
        # ✅ 使用基于orders表的统计，传入用户ID确保数据安全
        basic_stats = await db.get_orders_based_count_records(user_id=user.id)

        response = {
            "success": True,
            "message": "基础统计数据获取成功",
            "data": basic_stats,
        }

        logger.info("✅ 基础统计数据获取成功")
        return response

    except Exception as e:
        logger.error(f"❌ 获取基础统计数据失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取基础统计数据失败: {str(e)}")
