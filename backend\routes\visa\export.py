# backend/routes/visa/export.py
"""
签证申请数据导出功能模块

遵循PP和QQ Notepad要求：
- 单一职责：专注于数据导出功能
- API兼容性：保持现有接口完全兼容
- 异常处理：全链路日志与异常兜底
- 流式响应：支持大数据量导出
"""

from datetime import datetime
import io
import os
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import StreamingResponse

from app.repositories.order_repository import OrderRepository
from app.utils.logger_config import get_logger
from backend.auth_fastapi_users.dependencies import require_admin_auth, require_auth
from backend.db_config.unified_connection import get_unified_db

logger = get_logger()

# 中文映射字典
VISA_TYPE_MAP = {
    "Single-entry": "单次",
    "Multiple-entry": "多次",
}

ENTRY_GATE_MAP = {
    "Tan Son Nhat Int Airport (Ho Chi Minh City)": "胡志明",
    "Noi Bai Int Airport": "河内",
    "Da Nang International Airport": "岘港",
    "Cam Ranh Int Airport (Khanh Hoa)": "芽庄",
    "Mong Cai Landport": "东兴",
    "Huu Nghi Landport": "友谊",
}

# 出行目的映射（匹配前端标准配置）
PURPOSE_OF_ENTRY_MAP = {
    "Tourist": "旅游",
    "Business": "商务",
    "Visiting relatives": "探亲",
    "Working": "工作",
    "Other": "其他",
}

# 签证状态映射（严格匹配数据库约束的5个状态）
VISA_STATUS_MAP = {
    "submit_failure": "提交失败",
    "submitted": "已提交",
    "additional_info_required": "需补充资料",
    "approved": "已出签",
    "denied": "已拒签",
}

router = APIRouter()


@router.get("/export")
async def export_user_data(
    format: str = Query("excel", description="导出格式"),
    date_from: str | None = Query(None, description="开始日期"),
    date_to: str | None = Query(None, description="结束日期"),
    status: str | None = Query(None, description="状态筛选"),
    user=Depends(require_auth),
):
    """
    导出用户申请数据

    🔐 安全性：只能导出当前用户的数据
    - **format**: 导出格式 (简单化，暂时只支持excel)
    - **date_from**: 开始日期 (YYYY-MM-DD)
    - **date_to**: 结束日期 (YYYY-MM-DD)
    - **status**: 状态筛选
    - **返回**: Excel格式导出文件
    """
    try:
        logger.info(
            f"📤 用户 {user.id} 开始导出数据: format={format}, date_from={date_from}, date_to={date_to}, status={status}"
        )

        db = await get_unified_db()

        # 使用Repository模式获取数据
        async with db.get_session() as session:
            order_repo = OrderRepository(session)

            # 🔐 构建查询条件 - 强制用户权限
            filters = {
                "user_id": user.id  # 🔐 强制只查询当前用户的数据
            }
            if status:
                filters["order_status"] = status
            if date_from:
                try:
                    date_obj = datetime.strptime(date_from, "%Y-%m-%d")
                    filters["date_from"] = date_obj.date()
                    logger.info(
                        f"📅 开始日期转换: {date_from} -> {filters['date_from']}"
                    )
                except ValueError:
                    logger.warning(f"⚠️ 无效的开始日期格式: {date_from}")
                    raise HTTPException(
                        status_code=400, detail=f"无效的开始日期格式: {date_from}"
                    )
            if date_to:
                try:
                    date_obj = datetime.strptime(date_to, "%Y-%m-%d")
                    filters["date_to"] = date_obj.date()
                    logger.info(f"📅 结束日期转换: {date_to} -> {filters['date_to']}")
                except ValueError:
                    logger.warning(f"⚠️ 无效的结束日期格式: {date_to}")
                    raise HTTPException(
                        status_code=400, detail=f"无效的结束日期格式: {date_to}"
                    )

            # 获取数据 - 使用Repository方法
            logger.info(f"🔍 导出查询条件: {filters}")

            # 添加详细的日期筛选日志
            if filters.get("date_from") or filters.get("date_to"):
                logger.info("📅 日期筛选详情:")
                if filters.get("date_from"):
                    from datetime import time

                    date_from_start = datetime.combine(filters["date_from"], time.min)
                    logger.info(
                        f"   开始时间: {filters['date_from']} -> {date_from_start}"
                    )
                    print(
                        f"🔍 [EXPORT DEBUG] 开始时间: {filters['date_from']} -> {date_from_start}"
                    )
                if filters.get("date_to"):
                    from datetime import time

                    date_to_end = datetime.combine(filters["date_to"], time.max)
                    logger.info(f"   结束时间: {filters['date_to']} -> {date_to_end}")
                    print(
                        f"🔍 [EXPORT DEBUG] 结束时间: {filters['date_to']} -> {date_to_end}"
                    )
            else:
                logger.debug("🔍 [EXPORT DEBUG] 没有日期筛选条件")

            applications = await order_repo.query_orders_with_filters(
                filters=filters,
                limit=10000,  # 导出限制
            )

        logger.info(f"📊 导出数据量: {len(applications)}条记录 (用户: {user.id})")

        if format.lower() == "excel":
            return await _export_excel(applications, date_from, date_to)
        else:
            logger.warning(f"⚠️ 不支持的导出格式: {format}，仅支持excel格式")
            raise HTTPException(status_code=400, detail="仅支持excel格式导出")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 导出数据失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")


@router.get("/admin/export")
async def admin_export_all_data(
    format: str = Query("excel", description="导出格式"),
    date_from: str | None = Query(None, description="开始日期"),
    date_to: str | None = Query(None, description="结束日期"),
    status: str | None = Query(None, description="状态筛选"),
    user=Depends(require_admin_auth),
):
    """
    管理员导出所有用户数据

    🔐 安全性：仅限管理员使用，可导出所有用户数据
    - **format**: 导出格式 (简单化，暂时只支持excel)
    - **date_from**: 开始日期 (YYYY-MM-DD)
    - **date_to**: 结束日期 (YYYY-MM-DD)
    - **status**: 状态筛选
    - **返回**: Excel格式导出文件
    """
    try:
        logger.info(
            f"📤 管理员 {user.id} 开始导出所有数据: format={format}, date_from={date_from}, date_to={date_to}, status={status}"
        )

        db = await get_unified_db()

        # 使用Repository模式获取数据
        async with db.get_session() as session:
            order_repo = OrderRepository(session)

            # 构建查询条件 - 管理员可以查询所有数据
            filters: dict[str, Any] = {}
            if status:
                filters["order_status"] = status
            if date_from:
                try:
                    date_obj = datetime.strptime(date_from, "%Y-%m-%d")
                    filters["date_from"] = (
                        date_obj.date()
                    )  # Keep as date object for filtering
                except ValueError:
                    raise HTTPException(
                        status_code=400, detail=f"无效的开始日期格式: {date_from}"
                    )
            if date_to:
                try:
                    date_obj = datetime.strptime(date_to, "%Y-%m-%d")
                    filters["date_to"] = (
                        date_obj.date()
                    )  # Keep as date object for filtering
                except ValueError:
                    raise HTTPException(
                        status_code=400, detail=f"无效的结束日期格式: {date_to}"
                    )

            # 🔐 管理员特权：使用特殊方法绕过用户权限检查
            applications = await _admin_query_all_orders(order_repo, filters)

        logger.info(f"📊 管理员导出数据量: {len(applications)}条记录")

        if format.lower() == "excel":
            return await _export_excel(applications, date_from, date_to)
        else:
            raise HTTPException(status_code=400, detail="仅支持excel格式导出")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 管理员导出数据失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")


async def _admin_query_all_orders(order_repo: OrderRepository, filters: dict) -> list:
    """
    管理员专用：查询所有用户订单（绕过权限检查）
    """
    from sqlalchemy import and_, desc, select

    from app.data.models.applicant import Applicant
    from app.data.models.application import Application
    from app.data.models.order import Order

    # 直接构建查询，不使用Repository的权限检查
    query = (
        select(
            Order.id,
            Order.order_no,
            Order.user_id,
            Order.order_status,
            Order.created_at,
            Order.updated_at,
            Applicant.passport_number,
            Applicant.surname,
            Applicant.given_name,
            Applicant.chinese_name,
            Applicant.date_of_birth,
            Applicant.email,
            Applicant.telephone_number,
            Application.vietnam_application_number,
            Application.form_snapshot.label("application_data"),
        )
        .select_from(Order)
        .outerjoin(Application, Order.id == Application.order_id)
        .outerjoin(Applicant, Application.applicant_id == Applicant.id)
    )

    # 添加过滤条件（不包含user_id限制）
    conditions = []
    if filters.get("order_status"):
        conditions.append(Order.order_status == filters["order_status"])
    if filters.get("date_from"):
        from app.repositories.order_repository import get_day_boundary

        date_from_start = get_day_boundary(filters["date_from"], "start")
        conditions.append(Order.created_at >= date_from_start)
    if filters.get("date_to"):
        from app.repositories.order_repository import get_day_boundary

        date_to_end = get_day_boundary(filters["date_to"], "end")
        conditions.append(Order.created_at <= date_to_end)

    if conditions:
        query = query.where(and_(*conditions))

    query = query.order_by(desc(Order.created_at)).limit(10000)

    result = await order_repo.session.execute(query)
    return list(result.fetchall())  # type: ignore[return-value]


async def _export_excel(
    applications: list, date_from: str | None = None, date_to: str | None = None
) -> StreamingResponse:
    """
    导出Excel格式数据

    Args:
        applications: 申请记录列表
        date_from: 开始日期 (用于生成文件名)
        date_to: 结束日期 (用于生成文件名)

    Returns:
        StreamingResponse: Excel文件流响应
    """
    try:
        logger.info("📊 开始生成Excel文件")

        # 动态导入pandas和openpyxl (避免启动时加载)
        try:
            import tempfile

            from openpyxl import load_workbook
            from openpyxl.styles import Alignment, Font, PatternFill
            import pandas as pd

            logger.info("✅ Excel处理库导入成功")
        except ImportError as import_error:
            logger.error(f"❌ 缺少必要的Excel处理库: {import_error}")
            raise HTTPException(
                status_code=500, detail="Excel导出功能不可用，请安装pandas和openpyxl"
            )

        # 准备数据
        data = []
        stored_expedited_count = 0  # 初始化计数器
        for index, row in enumerate(applications, 1):
            # 🔍 安全检查：确保row不为None
            if not row:
                logger.warning(f"⚠️ 跳过空订单记录，索引: {index}")
                continue

            # 🔧 修复：将SQLAlchemy Row对象转换为字典
            try:
                order = dict(row._mapping) if hasattr(row, "_mapping") else dict(row)
            except Exception as e:
                logger.error(f"❌ 转换Row对象失败: {e}")
                # 尝试直接访问属性
                order = {
                    "id": getattr(row, "id", None),
                    "order_no": getattr(row, "order_no", ""),
                    "user_id": getattr(row, "user_id", None),
                    "order_status": getattr(row, "order_status", ""),
                    "created_at": getattr(row, "created_at", ""),
                    "updated_at": getattr(row, "updated_at", ""),
                    "passport_number": getattr(row, "passport_number", ""),
                    "surname": getattr(row, "surname", ""),
                    "given_name": getattr(row, "given_name", ""),
                    "chinese_name": getattr(row, "chinese_name", ""),
                    "date_of_birth": getattr(row, "date_of_birth", ""),
                    "email": getattr(row, "email", ""),
                    "telephone_number": getattr(row, "telephone_number", ""),
                    "vietnam_application_number": getattr(
                        row, "vietnam_application_number", ""
                    ),
                    "application_data": getattr(row, "application_data", {}),
                }

            # 🔍 修复数据提取逻辑：从application_data的form_data中获取详细信息
            app_data = order.get("application_data", {}) or {}

            # 🔍 提取form_data（实际数据存储位置）
            form_data = (
                app_data.get("form_data", {}) if isinstance(app_data, dict) else {}
            )
            if not form_data:
                # 如果没有form_data，尝试直接从application_data获取（兼容旧数据）
                form_data = app_data if isinstance(app_data, dict) else {}

            # 🎯 加急类型：仅使用存储的expedited_type，无推断逻辑
            expedited_type_display = ""

            # 唯一数据源：存储的expedited_type（100%准确）
            stored_expedited_type = form_data.get("expedited_type") or app_data.get(
                "expedited_type"
            )

            # 添加调试日志，查看数据存储情况
            logger.debug(
                f"🔍 调试加急类型数据: order_no={order.get('order_no', 'N/A')}, "
                f"form_data.expedited_type={form_data.get('expedited_type')}, "
                f"app_data.expedited_type={app_data.get('expedited_type')}"
            )

            if stored_expedited_type:
                # 转换为显示文本
                expedited_map = {
                    "1days": "1工加急",
                    "2days": "2工加急",
                    "3days": "3工加急",
                    "4days": "4工出签",
                }
                expedited_type_display = expedited_map.get(
                    stored_expedited_type, stored_expedited_type
                )
                stored_expedited_count += 1
                logger.debug(
                    f"🎯 使用存储的加急类型: {stored_expedited_type} → {expedited_type_display}"
                )
            else:
                logger.debug(f"⚠️ 订单 {order.get('order_no', 'N/A')} 没有加急类型数据")
            # 没有存储数据则保持为空，不做任何推断

            # 处理姓名显示 - 分离中文姓名和拼音
            # 优先从form_data获取，如果没有则从order直接获取
            chinese_name = form_data.get("chinese_name", "") or order.get(
                "chinese_name", ""
            )
            surname = form_data.get("surname", "") or order.get("surname", "")
            given_name = form_data.get("given_name", "") or order.get("given_name", "")
            english_name = (
                f"{surname} {given_name}".strip() if surname or given_name else ""
            )

            # 格式化创建时间 - 只保留到秒
            created_at = order.get("created_at", "")
            if created_at:
                try:
                    if hasattr(created_at, "strftime"):
                        formatted_time = created_at.strftime("%Y-%m-%d %H:%M:%S")
                    else:
                        # 如果是字符串，尝试解析并格式化
                        dt = datetime.fromisoformat(
                            str(created_at).replace("Z", "+00:00")
                        )
                        formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
                except (ValueError, TypeError, AttributeError, OSError):
                    formatted_time = str(created_at)[:19]  # 截取前19个字符
            else:
                formatted_time = ""

            # 构建数据行（修复字段名映射）
            data.append(
                {
                    "序号": index,
                    "订单编号": order.get("order_no", ""),
                    "申请编号": order.get("vietnam_application_number", ""),
                    "护照号码": order.get("passport_number", ""),
                    "中文姓名": chinese_name,
                    "姓名拼音": english_name,
                    "出生日期": str(order.get("date_of_birth", "")),
                    "邮箱": form_data.get("email", "") or order.get("email", ""),
                    "电话": form_data.get("telephone_number", "")
                    or order.get("telephone_number", ""),
                    "签证类型": VISA_TYPE_MAP.get(
                        form_data.get("visa_entry_type", ""),
                        form_data.get("visa_entry_type", ""),
                    ),
                    "入境口岸": ENTRY_GATE_MAP.get(
                        form_data.get("intended_entry_gate", ""),
                        form_data.get("intended_entry_gate", ""),
                    ),
                    "生效日期": form_data.get("visa_start_date", ""),
                    "签证有效期": form_data.get("visa_validity_duration", "")
                    or form_data.get("visa_duration", ""),
                    "出行目的": PURPOSE_OF_ENTRY_MAP.get(
                        form_data.get("purpose_of_entry", ""),
                        form_data.get("purpose_of_entry", ""),
                    ),
                    "加急类型": expedited_type_display,
                    "签证状态": VISA_STATUS_MAP.get(
                        form_data.get("visa_status")
                        or app_data.get("visa_status")
                        or order.get("visa_status", ""),
                        "",
                    ),
                    "创建时间": formatted_time,
                }
            )

        # 创建DataFrame
        df = pd.DataFrame(data)
        logger.info(f"📊 DataFrame创建成功，行数: {len(df)}")

        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx") as temp_file:
            temp_path = temp_file.name
        logger.info(f"📁 临时文件创建: {temp_path}")

        # 写入Excel
        with pd.ExcelWriter(temp_path, engine="openpyxl") as writer:
            df.to_excel(writer, sheet_name="订单数据", index=False)
        logger.info("📝 Excel基本写入完成")

        # 格式化Excel
        workbook = load_workbook(temp_path)
        worksheet = workbook["订单数据"]

        # 标题行格式化
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(
            start_color="4F81BD", end_color="4F81BD", fill_type="solid"
        )
        header_alignment = Alignment(horizontal="center", vertical="center")

        for cell in worksheet[1]:
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment

        # 智能自动调整列宽 - 在原有基础上增加一些宽度余量
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except (TypeError, AttributeError, ValueError):
                    pass
            # 在原有长度基础上增加更多余量：+5个字符，最小宽度12，最大宽度35
            adjusted_width = min(max(max_length + 3, 10), 25)
            worksheet.column_dimensions[column_letter].width = adjusted_width

        # 冻结标题行
        worksheet.freeze_panes = "A2"

        # 添加筛选功能 - 对整个数据范围启用筛选
        if len(df) > 0:
            worksheet.auto_filter.ref = f"A1:{chr(64 + len(df.columns))}{len(df) + 1}"

        workbook.save(temp_path)
        logger.info("🎨 Excel格式化完成")

        # 生成文件名 - 基于筛选日期范围
        if date_from and date_to:
            if date_from == date_to:
                # 单日筛选
                date_str = date_from.replace("-", "")  # 2025-06-22 -> 20250622
                filename = f"越南签证订单表_{date_str}.xlsx"
            else:
                # 日期范围筛选
                date_from_str = date_from.replace("-", "")  # 2025-06-01 -> 20250601
                date_to_str = date_to.replace("-", "")  # 2025-06-22 -> 20250622
                filename = f"越南签证订单表_{date_from_str}-{date_to_str}.xlsx"
        elif date_from:
            # 只有开始日期
            date_str = date_from.replace("-", "")
            filename = f"越南签证订单表_{date_str}起.xlsx"
        elif date_to:
            # 只有结束日期
            date_str = date_to.replace("-", "")
            filename = f"越南签证订单表_至{date_str}.xlsx"
        else:
            # 无日期筛选，使用当前时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"越南签证订单表_全部_{timestamp}.xlsx"

        # 读取文件内容
        with open(temp_path, "rb") as f:
            excel_content = f.read()

        logger.info(f"📖 Excel文件读取完成，大小: {len(excel_content)} bytes")

        # 删除临时文件
        os.unlink(temp_path)
        logger.info("🗑️ 临时文件已清理")

        # 创建响应 - 使用URL编码的文件名避免编码问题
        from urllib.parse import quote

        encoded_filename = quote(filename.encode("utf-8"))
        response = StreamingResponse(
            io.BytesIO(excel_content),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}"
            },
        )

        logger.info(f"✅ Excel文件生成成功: {filename}")
        return response

    except Exception as e:
        logger.error(f"❌ Excel导出失败: {str(e)}")
        raise


def _validate_export_parameters(
    format: str, date_from: str | None, date_to: str | None
) -> bool:
    """
    验证导出参数

    Args:
        format: 导出格式
        date_from: 开始日期
        date_to: 结束日期

    Returns:
        bool: 参数是否有效
    """
    try:
        # 验证格式 - 现在只支持excel
        if format.lower() != "excel":
            logger.warning(f"⚠️ 无效的导出格式: {format}，仅支持excel")
            return False

        # 验证日期格式
        if date_from:
            try:
                datetime.strptime(date_from, "%Y-%m-%d")
            except ValueError:
                logger.warning(f"⚠️ 无效的开始日期格式: {date_from}")
                return False

        if date_to:
            try:
                datetime.strptime(date_to, "%Y-%m-%d")
            except ValueError:
                logger.warning(f"⚠️ 无效的结束日期格式: {date_to}")
                return False

        # 验证日期范围
        if date_from and date_to:
            start_date = datetime.strptime(date_from, "%Y-%m-%d")
            end_date = datetime.strptime(date_to, "%Y-%m-%d")
            if start_date > end_date:
                logger.warning(f"⚠️ 开始日期大于结束日期: {date_from} > {date_to}")
                return False

        logger.info("✅ 导出参数验证通过")
        return True

    except Exception as e:
        logger.error(f"❌ 导出参数验证失败: {str(e)}")
        return False
