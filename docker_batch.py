import contextlib
import os
import sys

from dotenv import load_dotenv

sys.path.append(os.path.abspath("."))
from app.batch_processing.batch_applicant_importer import import_applicants_from_folder
from app.batch_processing.batch_runner import run_batch
from app.utils.logger_config import get_logger, setup_logger


# PostgreSQL数据库统计函数 - 同步包装器
def show_db_stats():
    """显示数据库统计信息"""
    try:
        import asyncio

        from backend.db_config.unified_connection import get_unified_db

        async def _async_show_stats():
            # 获取数据库健康状态
            unified_db = await get_unified_db()
            # 执行健康检查
            is_healthy = await unified_db.check_health()
            print(f"📊 数据库健康状态: {'正常' if is_healthy else '异常'}")
            print(f"📊 连接状态: {'正常' if unified_db.is_connected() else '异常'}")
            print(f"📊 数据库类型: {unified_db.get_database_type()}")
            print(f"📊 数据库版本: {unified_db.get_database_version()}")
            print(f"📊 数据库连接数: {unified_db.get_connection_count()}")
            print(
                f"📊 数据库连接池状态: {'正常' if unified_db.is_connection_pool_healthy() else '异常'}"
            )
            print(f"📊 数据库连接池大小: {unified_db.get_connection_pool_size()}")
            print(
                f"📊 数据库连接池最大大小: {unified_db.get_connection_pool_max_size()}"
            )
            print(
                f"📊 数据库连接池最小大小: {unified_db.get_connection_pool_min_size()}"
            )
            print(
                f"📊 数据库连接池空闲大小: {unified_db.get_connection_pool_idle_size()}"
            )

            # 获取数据库统计（简化版）
            try:
                stats = await unified_db.get_stats()
                print(
                    f"📊 数据库统计: {stats['applicants']} 位申请人, {stats['tasks']} 条签证任务"
                )
                print(
                    f"📊 任务状态: {stats['success']} 条成功提交, {stats['downloaded']} 条已下载签证"
                )
            except Exception as e:
                print(f"❌ 获取数据库统计失败: {e}")

        asyncio.run(_async_show_stats())
    except Exception as e:
        print(f"❌ 数据库统计获取失败: {e}")


def verify_recent_records(limit: int = 5):
    """验证最近记录"""
    try:
        import asyncio

        from backend.db_config.unified_connection import get_unified_db

        async def _async_verify():
            unified_db = await get_unified_db()
            try:
                # 简单的连接验证
                result = await unified_db.execute_query("SELECT 1 as test")
                if result and result["test"] == 1:
                    print(f"📊 连接验证成功: {result}")
                else:
                    print("❌ 连接验证失败")
            except Exception as e:
                print(f"❌ 连接验证失败: {e}")

        asyncio.run(_async_verify())
    except Exception as e:
        print(f"❌ 记录验证失败: {e}")


# 加载环境变量
load_dotenv()

# 设置日志级别
setup_logger(console_level="DEBUG")

# 设置控制台输出编码为 UTF-8
with contextlib.suppress(Exception):
    sys.stdout.reconfigure(encoding="utf-8")

if __name__ == "__main__":
    logger = get_logger()
    logger.info("批处理程序启动...")

    # 显示数据库统计信息
    show_db_stats()
    verify_recent_records(5)

    # 从文件夹导入申请人
    folder = r"/app/test_data"
    logger.info(f"开始从文件夹导入申请人: {folder}")
    applicants = import_applicants_from_folder(folder)
    logger.info(f"✅ 共导入 {len(applicants)} 位申请人")

    # 运行批处理
    run_batch(applicants, max_concurrent=4, launch_window=30)
    logger.info("✅批处理任务全部完成,处理结果请查看日志文件")
