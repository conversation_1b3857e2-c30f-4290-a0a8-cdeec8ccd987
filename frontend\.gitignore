# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
.DS_Store
dist
dist-ssr
coverage
*.local

/cypress/videos/
/cypress/screenshots/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

*.tsbuildinfo

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# OS generated files
Thumbs.db
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Build outputs
build/
dist/
out/

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Package manager files
yarn.lock
pnpm-lock.yaml

# TypeScript
*.tsbuildinfo

# Testing
coverage/
.nyc_output/

# Storybook
storybook-static/

# Auto-generated files
auto-imports.d.ts
components.d.ts
