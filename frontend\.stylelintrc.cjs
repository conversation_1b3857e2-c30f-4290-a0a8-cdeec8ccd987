module.exports = {
  extends: [
    'stylelint-config-standard-vue' // 🎯 Vue CSS 标准套餐
  ],
  rules: {
    // 🎯 遵循最佳实践：初期留空，让套餐规则生效
    // 只有在特殊需求时才添加自定义规则

    // 允许Element Plus等UI库的类名格式
    'selector-class-pattern': [
      '^([a-z][a-z0-9]*(-[a-z0-9]+)*|el-[a-z0-9-]+(__[a-z0-9-]+)*(--[a-z0-9-]+)*)$',
      {
        message: 'Expected class selector to be kebab-case or Element Plus format'
      }
    ],

    // 允许keyframe使用驼峰命名
    'keyframes-name-pattern': [
      '^([a-z][a-z0-9]*(-[a-z0-9]+)*|[a-z][a-zA-Z0-9]*)$',
      {
        message: 'Expected keyframe name to be kebab-case or camelCase'
      }
    ]
  }
}
