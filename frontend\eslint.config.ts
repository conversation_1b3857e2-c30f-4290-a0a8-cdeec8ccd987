import pluginVitest from '@vitest/eslint-plugin'
import skipFormatting from '@vue/eslint-config-prettier/skip-formatting'
import { defineConfigWithVueTs, vueTsConfigs } from '@vue/eslint-config-typescript'
import pluginVue from 'eslint-plugin-vue'

export default defineConfigWithVueTs(
  {
    name: 'app/files-to-lint',
    files: ['**/*.{ts,mts,tsx,vue}'],
  },

  {
    ignores: ['**/dist/**', '**/dist-ssr/**', '**/coverage/**'],
  },

  pluginVue.configs['flat/essential'],
  vueTsConfigs.recommended,

  {
    ...pluginVitest.configs.recommended,
    files: ['src/**/__tests__/*'],
  },

  // 自定义规则配置
  {
    rules: {
      '@typescript-eslint/no-explicit-any': 'error', // 🔧 修复：严格禁止any类型
      '@typescript-eslint/no-unused-vars': 'warn', // 将未使用变量降级为警告
      'vue/multi-word-component-names': 'off', // 关闭组件名必须多单词的限制
      '@typescript-eslint/prefer-promise-reject-errors': 'off', // 允许reject非Error对象
      '@typescript-eslint/prefer-nullish-coalescing': 'warn', // 建议使用??而不是||
      '@typescript-eslint/no-empty-object-type': 'off', // 暂时关闭空对象类型检查
    },
  },

  skipFormatting,
)
