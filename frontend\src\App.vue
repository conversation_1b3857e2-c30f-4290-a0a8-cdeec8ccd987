<script setup lang="ts">
import { useAuthStore } from '@/stores/auth'
import { useNotificationStore } from '@/stores/notification'
import { useSessionStore } from '@/stores/session'
import { redirectToLogin } from '@/utils/navigation'
import {
  ArrowDown,
  Bell,
  DataAnalysis,
  Document,
  List,
  Menu,
  Setting,
  SwitchButton,
  User,
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'
import { RouterView, useRoute, useRouter } from 'vue-router'

// 路由信息
const route = useRoute()
const router = useRouter()

// Store状态 - 延迟初始化
let authStore: ReturnType<typeof useAuthStore>

// 侧边栏状态
const isCollapse = ref(false)

// Store是否已初始化
const storeReady = ref(false)

// 用户信息
const userInfo = computed(() => {
  if (!storeReady.value || !authStore) {
    return {
      username: 'Loading...',
      avatar: 'L',
      email: '<EMAIL>',
    }
  }

  return {
    username: authStore.user?.username ?? 'Admin',
    avatar: authStore.user?.username?.[0]?.toUpperCase() ?? 'A',
    email: authStore.user?.email ?? '<EMAIL>',
  }
})

// 是否显示主系统布局（登录页面不显示）
const showMainLayout = computed(() => {
  if (!storeReady.value || !authStore) {
    return false
  }
  return route.name !== 'login' && authStore.isAuthenticated
})

// 面包屑数据
const breadcrumbList = computed(() => {
  const breadcrumbs = [{ title: '越南签证申请系统', path: '/' }]

  if (route.meta?.title) {
    breadcrumbs.push({ title: route.meta.title as string, path: route.path })
  }

  return breadcrumbs
})

// 组件挂载后初始化store
onMounted(() => {
  try {
    authStore = useAuthStore()
    storeReady.value = true
    console.log('[App] Store初始化完成')
  } catch (error) {
    console.error('[App] Store初始化失败:', error)
  }

  // 🔧 修复：先初始化通知系统
  const notificationStore = useNotificationStore()

  // 全局监听会话过期，自动提示并跳转
  const sessionStore = useSessionStore()
  watch(
    () => sessionStore.isSessionExpired,
    (expired) => {
      if (expired) {
        notificationStore.showAuthError('登录已过期，请重新登录')
        redirectToLogin()
      }
    },
    { immediate: true },
  )

  // 监听错误消息
  watch(
    () => notificationStore.lastError,
    (error) => {
      if (error) {
        ElMessage.error({
          message: error,
          duration: 4000,
          showClose: true,
        })
        notificationStore.clearError()
      }
    },
    { immediate: true },
  )

  // 监听成功消息
  watch(
    () => notificationStore.lastSuccess,
    (success) => {
      if (success) {
        ElMessage.success({
          message: success,
          duration: 3000,
          showClose: true,
        })
        notificationStore.clearSuccess()
      }
    },
    { immediate: true },
  )

  // 监听警告消息
  watch(
    () => notificationStore.lastWarning,
    (warning) => {
      if (warning) {
        ElMessage.warning({
          message: warning,
          duration: 3000,
          showClose: true,
        })
        notificationStore.clearWarning()
      }
    },
    { immediate: true },
  )

  // 监听信息消息
  watch(
    () => notificationStore.lastInfo,
    (info) => {
      if (info) {
        ElMessage.info({
          message: info,
          duration: 3000,
          showClose: true,
        })
        notificationStore.clearInfo()
      }
    },
    { immediate: true },
  )
})

// 切换侧边栏
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

// 用户菜单操作
const handleCommand = async (command: string) => {
  if (!authStore) {
    console.warn('[App] AuthStore未初始化')
    return
  }

  switch (command) {
    case 'profile':
      console.log('查看个人信息')
      break
    case 'settings':
      console.log('系统设置')
      break
    case 'logout':
      // 执行登出
      await authStore.logout()
      // 重定向到登录页
      router.push('/login')
      break
  }
}
</script>

<template>
  <div id="app">
    <!-- 登录页面：纯净布局 -->
    <template v-if="!showMainLayout">
      <RouterView />
    </template>

    <!-- 主系统页面：完整布局 -->
    <template v-else>
      <el-container class="layout-container">
        <!-- 左侧导航栏 -->
        <el-aside :width="isCollapse ? '64px' : '160px'" class="sidebar">
          <div class="logo-container">
            <div class="logo">
              <span v-if="!isCollapse" class="logo-text">签证自动化系统</span>
              <span v-else class="logo-mini">V</span>
            </div>
          </div>

          <el-scrollbar class="sidebar-scrollbar">
            <el-menu
              :default-active="route.path"
              class="sidebar-menu"
              :collapse="isCollapse"
              background-color="#2c3e50"
              text-color="#bdc3c7"
              active-text-color="#18bc9c"
              router
            >
              <el-menu-item index="/visa-form">
                <el-icon><Document /></el-icon>
                <span>申请签证</span>
              </el-menu-item>

              <el-menu-item index="/order-management">
                <el-icon><List /></el-icon>
                <span>订单管理</span>
              </el-menu-item>

              <el-menu-item index="/dashboard">
                <el-icon><DataAnalysis /></el-icon>
                <span>数据统计</span>
              </el-menu-item>
            </el-menu>
          </el-scrollbar>
        </el-aside>

        <!-- 右侧主内容区域 -->
        <el-container class="main-container">
          <!-- 顶部导航栏 -->
          <el-header class="header">
            <!-- 左侧：菜单切换 + 面包屑 -->
            <div class="header-left">
              <el-button type="text" @click="toggleSidebar" class="sidebar-toggle">
                <el-icon><Menu /></el-icon>
              </el-button>
              <div class="system-title">Vietnam Visa System</div>
              <el-divider direction="vertical" />
              <el-breadcrumb separator="/" class="breadcrumb">
                <el-breadcrumb-item
                  v-for="(item, index) in breadcrumbList"
                  :key="index"
                  :to="index === breadcrumbList.length - 1 ? undefined : item.path"
                >
                  {{ item.title }}
                </el-breadcrumb-item>
              </el-breadcrumb>
            </div>

            <!-- 右侧：用户菜单 -->
            <div class="header-right">
              <!-- 通知 -->
              <el-button type="text" class="header-action">
                <el-badge :value="3" class="item">
                  <el-icon><Bell /></el-icon>
                </el-badge>
              </el-button>

              <!-- 设置 -->
              <el-button type="text" class="header-action">
                <el-icon><Setting /></el-icon>
              </el-button>

              <!-- 用户下拉菜单 -->
              <el-dropdown @command="handleCommand" class="user-dropdown">
                <div class="user-info">
                  <div class="user-avatar">{{ userInfo.avatar }}</div>
                  <span class="username">{{ userInfo.username }}</span>
                  <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="profile">
                      <el-icon><User /></el-icon>
                      个人信息
                    </el-dropdown-item>
                    <el-dropdown-item command="settings">
                      <el-icon><Setting /></el-icon>
                      系统设置
                    </el-dropdown-item>
                    <el-dropdown-item divided command="logout">
                      <el-icon><SwitchButton /></el-icon>
                      退出登录
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </el-header>

          <!-- 主内容区 -->
          <el-main class="main-content">
            <div class="content-wrapper">
              <RouterView />
            </div>
          </el-main>
        </el-container>
      </el-container>
    </template>
  </div>
</template>

<style scoped lang="scss">
#app {
  height: 100vh;
  width: 100%;
}

.layout-container {
  height: 100%;

  .sidebar {
    background: #2c3e50;
    border-right: 1px solid #34495e;

    .logo-container {
      height: 64px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #34495e;
      border-bottom: 1px solid #4a5f7a;

      .logo {
        color: #ecf0f1;
        font-weight: 600;

        .logo-text {
          font-size: 16px;
        }

        .logo-mini {
          font-size: 18px;
          font-weight: 700;
        }
      }
    }

    .sidebar-scrollbar {
      height: calc(100% - 64px);

      .sidebar-menu {
        border: none;

        .el-menu-item {
          height: 48px;
          line-height: 48px;

          &:hover {
            background-color: #34495e !important;
          }

          &.is-active {
            background-color: #18bc9c !important;
            color: white !important;
          }
        }
      }
    }
  }

  .main-container {
    .header {
      background: white;
      border-bottom: 1px solid #e4e7ed;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;

        .sidebar-toggle {
          color: #606266;
          font-size: 18px;

          &:hover {
            color: #409eff;
          }
        }

        .system-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }

        .breadcrumb {
          font-size: 14px;
        }
      }

      .header-right {
        display: flex;
        align-items: center;
        gap: 8px;

        .header-action {
          color: #606266;
          padding: 8px;

          &:hover {
            color: #409eff;
            background: #f5f7fa;
          }
        }

        .user-dropdown {
          margin-left: 8px;

          .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
              background: #f5f7fa;
            }

            .user-avatar {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              background: #409eff;
              color: white;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 14px;
              font-weight: 600;
            }

            .username {
              font-size: 14px;
              color: #303133;
              font-weight: 500;
            }

            .dropdown-icon {
              color: #909399;
              font-size: 12px;
            }
          }
        }
      }
    }

    .main-content {
      background: #f5f7fa;
      padding: 0;

      .content-wrapper {
        height: 100%;
        padding: 20px;
        overflow-y: auto;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 0 12px !important;

    .header-left {
      gap: 8px !important;

      .system-title {
        display: none;
      }
    }

    .header-right {
      .username {
        display: none;
      }
    }
  }

  .sidebar {
    position: fixed !important;
    z-index: 1000;
    height: 100vh !important;
  }

  .main-container {
    margin-left: 0 !important;
  }

  .content-wrapper {
    padding: 12px !important;
  }
}
</style>
