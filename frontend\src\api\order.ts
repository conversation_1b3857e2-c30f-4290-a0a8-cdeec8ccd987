import { api } from './request'
import type {
  ApiResponse,
  CreateOrderRequest,
  CreateOrderResponse,
  CreateOrderResponseData,
  OrderDetailResponse,
  OrderDetailResponseData,
  OrderQueryResponse,
  OrderQueryResponseData,
  OrderStatus,
  UpdateOrderRequest,
} from './types'

// 订单查询参数
export interface OrderQueryParams {
  page?: number
  limit?: number
  status?: OrderStatus
  order_no?: string
  application_number?: string
  date_from?: string
  date_to?: string
}

/**
 * 订单管理API
 * 统一使用request.ts的api对象，使用统一的拦截器和错误处理
 */
export const orderApi = {
  /**
   * 创建新订单
   * 支持同一申请人多次申请（签证过期、被拒重申等场景）
   */
  async createOrder(request: CreateOrderRequest): Promise<CreateOrderResponse> {
    return api.post<CreateOrderResponseData>('/api/visa/orders/create', request)
  },

  /**
   * 查询订单列表
   * 支持多条件筛选和分页
   */
  async queryOrders(params: OrderQueryParams = {}): Promise<OrderQueryResponse> {
    const queryParams = {
      page: params.page ?? 1,
      limit: params.limit ?? 20,
      ...(params.status && { status: params.status }),
      ...(params.order_no && { order_no: params.order_no }),
      ...(params.application_number && { application_number: params.application_number }),
      ...(params.date_from && { date_from: params.date_from }),
      ...(params.date_to && { date_to: params.date_to }),
    }

    return api.get<OrderQueryResponseData>('/api/visa/orders/query', {
      params: queryParams,
    })
  },

  /**
   * 获取订单详情
   * 包含完整订单信息和状态变更历史
   */
  async getOrderDetail(orderNo: string): Promise<OrderDetailResponse> {
    return api.get<OrderDetailResponseData>(`/api/visa/orders/${orderNo}/detail`)
  },

  /**
   * 更新订单信息
   * 主要用于更新越南官方编号、状态等
   */
  async updateOrder(orderNo: string, request: UpdateOrderRequest): Promise<ApiResponse> {
    return api.put<ApiResponse>(`/api/visa/orders/${orderNo}/update`, request)
  },

  /**
   * 重试订单处理
   * 用于重新提交失败的订单
   */
  async retryOrder(orderNo: string): Promise<ApiResponse> {
    return api.post<ApiResponse>(`/api/visa/orders/${orderNo}/retry`)
  },

  /**
   * 根据申请编号获取订单
   */
  async getOrderByApplicationNumber(applicationNumber: string): Promise<OrderDetailResponse> {
    return api.get<OrderDetailResponseData>(`/api/visa/orders/by-application/${applicationNumber}`)
  },

  /**
   * 获取用户订单列表
   */
  async getUserOrders(params: { page?: number; limit?: number } = {}): Promise<OrderQueryResponse> {
    const queryParams = {
      page: params.page ?? 1,
      limit: params.limit ?? 20,
    }
    return api.get<OrderQueryResponseData>('/api/visa/orders/user', {
      params: queryParams,
    })
  },

  /**
   * 获取订单状态变更历史
   */
  async getOrderStatusHistory(orderNo: string): Promise<ApiResponse> {
    return api.get<ApiResponse>(`/api/visa/orders/${orderNo}/status-history`)
  },

  /**
   * 获取订单统计数据
   */
  async getOrderStats(): Promise<ApiResponse> {
    return api.get<ApiResponse>('/api/visa/orders/stats')
  },

  /**
   * 取消订单
   */
  async cancelOrder(orderNo: string, reason?: string): Promise<ApiResponse> {
    return api.post<ApiResponse>(`/api/visa/orders/${orderNo}/cancel`, { reason })
  },
}

// 默认导出
export default orderApi
