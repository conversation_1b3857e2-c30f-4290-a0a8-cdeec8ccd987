import { useAuthStore } from '@/stores/auth'
import { redirectToLogin } from '@/utils/navigation'
import type { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import axios from 'axios'

// API错误类型定义
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
    public details?: any,
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

// 网络错误类型定义
export class NetworkError extends Error {
  constructor(message: string = '网络连接失败') {
    super(message)
    this.name = 'NetworkError'
  }
}

// 认证错误类型定义
export class AuthError extends Error {
  constructor(message: string = '认证失败') {
    super(message)
    this.name = 'AuthError'
  }
}

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器：自动添加认证token
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const authStore = useAuthStore()
    const token = authStore.token

    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(new NetworkError('请求配置失败'))
  },
)

// 响应拦截器：统一处理响应和错误
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response.data
  },
  async (error) => {
    console.error('📛 API错误:', error)

    // 🔥 网络错误处理（不调用UI）
    if (!error.response) {
      // 网络连接问题
      if (error.code === 'ECONNABORTED') {
        throw new NetworkError('请求超时，请检查网络连接')
      } else if (error.code === 'ERR_NETWORK') {
        throw new NetworkError('网络连接失败，请检查网络设置')
      } else {
        throw new NetworkError('网络请求失败')
      }
    }

    const { status, data } = error.response

    // 🔥 HTTP状态码错误处理（不调用UI）
    switch (status) {
      case 401: {
        const authStore = useAuthStore()
        authStore.clearAuth()

        // 🔧 修复：使用统一的导航机制
        redirectToLogin()

        throw new AuthError('登录已过期，请重新登录')
      }

      case 403:
        throw new ApiError('权限不足', status, 'FORBIDDEN')

      case 404:
        throw new ApiError('请求的资源不存在', status, 'NOT_FOUND')

      case 422: {
        const validationMessage = data?.detail || '请求参数验证失败'
        throw new ApiError(validationMessage, status, 'VALIDATION_ERROR', data?.detail)
      }

      case 500:
        throw new ApiError('服务器内部错误', status, 'INTERNAL_ERROR')

      case 502:
      case 503:
      case 504:
        throw new ApiError('服务暂时不可用，请稍后重试', status, 'SERVICE_UNAVAILABLE')

      default: {
        const defaultMessage = data?.message || data?.detail || `请求失败 (${status})`
        throw new ApiError(defaultMessage, status, 'UNKNOWN_ERROR', data)
      }
    }
  },
)

// 🔥 通用请求方法 - 只抛出错误，不处理UI
const request = {
  get: <T = any>(url: string, config?: any): Promise<T> => {
    return api.get(url, config)
  },

  post: <T = any>(url: string, data?: any, config?: any): Promise<T> => {
    return api.post(url, data, config)
  },

  put: <T = any>(url: string, data?: any, config?: any): Promise<T> => {
    return api.put(url, data, config)
  },

  delete: <T = any>(url: string, config?: any): Promise<T> => {
    return api.delete(url, config)
  },

  patch: <T = any>(url: string, data?: any, config?: any): Promise<T> => {
    return api.patch(url, data, config)
  },

  upload: <T = any>(url: string, formData: FormData, config?: any): Promise<T> => {
    return api.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      ...config,
    })
  },
}

export { api, request }
