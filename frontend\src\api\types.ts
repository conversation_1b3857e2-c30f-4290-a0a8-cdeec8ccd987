// API响应基础接口
export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 会话检查响应
export interface SessionCheckResponse {
  valid: boolean
}

// 🔥 修复：重复检查响应 - 移除冗余的success字段，继承ApiResponse
export interface DuplicateCheckResponseData {
  exists: boolean
  can_resubmit?: boolean // 是否允许重新提交
  warning_type?: 'success' | 'pending' | 'rejected' | 'failed' | 'none' | 'error' // 警告类型
  application_number?: string
  submission_time?: string
  status?: string
  approval_status?: string
  order_no?: string
}

// 完整的重复检查响应
export interface DuplicateCheckResponse extends ApiResponse<DuplicateCheckResponseData> {}

// 🔥 修复：签证申请跟踪信息接口（提取内联接口）
export interface VisaApplicationTrackingInfo {
  submission_time: string
  expected_processing_days: string
  email?: string
  order_no?: string
  vietnam_application_number?: string
  is_duplicate?: boolean
  failure_reason?: string
}

// 🔥 修复：签证申请响应类型（移除冗余success字段，使用提取的接口）
export interface VisaApplicationResponse
  extends ApiResponse<{
    application_id?: string
    status: string
    tracking_info?: VisaApplicationTrackingInfo
  }> {}

// 🔥 修复：OCR识别响应数据 - 统一字段命名，移除重复字段
export interface OcrResponseData {
  surname?: string
  given_name?: string
  chinese_name?: string
  passport_number?: string
  date_of_birth?: string // 统一使用这个字段名
  place_of_birth?: string
  nationality?: string
  sex?: string
  date_of_issue?: string
  date_of_expiry?: string // 统一使用这个字段名
  place_of_issue?: string
  passport_type?: string
  [key: string]: unknown
}

// OCR识别完整响应（包装在ApiResponse中）
export interface OcrResponse extends ApiResponse<OcrResponseData> {}

// ==================== 订单管理相关类型 ====================

// 订单状态枚举 - 根据修正后的业务流程
export type OrderStatus =
  | 'created' // 订单已创建
  | 'cancelled' // 订单已取消
  | 'processing' // 提交中
  | 'success' // 提交成功（自动化填表成功，获得越南官方编号）
  | 'pending_approve' // 等待审批（success之后的状态）
  | 'approved' // 已审批
  | 'pending_download' // 等待下载
  | 'downloaded' // 已下载
  | 'paid' // 已付款（付款成功）
  | 'failed' // 失败
  | 'unknown' // 未知状态
  | 'reserved1' // 扩展状态1
  | 'reserved2' // 扩展状态2
  | 'reserved3' // 扩展状态3

// 🔥 改进：定义更精确的申请数据类型，而不是使用通用Record
export interface VisaApplicationData {
  // 个人信息
  personal_info: {
    surname: string
    given_name: string
    chinese_name?: string
    date_of_birth: string
    place_of_birth: string
    nationality: string
    sex: 'M' | 'F'
  }
  // 护照信息
  passport_info: {
    passport_number: string
    date_of_issue: string
    date_of_expiry: string
    place_of_issue: string
    passport_type?: string
  }
  // 旅行信息
  travel_info: {
    purpose_of_visit: string
    duration_of_stay: number
    entry_date: string
    exit_date: string
    entry_port?: string
  }
  // 联系信息
  contact_info: {
    email: string
    phone?: string
    address?: string
  }
  // 其他信息（保留扩展性）
  additional_info?: Record<string, unknown>
}

// 订单信息接口
export interface OrderInfo {
  // 核心编号
  order_no: string // 我们的订单编号
  application_number?: string // 越南官方编号

  // 基本信息
  user_id: number
  applicant_name: string
  passport_number: string
  date_of_birth: string

  // 状态信息
  status: OrderStatus
  error_message?: string
  last_error_at?: string

  // 重试信息
  retry_count: number
  max_retry_count: number

  // 🔥 改进：使用更精确的类型而不是通用Record
  application_data: VisaApplicationData

  // 时间信息
  created_at: string
  updated_at: string
  submitted_at?: string
  approved_at?: string
  completed_at?: string
}

// 订单状态历史
export interface OrderStatusHistory {
  id: number
  order_no: string
  from_status?: string
  to_status: string
  reason?: string
  operator_id?: number
  operator_type: 'system' | 'admin' | 'user'
  metadata?: Record<string, unknown>
  created_at: string
}

// 创建订单请求
export interface CreateOrderRequest {
  // 幂等性标识数据
  applicant_name: string
  passport_number: string
  date_of_birth: string

  // 🔥 改进：使用精确类型
  application_data: VisaApplicationData
}

// 创建订单响应数据（业务数据部分）
export interface CreateOrderResponseData {
  order_no: string
  status: OrderStatus
  created_at: string
}

// 创建订单完整响应
export interface CreateOrderResponse extends ApiResponse<CreateOrderResponseData> {}

// 订单查询响应数据（业务数据部分）
export interface OrderQueryResponseData {
  orders: OrderInfo[]
  pagination: PaginationInfo
}

// 订单查询完整响应
export interface OrderQueryResponse extends ApiResponse<OrderQueryResponseData> {}

// 订单详情响应数据（业务数据部分）
export interface OrderDetailResponseData {
  order: OrderInfo
  status_history: OrderStatusHistory[]
}

// 订单详情完整响应
export interface OrderDetailResponse extends ApiResponse<OrderDetailResponseData> {}

// 订单更新请求
export interface UpdateOrderRequest {
  application_number?: string // 更新越南官方编号
  status?: OrderStatus // 更新状态
  error_message?: string // 错误信息
  metadata?: Record<string, unknown> // 额外数据
}

// 分页信息
export interface PaginationInfo {
  current_page: number
  total_pages: number
  total_items: number
  has_prev: boolean
  has_next: boolean
  page_size: number
}

// API错误接口
export interface ApiError {
  status: number
  message: string
  details?: unknown
}
