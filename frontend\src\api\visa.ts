import { request } from './request'
import type {
  ApiResponse,
  DuplicateCheckResponse,
  OcrResponseData,
  VisaApplicationResponse,
} from './types'

// 签证申请API
export const visaApi = {
  // 检查重复提交，严格对应旧版 /api/visa/check-duplicate/:passport_number
  checkDuplicate: (passportNumber: string): Promise<ApiResponse<DuplicateCheckResponse>> => {
    return request.get(`/api/visa/check-duplicate/${passportNumber}`)
  },

  // 提交签证申请，严格对应旧版 /api/visa/apply
  submitApplication: (formData: FormData): Promise<ApiResponse<VisaApplicationResponse>> => {
    return request.upload('/api/visa/apply', formData)
  },

  // OCR护照识别，统一使用 /ocr-passport/ 端点
  ocrPassport: (file: File, signal?: AbortSignal): Promise<ApiResponse<OcrResponseData>> => {
    const formData = new FormData()
    formData.append('passport_scan', file) // 匹配后端期望的字段名

    // 使用统一的 /ocr-passport/ 端点，依赖request.ts的统一错误处理
    return request.upload('/ocr-passport/', formData, {
      signal, // 支持取消请求
      timeout: 60000, // OCR处理可能需要更长时间
    })
  },

  // 下载申请结果文件
  downloadResult: (applicationNumber: string): Promise<ApiResponse<Blob>> => {
    return request.get(`/api/visa/download/${applicationNumber}`, {
      responseType: 'blob',
    })
  },
}
