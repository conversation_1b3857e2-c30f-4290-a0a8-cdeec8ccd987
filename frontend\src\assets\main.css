@import './base.css';

#app {
  width: 100%;
  min-height: 100vh;
  font-weight: normal;
  margin: 0;
  padding: 0;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

/* OCR状态指示器全局样式 */
.ocr-status-indicator {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #0ea5e9;
  border-radius: 8px;
  padding: 12px;
  animation: ocr-pulse 2s infinite;
}

.ocr-status-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.ocr-loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e0f2fe;
  border-top: 2px solid #0ea5e9;
  border-radius: 50%;
  animation: ocr-spin 1s linear infinite;
}

.ocr-status-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.ocr-main-text {
  font-size: 14px;
  font-weight: 500;
  color: #0369a1;
}

.ocr-file-name {
  font-size: 12px;
  color: #64748b;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ocr-cancel-btn {
  width: 24px;
  height: 24px;
  min-height: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ocr-cancel-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

/* OCR错误指示器样式 */
.ocr-error-indicator {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border: 1px solid #f87171;
  border-radius: 8px;
  padding: 12px;
  margin-top: 8px;
}

.ocr-error-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.ocr-error-icon {
  font-size: 20px;
  color: #dc2626;
}

.ocr-error-text {
  flex: 1;
  font-size: 14px;
  color: #dc2626;
}

.ocr-retry-btn {
  background: #f59e0b;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ocr-retry-btn:hover {
  background: #d97706;
}

/* 动画定义 */
@keyframes ocr-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes ocr-pulse {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(14, 165, 233, 0.3);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(14, 165, 233, 0);
  }
}
