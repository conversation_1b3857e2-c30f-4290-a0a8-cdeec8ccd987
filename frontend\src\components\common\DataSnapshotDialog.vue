<template>
  <div class="snapshot-dialog">
    <el-card class="file-upload-section" shadow="hover">
      <template #header>
        <div class="section-header">
          <span class="title">签证申请信息</span>
        </div>
      </template>
      <div class="double-layout">
        <div class="left-column">
          <div class="file-preview-vertical">
            <div class="image-item">
              <img
                v-if="snapshot?.files?.portrait_preview"
                :src="String(snapshot.files.portrait_preview)"
                alt="Portrait"
              />
              <div v-else class="no-image">未上传</div>
            </div>
            <div class="image-item">
              <el-image
                v-if="snapshot?.files?.passport_preview"
                :src="String(snapshot.files.passport_preview)"
                :preview-src-list="[String(snapshot.files.passport_preview)]"
                fit="cover"
                style="
                  width: 140px;
                  height: 140px;
                  border-radius: 4px;
                  border: 1px solid var(--el-border-color);
                  background: white;
                "
              />
              <div v-else class="no-image">未上传</div>
            </div>
          </div>
        </div>
        <div class="right-column">
          <div class="visa-info-section">
            <table class="info-table">
              <tbody>
                <tr>
                  <td class="label">签证类型</td>
                  <td class="value">
                    {{ visaTypeText(String(snapshot?.visaInfo?.visa_entry_type ?? '')) }}
                  </td>
                  <td class="label">签证有效期</td>
                  <td class="value">
                    {{ snapshot?.visaInfo?.visa_validity_duration || '未填写' }}
                  </td>
                  <td class="label">出行目的</td>
                  <td class="value">{{ purposeText(snapshot?.visaInfo?.purpose_of_entry) }}</td>
                </tr>
                <tr>
                  <td class="label">签证生效日期</td>
                  <td class="value">{{ snapshot?.visaInfo?.visa_start_date || '未填写' }}</td>
                  <td class="label">入境口岸</td>
                  <td class="value">
                    {{ entryGateText(String(snapshot?.visaInfo?.intended_entry_gate ?? '')) }}
                  </td>
                  <td class="label">近1年是否去过越南</td>
                  <td class="value">
                    {{ snapshot?.visaInfo?.visited_vietnam_last_year ? '是' : '否' }}
                  </td>
                </tr>
                <tr>
                  <td class="label">加急类型</td>
                  <td class="value">
                    {{ expeditedTypeText(String(snapshot?.visaInfo?.expedited_type ?? '')) }}
                  </td>
                  <td class="label">越南联系组织/个人</td>
                  <td class="value">{{ snapshot?.visaInfo?.has_vietnam_contact ? '有' : '无' }}</td>
                  <td colspan="2"></td>
                </tr>
                <tr v-if="snapshot?.visaInfo?.visited_vietnam_last_year">
                  <td class="label">之前访问日期</td>
                  <td class="value">{{ snapshot?.visaInfo?.previous_entry_date || '未填写' }}</td>
                  <td class="label">之前离开日期</td>
                  <td class="value">{{ snapshot?.visaInfo?.previous_exit_date || '未填写' }}</td>
                  <td class="label">之前访问目的</td>
                  <td class="value">{{ snapshot?.visaInfo?.previous_purpose || '未填写' }}</td>
                </tr>
                <tr v-if="snapshot?.visaInfo?.has_vietnam_contact">
                  <td class="label">组织名称</td>
                  <td class="value">
                    {{ snapshot?.visaInfo?.vietnam_contact_organization || '未填写' }}
                  </td>
                  <td class="label">联系电话</td>
                  <td class="value">{{ snapshot?.visaInfo?.vietnam_contact_phone || '未填写' }}</td>
                  <td class="label">联系地址</td>
                  <td class="value">
                    {{ snapshot?.visaInfo?.vietnam_contact_address || '未填写' }}
                  </td>
                </tr>
                <tr v-if="snapshot?.visaInfo?.has_vietnam_contact">
                  <td class="label">联系目的</td>
                  <td class="value">
                    {{ snapshot?.visaInfo?.vietnam_contact_purpose || '未填写' }}
                  </td>
                  <td colspan="4"></td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="personal-info-section">
            <div class="section-header">
              <el-icon><User /></el-icon>
              <span class="title">Personal Information 个人信息</span>
            </div>
            <div class="full-width-table">
              <table class="info-table">
                <tbody>
                  <tr>
                    <td class="label">姓</td>
                    <td class="value">{{ snapshot?.personalInfo?.surname || '未填写' }}</td>
                    <td class="label">名</td>
                    <td class="value">{{ snapshot?.personalInfo?.given_name || '未填写' }}</td>
                    <td class="label">中文名</td>
                    <td class="value">{{ snapshot?.personalInfo?.chinese_name || '未填写' }}</td>
                    <td class="label">Sex 性别</td>
                    <td class="value">
                      {{
                        snapshot?.personalInfo?.sex === 'M'
                          ? '男'
                          : snapshot?.personalInfo?.sex === 'F'
                            ? '女'
                            : '未填写'
                      }}
                    </td>
                  </tr>
                  <tr>
                    <td class="label">出生日期</td>
                    <td class="value">{{ snapshot?.personalInfo?.dob || '未填写' }}</td>
                    <td class="label">出生地</td>
                    <td class="value">{{ snapshot?.personalInfo?.place_of_birth || '未填写' }}</td>
                    <td class="label">国籍</td>
                    <td class="value">{{ snapshot?.personalInfo?.nationality || '未填写' }}</td>
                    <td class="label">宗教</td>
                    <td class="value">{{ snapshot?.personalInfo?.religion || '未填写' }}</td>
                  </tr>
                  <tr>
                    <td class="label">护照号码</td>
                    <td class="value">{{ snapshot?.passportInfo?.passport_number || '未填写' }}</td>
                    <td class="label">签发日期</td>
                    <td class="value">{{ snapshot?.passportInfo?.date_of_issue || '未填写' }}</td>
                    <td class="label">签发地</td>
                    <td class="value">{{ snapshot?.passportInfo?.place_of_issue || '未填写' }}</td>
                    <td class="label">护照有效期</td>
                    <td class="value">{{ snapshot?.passportInfo?.passport_expiry || '未填写' }}</td>
                  </tr>
                  <tr>
                    <td class="label">邮箱</td>
                    <td class="value">{{ snapshot?.contactInfo?.email || '未填写' }}</td>
                    <td class="label">电话号码</td>
                    <td class="value">{{ snapshot?.contactInfo?.telephone_number || '未填写' }}</td>
                    <td class="label">永久地址</td>
                    <td class="value">{{ snapshot?.contactInfo?.permanent_address || '' }}</td>
                    <td class="label">联系地址</td>
                    <td class="value">{{ snapshot?.contactInfo?.contact_address || '' }}</td>
                  </tr>
                  <tr>
                    <td class="label">紧急联系人</td>
                    <td class="value">{{ snapshot?.contactInfo?.emergency_contact_name || '' }}</td>
                    <td class="label">紧急联系电话</td>
                    <td class="value">
                      {{ snapshot?.contactInfo?.emergency_contact_phone || '' }}
                    </td>
                    <td class="label">紧急联系地址</td>
                    <td class="value">{{ snapshot?.contactInfo?.emergency_address || '' }}</td>
                    <td colspan="2"></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 操作按钮 -->
    <div class="footer">
      <el-button size="large" @click="$emit('cancel')">返回修改</el-button>
      <el-button type="primary" size="large" @click="$emit('confirm')">确认提交</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { User } from '@element-plus/icons-vue'
import { useVisaFormStore } from '@/stores/visaForm'

defineEmits(['confirm', 'cancel'])

const store = useVisaFormStore()
const snapshot = computed(() => store.getSnapshot())

const visaTypeMap = { 'Single-entry': '单次', 'Multiple-entry': '多次' } as Record<string, string>
const purposeMap = { Tourist: '旅游', Business: '商务', Family: '探亲', Work: '工作' } as Record<
  string,
  string
>
const entryGateMap: Record<string, string> = {
  'Tan Son Nhat Int Airport (Ho Chi Minh City)': '胡志明',
  'Noi Bai Int Airport': '河内',
  'Da Nang International Airport': '岘港',
  'Cam Ranh Int Airport (Khanh Hoa)': '芽庄',
  'Mong Cai Landport': '东兴',
  'Huu Nghi Landport': '友谊',
}
const expeditedTypeMap: Record<string, string> = {
  '4days': '4工',
  '3days': '3工',
  '2days': '2工',
  '1days': '1工',
}
function visaTypeText(val: string) {
  return visaTypeMap[val] || val || '未填写'
}
function purposeText(val: unknown) {
  return purposeMap[String(val)] || String(val) || '未填写'
}
function entryGateText(val: string | undefined) {
  if (!val) return '未填写'
  return entryGateMap[val] || val
}
function expeditedTypeText(val: string) {
  return expeditedTypeMap[val] || val || '未填写'
}
</script>

<style scoped lang="scss">
.snapshot-dialog {
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 0 8px;
  font-size: 18px;
}

.file-upload-section,
.form-section {
  margin-bottom: 16px;
  border-radius: 8px;

  .section-header {
    display: flex;
    align-items: center;
    gap: 6px;

    .title {
      font-size: 20px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
}

.double-layout {
  display: flex;
  flex-direction: row;
  gap: 24px;
}

.left-column {
  width: 160px;
  min-width: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.right-column {
  flex: 1 1 0%;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.file-preview-section {
  flex: 0 0 110px;
  min-width: 90px;
  max-width: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.file-preview-vertical {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 150px;
  min-width: 120px;
  align-items: center;
}

.visa-info-section {
  width: 100%;
}

.personal-info-section {
  width: 100%;
}

@media (max-width: 900px) {
  .double-layout {
    flex-direction: column;
    gap: 12px;
  }
  .left-column,
  .right-column {
    width: 100%;
    min-width: 0;
    align-items: flex-start;
  }
}

.image-row {
  display: flex;
  gap: 8px;
}

.image-item {
  flex: 1;

  label {
    display: block;
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--el-text-color-primary);
  }

  img {
    width: 140px;
    height: 140px;
    object-fit: cover;
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    background: white;
    display: block;
    margin: 0 auto;
  }

  .no-image {
    width: 140px;
    height: 140px;
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--el-text-color-placeholder);
    font-size: 15px;
    margin: 0 auto;
  }
}

.info-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: auto;

  td {
    padding: 4px 6px;
    border: 1px solid var(--el-border-color-light);
    font-size: 16px;
    line-height: 1.3;
    vertical-align: top;

    &.label {
      background: var(--el-fill-color-extra-light);
      font-weight: 600;
      color: var(--el-text-color-primary);
      white-space: nowrap;
      min-width: 50px;
      max-width: none;
    }

    &.value {
      background: white;
      color: var(--el-text-color-regular);
      word-break: break-word;
      white-space: normal;
      min-width: 50px;
      max-width: none;
    }
  }
}

@media (max-width: 1200px) {
  .double-layout {
    gap: 16px;
  }

  .file-preview-section {
    flex: 0 0 240px;
    min-width: 240px;
  }
}

@media (max-width: 992px) {
  .double-layout {
    gap: 12px;
  }

  .file-preview-section {
    flex: 0 0 200px;
    min-width: 200px;
  }

  .image-item {
    img,
    .no-image {
      height: 100px;
    }
  }
}

@media (max-width: 768px) {
  .double-layout {
    flex-direction: column;
    gap: 16px;
  }

  .file-preview-section {
    flex: none;
    min-width: auto;
  }

  .image-row {
    justify-content: center;
  }

  .image-item {
    max-width: 150px;
  }
}

.footer {
  margin-top: 20px;
  text-align: center;
  padding: 16px 0;
  border-top: 1px solid var(--el-border-color-light);

  .el-button {
    margin: 0 6px;
    min-width: 100px;
    font-size: 18px;
  }
}
</style>
