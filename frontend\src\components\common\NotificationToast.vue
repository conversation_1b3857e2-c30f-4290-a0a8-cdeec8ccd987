<template>
  <div
    v-if="visible"
    class="notification-toast"
    :class="[`toast-${type}`, { 'toast-entering': entering, 'toast-leaving': leaving }]"
  >
    <div class="toast-icon">
      <el-icon v-if="type === 'success'" color="#67C23A">
        <SuccessFilled />
      </el-icon>
      <el-icon v-else-if="type === 'error'" color="#F56C6C">
        <CircleCloseFilled />
      </el-icon>
      <el-icon v-else-if="type === 'warning'" color="#E6A23C">
        <WarningFilled />
      </el-icon>
      <el-icon v-else color="#409EFF">
        <InfoFilled />
      </el-icon>
    </div>

    <div class="toast-content">
      <div class="toast-title">{{ title }}</div>
      <div v-if="message" class="toast-message">{{ message }}</div>
      <div v-if="orderNo" class="toast-order">订单: {{ orderNo }}</div>
    </div>

    <button class="toast-close" @click="$emit('close')">
      <el-icon>
        <Close />
      </el-icon>
    </button>
  </div>
</template>

<script setup lang="ts">
import {
  CircleCloseFilled,
  Close,
  InfoFilled,
  SuccessFilled,
  WarningFilled,
} from '@element-plus/icons-vue'
import { onMounted, ref } from 'vue'

interface Props {
  visible: boolean
  type?: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  orderNo?: string
  duration?: number // Auto close after this many milliseconds
}

const props = withDefaults(defineProps<Props>(), {
  type: 'info',
  duration: 5000,
})

const emit = defineEmits<{
  close: []
}>()

const entering = ref(false)
const leaving = ref(false)

onMounted(() => {
  if (props.visible) {
    entering.value = true
    setTimeout(() => {
      entering.value = false
    }, 300)

    // Auto close
    if (props.duration > 0) {
      setTimeout(() => {
        leaving.value = true
        setTimeout(() => {
          emit('close')
        }, 300)
      }, props.duration)
    }
  }
})
</script>

<style scoped>
.notification-toast {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 9999;
  min-width: 320px;
  max-width: 480px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgb(0 0 0 / 15%);
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border-left: 4px solid #409eff;
  animation: slideIn 0.3s ease-out;
}

.notification-toast.toast-entering {
  animation: slideIn 0.3s ease-out;
}

.notification-toast.toast-leaving {
  animation: slideOut 0.3s ease-in;
}

.notification-toast.toast-success {
  border-left-color: #67c23a;
}

.notification-toast.toast-error {
  border-left-color: #f56c6c;
}

.notification-toast.toast-warning {
  border-left-color: #e6a23c;
}

.notification-toast.toast-info {
  border-left-color: #409eff;
}

.toast-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.toast-content {
  flex: 1;
}

.toast-title {
  font-weight: 600;
  font-size: 14px;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.toast-message {
  font-size: 13px;
  color: var(--el-text-color-regular);
  line-height: 1.4;
  margin-bottom: 4px;
}

.toast-order {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  font-family: Monaco, Menlo, 'Ubuntu Mono', monospace;
  background: var(--el-color-info-light-9);
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
  margin-top: 4px;
}

.toast-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  margin-left: 8px;
  color: var(--el-text-color-placeholder);
  transition: color 0.2s ease;
}

.toast-close:hover {
  color: var(--el-text-color-regular);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }

  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 响应式设计 */
@media (width <= 768px) {
  .notification-toast {
    right: 10px;
    left: 10px;
    min-width: auto;
    max-width: none;
  }
}
</style>
