<template>
  <el-dialog
    v-model="dialogVisible"
    title="订单详情"
    width="700px"
    :close-on-click-modal="false"
    destroy-on-close
    class="order-detail-dialog"
  >
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="4" animated />
    </div>

    <div v-else-if="orderDetail" class="order-detail-content">
      <!-- 订单状态头部 -->
      <div class="status-header">
        <div class="order-number">
          <span class="number">{{ orderDetail.order_no }}</span>
          <el-button type="primary" text size="small" @click="copyOrderNo" :icon="CopyDocument">
            复制
          </el-button>
        </div>
        <el-tag :type="statusType" size="large" class="status-tag">
          {{ statusText }}
        </el-tag>
      </div>

      <!-- 主要信息 -->
      <div class="main-info">
        <div class="info-rows">
          <!-- 第一行：申请人 + 护照号码 + 出生日期 -->
          <div class="info-row">
            <div class="info-item">
              <span class="label">申请人</span>
              <span class="value">{{ orderDetail.applicant_name }}</span>
            </div>

            <div class="info-item">
              <span class="label">护照号码</span>
              <span class="value passport">{{ orderDetail.passport_number }}</span>
            </div>

            <div class="info-item">
              <span class="label">出生日期</span>
              <span class="value">{{ orderDetail.date_of_birth }}</span>
            </div>
          </div>

          <!-- 第二行：创建时间 + 更新时间 -->
          <div class="info-row">
            <div class="info-item">
              <span class="label">创建时间</span>
              <span class="value">{{ formatTime(orderDetail.created_at) }}</span>
            </div>

            <div class="info-item">
              <span class="label">更新时间</span>
              <span class="value">{{ formatTime(orderDetail.updated_at) }}</span>
            </div>

            <!-- 空占位，保持三列对齐 -->
            <div class="info-item"></div>
          </div>

          <!-- 第三行：申请编号（如果有） -->
          <div v-if="orderDetail.application_number" class="info-row">
            <div class="info-item full-width">
              <span class="label">申请编号</span>
              <div class="value-with-copy">
                <span class="value">{{ orderDetail.application_number }}</span>
                <el-button
                  type="primary"
                  text
                  size="small"
                  @click="copyApplicationNo"
                  :icon="CopyDocument"
                >
                  复制
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="orderDetail.error_message" class="error-section">
        <div class="error-title">
          <el-icon color="#F56C6C"><WarningFilled /></el-icon>
          <span>错误信息</span>
        </div>
        <div class="error-content">
          {{ orderDetail.error_message }}
        </div>
        <div v-if="orderDetail.last_error_at" class="error-time">
          {{ formatTime(orderDetail.last_error_at) }}
        </div>
      </div>

      <!-- 重试信息 -->
      <div v-if="orderDetail.retry_count > 0" class="retry-section">
        <div class="retry-info">
          <el-icon color="#E6A23C"><Refresh /></el-icon>
          <span>重试次数：{{ orderDetail.retry_count }} / {{ orderDetail.max_retry_count }}</span>
        </div>
      </div>

      <!-- 状态历史 -->
      <div class="history-section">
        <div class="section-header">
          <h4>状态历史</h4>
        </div>

        <div v-if="statusHistory.length > 0" class="history-timeline">
          <div
            v-for="history in [...statusHistory].reverse()"
            :key="history.id"
            class="timeline-item"
          >
            <div class="timeline-time">
              {{ formatTime(history.created_at) }}
            </div>
            <div class="timeline-content">
              <div class="status-change">
                <span class="to-status">
                  {{ getStatusText(history.to_status) }}
                </span>
              </div>

              <div v-if="history.reason" class="change-reason">
                {{ history.reason }}
              </div>
            </div>
          </div>
        </div>

        <div v-else class="no-history">
          <el-text type="info">暂无状态变更记录</el-text>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button v-if="canDownload" type="success" @click="handleDownload" :icon="Download">
          下载签证
        </el-button>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import OrderAPI from '@/api/order'
import type { OrderInfo, OrderStatus, OrderStatusHistory } from '@/api/types'
import { OrderStatusHelper } from '@/utils/orderNumber'
import { CopyDocument, Download, Refresh, WarningFilled } from '@element-plus/icons-vue'
import { computed, ref, watch } from 'vue'

// Props
interface Props {
  visible: boolean
  orderNo?: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  download: [orderNo: string]
}>()

// 响应式数据
const loading = ref(false)
const orderDetail = ref<OrderInfo | null>(null)
const statusHistory = ref<OrderStatusHistory[]>([])

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => {
    emit('update:visible', value)
  },
})

const statusText = computed(() => {
  return orderDetail.value ? OrderStatusHelper.getStatusText(orderDetail.value.status) : ''
})

const statusType = computed(() => {
  return orderDetail.value ? OrderStatusHelper.getStatusType(orderDetail.value.status) : 'info'
})

const canDownload = computed(() => {
  return orderDetail.value ? OrderStatusHelper.canDownload(orderDetail.value.status) : false
})

// 方法
const loadOrderDetail = async (): Promise<void> => {
  if (!props.orderNo) return

  try {
    loading.value = true

    const response = await OrderAPI.getOrderDetail(props.orderNo)

    if (response.success && response.data) {
      orderDetail.value = response.data.order
      statusHistory.value = response.data.status_history || []
    } else {
      const notificationStore = useNotificationStore()
      notificationStore.showError(response.message ?? '加载订单详情失败')
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : '加载订单详情失败'
    const notificationStore = useNotificationStore()
    notificationStore.showError(errorMessage)
  } finally {
    loading.value = false
  }
}

const handleDownload = (): void => {
  if (orderDetail.value) {
    emit('download', orderDetail.value.order_no)
  }
}

const formatTime = (timeStr: string): string => {
  try {
    return new Date(timeStr).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch {
    return timeStr
  }
}

const getStatusText = (status: string): string => {
  return OrderStatusHelper.getStatusText(status as OrderStatus)
}

const copyOrderNo = async (): Promise<void> => {
  if (!orderDetail.value) return

  try {
    await navigator.clipboard.writeText(orderDetail.value.order_no)
    const notificationStore = useNotificationStore()
    notificationStore.showCopySuccess('订单编号')
  } catch {
    const notificationStore = useNotificationStore()
    notificationStore.showCopyError()
  }
}

const copyApplicationNo = async (): Promise<void> => {
  if (!orderDetail.value?.application_number) return

  try {
    await navigator.clipboard.writeText(orderDetail.value.application_number)
    const notificationStore = useNotificationStore()
    notificationStore.showCopySuccess('申请编号')
  } catch {
    const notificationStore = useNotificationStore()
    notificationStore.showCopyError()
  }
}

// 监听器
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.orderNo) {
      loadOrderDetail()
    }
  },
)

watch(
  () => props.orderNo,
  (orderNo) => {
    if (orderNo && props.visible) {
      loadOrderDetail()
    }
  },
)
</script>

<style scoped>
.order-detail-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.loading-container {
  padding: 20px 0;
}

.order-detail-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 状态头部 */
.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f6f9fc 0%, #e9f4ff 100%);
  border-radius: 8px;
  border: 1px solid #e1e8f0;
}

.order-number {
  display: flex;
  align-items: center;
  gap: 12px;
}

.number {
  font-family: 'SF Mono', Monaco, Consolas, monospace;
  font-size: 16px;
  font-weight: 700;
  color: #1f2937;
  letter-spacing: 1px;
}

.status-tag {
  font-weight: 600;
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 20px;
}

/* 主要信息 */
.main-info {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.info-rows {
  padding: 16px 20px;
}

.info-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 32px;
  margin-bottom: 12px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
}

.label {
  font-size: 11px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.value {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  line-height: 1.3;
}

.value.passport {
  font-family: 'SF Mono', Monaco, Consolas, monospace;
  letter-spacing: 1px;
}

.value-with-copy {
  display: flex;
  align-items: center;
  gap: 8px;
}

.full-width {
  flex: 1;
}

/* 错误信息 */
.error-section {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 12px 16px;
}

.error-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 6px;
}

.error-content {
  color: #7f1d1d;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 6px;
}

.error-time {
  font-size: 11px;
  color: #991b1b;
}

/* 重试信息 */
.retry-section {
  background: #fffbeb;
  border: 1px solid #fed7aa;
  border-radius: 6px;
  padding: 10px 16px;
}

.retry-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #92400e;
  font-weight: 500;
  font-size: 13px;
}

/* 状态历史 */
.history-section {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
}

.section-header h4 {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
  color: #1f2937;
}

.history-timeline {
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.timeline-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.timeline-time {
  font-size: 11px;
  color: #6b7280;
  white-space: nowrap;
  min-width: 120px;
  font-weight: 500;
}

.timeline-content {
  flex: 1;
}

.status-change {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 3px;
}

.to-status {
  color: #1f2937;
  font-weight: 600;
  font-size: 13px;
}

.change-reason {
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

.no-history {
  padding: 30px 16px;
  text-align: center;
}

/* 底部按钮 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式 */
@media (width <= 768px) {
  .status-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .info-row {
    flex-direction: column;
    gap: 16px;
  }

  .timeline-item {
    flex-direction: column;
    gap: 8px;
  }

  .timeline-time {
    min-width: auto;
  }
}
</style>
