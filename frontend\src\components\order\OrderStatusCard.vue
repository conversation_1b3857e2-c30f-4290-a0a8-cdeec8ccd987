<template>
  <el-card class="order-status-card" shadow="hover">
    <!-- 横向布局 - 一行显示所有信息 -->
    <div class="order-row">
      <!-- 左侧：订单信息 -->
      <div class="order-info">
        <div class="order-main">
          <span class="order-number">{{ formatOrderNo }}</span>
          <el-button
            type="primary"
            text
            size="small"
            @click="copyOrderNo"
            :icon="CopyDocument"
            class="copy-btn"
          />
        </div>
        <div class="order-details">
          <span class="detail-item">
            <span class="label">申请人：</span>
            <span class="value">{{ order.applicant_name }}</span>
          </span>
          <span class="detail-item">
            <span class="label">护照号码：</span>
            <span class="value passport">{{ order.passport_number }}</span>
          </span>
          <span class="detail-item">
            <span class="label">出生日期：</span>
            <span class="value">{{ order.date_of_birth }}</span>
          </span>
          <span class="detail-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ formatTime(order.created_at) }}</span>
          </span>
        </div>
      </div>

      <!-- 右侧：状态和操作 -->
      <div class="order-actions">
        <el-tag :type="statusType" size="default" class="status-tag">
          {{ statusText }}
        </el-tag>

        <div class="action-buttons">
          <el-button
            type="primary"
            size="small"
            @click="$emit('view-detail', order.order_no)"
            :icon="View"
          >
            详情
          </el-button>

          <el-button
            v-if="canRetry"
            type="warning"
            size="small"
            @click="$emit('retry', order.order_no)"
            :icon="Refresh"
            :loading="loading"
          >
            重试
          </el-button>

          <el-button
            v-if="canDownload"
            type="success"
            size="small"
            @click="$emit('download', order.order_no)"
            :icon="Download"
          >
            下载
          </el-button>

          <el-button
            v-if="canCancel"
            type="danger"
            size="small"
            @click="$emit('cancel', order.order_no)"
            :icon="Close"
          >
            取消
          </el-button>
        </div>
      </div>
    </div>

    <!-- 错误信息和重试信息 - 只在有错误时显示 -->
    <div v-if="order.error_message || order.retry_count > 0" class="order-extra">
      <div v-if="order.error_message" class="error-section">
        <el-alert
          :title="order.error_message"
          type="error"
          :closable="false"
          show-icon
          size="small"
        />
      </div>

      <div v-if="order.retry_count > 0" class="retry-info">
        <el-text size="small" type="warning">
          已重试 {{ order.retry_count }}/{{ order.max_retry_count }} 次
        </el-text>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { CopyDocument, View, Refresh, Download, Close } from '@element-plus/icons-vue'
import type { OrderInfo } from '@/api/types'
import { OrderNumberGenerator, OrderStatusHelper } from '@/utils/orderNumber'

// Props
interface Props {
  order: OrderInfo
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

// Emits
defineEmits<{
  'view-detail': [orderNo: string]
  retry: [orderNo: string]
  download: [orderNo: string]
  cancel: [orderNo: string]
}>()

// 计算属性
const formatOrderNo = computed(() => {
  return OrderNumberGenerator.formatDisplay(props.order.order_no)
})

const statusText = computed(() => {
  return OrderStatusHelper.getStatusText(props.order.status)
})

const statusType = computed(() => {
  return OrderStatusHelper.getStatusType(props.order.status)
})

const canRetry = computed(() => {
  return OrderStatusHelper.canRetry(props.order.status)
})

const canDownload = computed(() => {
  return OrderStatusHelper.canDownload(props.order.status)
})

const canCancel = computed(() => {
  return OrderStatusHelper.canCancel(props.order.status)
})

// 方法
const formatTime = (timeStr: string): string => {
  try {
    return new Date(timeStr).toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch {
    return timeStr
  }
}

const copyOrderNo = async (): Promise<void> => {
  try {
    await navigator.clipboard.writeText(props.order.order_no)
    ElMessage.success('订单编号已复制')
  } catch {
    ElMessage.error('复制失败')
  }
}
</script>

<style scoped lang="scss">
.order-status-card {
  margin-bottom: 8px;
  border-radius: 4px;

  :deep(.el-card__body) {
    padding: 12px 16px;
  }

  &:hover {
    border-color: var(--el-color-primary);
  }
}

.order-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}

.order-info {
  flex: 1;
  min-width: 0; // 防止文本溢出
}

.order-main {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;

  .order-number {
    font-family: 'Courier New', monospace;
    font-weight: 700;
    font-size: 16px;
    color: var(--el-color-primary);
  }

  .copy-btn {
    padding: 4px;
    min-height: auto;
  }
}

.order-details {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  font-size: 13px;
}

.detail-item {
  display: flex;
  gap: 4px;
  white-space: nowrap;

  .label {
    font-weight: 500;
    color: var(--el-text-color-secondary);
    min-width: 40px;
  }

  .value {
    color: var(--el-text-color-primary);

    &.passport {
      font-family: 'Courier New', monospace;
      font-weight: 500;
    }
  }
}

.order-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  flex-shrink: 0;

  .status-tag {
    font-weight: 500;
  }

  .action-buttons {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
  }
}

.order-extra {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid var(--el-border-color-light);
}

.error-section {
  margin-bottom: 8px;
}

.retry-info {
  text-align: right;
}

// 响应式设计
@media (max-width: 768px) {
  .order-row {
    flex-direction: column;
    gap: 12px;
  }

  .order-details {
    flex-direction: column;
    gap: 4px;
  }

  .order-actions {
    align-items: stretch;
    flex-direction: row;
    justify-content: space-between;

    .action-buttons {
      flex: 1;
      justify-content: flex-end;
    }
  }
}

@media (max-width: 480px) {
  .order-details {
    gap: 2px;
  }

  .detail-item {
    .label {
      min-width: 35px;
      font-size: 12px;
    }

    .value {
      font-size: 12px;
    }
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;

    .el-button {
      justify-content: center;
    }
  }
}
</style>
