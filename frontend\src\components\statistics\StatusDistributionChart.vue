<template>
  <div class="status-chart-container">
    <v-chart
      class="chart"
      :option="chartOption"
      :loading="loading"
      :loading-options="loadingOptions"
      autoresize
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import VChart from 'vue-echarts'

// 注册必要的ECharts组件
use([CanvasRenderer, PieChart, TitleComponent, TooltipComponent, LegendComponent])

// Props
interface Props {
  data: Array<{ name: string; value: number }>
  loading?: boolean
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  title: '申请状态分布',
})

// 图表配置
const chartOption = computed(() => ({
  title: {
    text: props.title,
    left: 'center',
    textStyle: {
      fontSize: 16,
      fontWeight: 'bold',
    },
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)',
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    top: 'center',
  },
  series: [
    {
      name: '申请状态',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['60%', '50%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 4,
        borderColor: '#fff',
        borderWidth: 2,
      },
      label: {
        show: false,
        position: 'center',
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      labelLine: {
        show: false,
      },
      data: props.data.map((item, index) => ({
        ...item,
        itemStyle: {
          color: getStatusColor(item.name, index),
        },
      })),
    },
  ],
}))

// 加载动画配置
const loadingOptions = {
  text: '加载中...',
  color: '#409EFF',
  textColor: '#000',
  maskColor: 'rgba(255, 255, 255, 0.8)',
  zlevel: 0,
}

// 获取状态对应的颜色
const getStatusColor = (statusName: string, index: number): string => {
  const colorMap: Record<string, string> = {
    提交成功: '#67C23A',
    提交失败: '#F56C6C',
    处理中: '#E6A23C',
    已审批: '#5CB87A',
    已拒绝: '#F78989',
    已下载: '#409EFF',
  }

  // 如果有预定义颜色则使用，否则使用默认色谱
  if (colorMap[statusName]) {
    return colorMap[statusName]
  }

  // 默认色谱
  const defaultColors = [
    '#5470C6',
    '#91CC75',
    '#FAC858',
    '#EE6666',
    '#73C0DE',
    '#3BA272',
    '#FC8452',
    '#9A60B4',
  ]

  return defaultColors[index % defaultColors.length]
}
</script>

<style scoped lang="scss">
.status-chart-container {
  width: 100%;
  height: 100%;

  .chart {
    width: 100%;
    height: 300px;
  }
}
</style>
