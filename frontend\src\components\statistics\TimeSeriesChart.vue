<template>
  <div class="timeseries-chart-container">
    <v-chart
      class="chart"
      :option="chartOption"
      :loading="loading"
      :loading-options="loadingOptions"
      autoresize
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from 'echarts/components'
import VChart from 'vue-echarts'

// 注册必要的ECharts组件
use([CanvasRenderer, LineChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent])

// Props
interface Props {
  dates: string[]
  applications: number[]
  successful: number[]
  loading?: boolean
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  title: '申请趋势图',
})

// 图表配置
const chartOption = computed(() => ({
  title: {
    text: props.title,
    left: 'center',
    textStyle: {
      fontSize: 16,
      fontWeight: 'bold',
    },
  },
  tooltip: {
    trigger: 'axis',
  },
  legend: {
    data: ['总申请数', '成功申请数'],
    bottom: 10,
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: props.dates,
    axisLabel: {
      formatter: (value: string) => {
        // 简化日期显示
        const date = new Date(value)
        return `${date.getMonth() + 1}/${date.getDate()}`
      },
    },
  },
  yAxis: {
    type: 'value',
    name: '申请数量',
    nameLocation: 'middle',
    nameGap: 40,
    nameTextStyle: {
      fontSize: 12,
    },
  },
  series: [
    {
      name: '总申请数',
      type: 'line',
      data: props.applications,
      smooth: true,
      lineStyle: {
        color: '#409EFF',
        width: 3,
      },
      itemStyle: {
        color: '#409EFF',
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.05)' },
          ],
        },
      },
    },
    {
      name: '成功申请数',
      type: 'line',
      data: props.successful,
      smooth: true,
      lineStyle: {
        color: '#67C23A',
        width: 3,
      },
      itemStyle: {
        color: '#67C23A',
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
            { offset: 1, color: 'rgba(103, 194, 58, 0.05)' },
          ],
        },
      },
    },
  ],
}))

// 加载动画配置
const loadingOptions = {
  text: '加载中...',
  color: '#409EFF',
  textColor: '#000',
  maskColor: 'rgba(255, 255, 255, 0.8)',
  zlevel: 0,
}
</script>

<style scoped lang="scss">
.timeseries-chart-container {
  width: 100%;
  height: 100%;

  .chart {
    width: 100%;
    height: 350px;
  }
}
</style>
