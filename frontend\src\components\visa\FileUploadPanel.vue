<template>
  <div class="upload-panel">
    <div class="file-uploads">
      <!-- 头像照片上传 -->
      <PortraitUpload
        :file="portraitFile"
        :preview="portraitPreview"
        :is-submitting="isSubmitting"
        @file-change="(file: File) => $emit('file-change', file, 'portrait')"
        @file-remove="() => $emit('file-remove', 'portrait')"
      />

      <!-- 护照扫描上传 -->
      <PassportUpload
        :file="passportFile"
        :preview="passportPreview"
        :is-submitting="isSubmitting"
        :ocr-processing="ocrProcessing"
        @file-change="(file: File) => $emit('file-change', file, 'passport')"
        @file-remove="() => $emit('file-remove', 'passport')"
        @trigger-ocr="$emit('trigger-ocr')"
        @ocr-result="$emit('ocr-result', $event)"
        @ocr-error="$emit('ocr-error', $event)"
        @ocr-cancel="$emit('ocr-cancel')"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * FileUploadPanel - 文件上传面板组件
 *
 * 负责协调头像照片和护照文件的上传功能。
 * 该组件遵循单一职责原则，专门处理文件上传相关的UI展示和事件传递。
 *
 * @component
 * @example
 * ```vue
 * <FileUploadPanel
 *   :portrait-file="portraitFile"
 *   :passport-file="passportFile"
 *   :portrait-preview="portraitPreview"
 *   :passport-preview="passportPreview"
 *   :is-submitting="false"
 *   :ocr-processing="false"
 *   @file-change="handleFileChange"
 *   @file-remove="handleFileRemove"
 *   @trigger-ocr="handleOCRTrigger"
 *   @ocr-result="handleOCRResult"
 *   @ocr-error="handleOCRError"
 *   @ocr-cancel="handleOCRCancel"
 * />
 * ```
 */

import PortraitUpload from './PortraitUpload.vue'
import PassportUpload from './PassportUpload.vue'
import type { OCRPassportData } from '@/types/form'

/**
 * 组件属性接口
 */
interface Props {
  /** 头像文件对象 */
  portraitFile?: File
  /** 护照文件对象 */
  passportFile?: File
  /** 头像预览URL */
  portraitPreview?: string
  /** 护照预览URL */
  passportPreview?: string
  /** 是否正在提交 */
  isSubmitting: boolean
  /** 是否正在进行OCR识别 */
  ocrProcessing: boolean
}

/**
 * 组件事件接口
 */
interface Emits {
  /** 文件变更事件 */
  (e: 'file-change', file: File, type: 'portrait' | 'passport'): void
  /** 文件移除事件 */
  (e: 'file-remove', type: 'portrait' | 'passport'): void
  /** 触发OCR识别事件 */
  (e: 'trigger-ocr'): void
  /** OCR识别结果事件 */
  (e: 'ocr-result', data: OCRPassportData): void
  /** OCR识别错误事件 */
  (e: 'ocr-error', error: string): void
  /** OCR识别取消事件 */
  (e: 'ocr-cancel'): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>

<style scoped lang="scss">
/**
 * 文件上传面板样式
 * 
 * 使用Flexbox布局，在较小屏幕上切换为单列显示
 */
.upload-panel {
  flex: 0 0 35%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.file-uploads {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}
</style>
