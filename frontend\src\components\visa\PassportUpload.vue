<template>
  <div class="file-upload-item">
    <label class="upload-label">Passport Scan 上传护照 <span class="required">*</span></label>

    <!-- 仅在没有文件时显示上传区域 -->
    <el-upload
      v-if="fileList.length === 0"
      ref="uploadRef"
      class="passport-uploader"
      :file-list="fileList"
      list-type="picture-card"
      :auto-upload="false"
      :limit="1"
      accept="image/jpeg,image/png"
      :on-change="handleFileChange"
      :on-remove="handleFileRemove"
      :on-preview="handlePictureCardPreview"
      :before-upload="beforeUpload"
      :disabled="isSubmitting || ocrProcessing"
    >
      <template #default>
        <el-icon class="upload-icon"><Plus /></el-icon>
      </template>
    </el-upload>

    <!-- 仅在有文件时显示预览 -->
    <div v-else class="file-preview-container">
      <div v-for="fileItem in fileList" :key="fileItem.uid" class="file-preview-item">
        <img
          :src="fileItem.url"
          @click="handlePictureCardPreview(fileItem)"
          class="preview-image"
        />
        <div class="file-actions">
          <el-button @click="handlePictureCardPreview(fileItem)" size="small" circle>
            <el-icon><ZoomIn /></el-icon>
          </el-button>
          <el-button
            @click="handleFileRemove(fileItem, fileList)"
            size="small"
            circle
            type="danger"
          >
            <el-icon><Delete /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- OCR操作按钮 - 移到护照图片下方 -->
      <div class="passport-actions" v-if="!ocrProcessing">
        <el-button
          size="small"
          type="primary"
          @click="$emit('trigger-ocr')"
          :loading="ocrProcessing"
          :disabled="fileList.length === 0 || isSubmitting"
        >
          <el-icon><MagicStick /></el-icon>
          {{ ocrProcessing ? '识别中...' : 'OCR识别' }}
        </el-button>

        <el-button
          v-if="ocrError"
          size="small"
          type="warning"
          @click="retryOCR"
          :disabled="isSubmitting"
        >
          <el-icon><RefreshRight /></el-icon>
          重试OCR
        </el-button>
      </div>
    </div>

    <!-- OCR状态指示器 -->
    <div v-if="ocrProcessing" class="ocr-status-container">
      <div class="ocr-status-indicator">
        <div class="ocr-status-content">
          <div class="ocr-loading-spinner"></div>
          <div class="ocr-status-text">
            <span class="ocr-main-text">正在识别护照信息...</span>
            <span class="ocr-file-name">{{ currentFileName }}</span>
          </div>
          <el-button
            v-if="canCancelOCR"
            size="small"
            type="danger"
            @click="$emit('ocr-cancel')"
            class="ocr-cancel-btn"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- OCR错误提示 -->
    <div v-if="ocrError" class="ocr-error-container">
      <el-alert
        :title="ocrError"
        type="error"
        show-icon
        :closable="false"
        class="ocr-error-alert"
      />
    </div>

    <div v-if="fileList.length === 0" class="upload-tips"></div>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewVisible" width="600px" align-center>
      <img :src="previewImageUrl" alt="预览图片" style="width: 100%; height: auto" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * PassportUpload - 护照扫描上传组件
 *
 * 专门负责处理护照文件的上传和OCR识别功能，包括：
 * - 护照文件上传（JPG、PNG格式）
 * - 文件大小验证（10MB限制）
 * - 自动OCR识别功能
 * - OCR重试机制
 * - 图片预览功能
 * - 实时状态指示器
 *
 * @component
 * @example
 * ```vue
 * <PassportUpload
 *   :file="passportFile"
 *   :preview="passportPreview"
 *   :is-submitting="false"
 *   :ocr-processing="false"
 *   @file-change="handlePassportChange"
 *   @file-remove="handlePassportRemove"
 *   @trigger-ocr="handleOCRTrigger"
 *   @ocr-result="handleOCRResult"
 *   @ocr-error="handleOCRError"
 *   @ocr-cancel="handleOCRCancel"
 * />
 * ```
 */

import { useOCR } from '@/composables/useOCR'
import type { OCRPassportData } from '@/types/form'
import { Close, Delete, MagicStick, Plus, RefreshRight, ZoomIn } from '@element-plus/icons-vue'
import type { UploadUserFile } from 'element-plus'
import { ElMessage } from 'element-plus'
import { ref, watchEffect } from 'vue'

/**
 * 组件属性接口
 */
interface Props {
  /** 上传的护照文件对象 */
  file?: File
  /** 护照图片预览URL */
  preview?: string
  /** 是否正在提交表单 */
  isSubmitting: boolean
  /** 是否正在进行OCR识别 */
  ocrProcessing: boolean
}

/**
 * 组件事件接口
 */
interface Emits {
  /** 文件变更事件 - 当用户选择新护照文件时触发 */
  (e: 'file-change', file: File): void
  /** 文件移除事件 - 当用户删除护照文件时触发 */
  (e: 'file-remove'): void
  /** 触发OCR识别事件 */
  (e: 'trigger-ocr'): void
  /** OCR识别结果事件 */
  (e: 'ocr-result', data: OCRPassportData): void
  /** OCR识别错误事件 */
  (e: 'ocr-error', error: string): void
  /** OCR识别取消事件 */
  (e: 'ocr-cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// OCR功能增强
const { canCancel: canCancelOCR, retryOCR: retryOCRProcessing } = useOCR()

// 响应式状态管理
/** 上传组件引用 */
const uploadRef = ref()
/** 文件列表，用于Element Plus Upload组件 */
const fileList = ref<UploadUserFile[]>([])
/** 当前上传文件名 */
const currentFileName = ref('')
/** OCR识别错误信息 */
const ocrError = ref('')
/** 预览对话框显示状态 */
const previewVisible = ref(false)
/** 预览图片URL */
const previewImageUrl = ref('')

/**
 * 响应式初始化文件列表
 * 当父组件传入文件和预览URL时，更新内部文件列表状态
 */
watchEffect(() => {
  if (props.file && props.preview) {
    fileList.value = [
      {
        name: props.file.name,
        url: props.preview,
        uid: Date.now(),
        status: 'success',
      },
    ]
    currentFileName.value = props.file.name
  } else {
    fileList.value = []
    currentFileName.value = ''
  }
})

/**
 * 文件上传前验证
 *
 * @param file - 要上传的文件
 * @returns false - 阻止默认上传行为，由父组件处理文件
 */
const beforeUpload = (file: File): boolean => {
  // 文件类型验证
  const isValidType = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isValidType) {
    ElMessage.error('只支持 JPG 和 PNG 格式的图片！')
    return false
  }

  // 文件大小验证 (10MB)
  const isValidSize = file.size / 1024 / 1024 < 10
  if (!isValidSize) {
    ElMessage.error('图片大小不能超过 10MB！')
    return false
  }

  return false // 阻止默认上传，由父组件处理
}

/**
 * 文件变化处理函数
 * 自动触发OCR识别（延迟100ms，保持与旧版本的兼容性）
 *
 * @param file - Element Plus 上传文件对象
 * @param fileListParam - Element Plus 文件列表
 */
const handleFileChange = (file: UploadUserFile, fileListParam: UploadUserFile[]): void => {
  if (file.status === 'ready') {
    fileList.value = fileListParam.slice(-1) // 只保留最后一个文件
    currentFileName.value = file.name || ''
    clearOCRError() // 清除之前的错误
    emit('file-change', file.raw as File)

    // 如果是护照文件，自动触发OCR（延迟100ms，对应旧版本逻辑）
    setTimeout(() => {
      emit('trigger-ocr')
    }, 100)
  }
}

/**
 * 移除文件处理函数
 *
 * @param file - 要移除的文件对象
 * @param fileListParam - 更新后的文件列表
 */
const handleFileRemove = (file: UploadUserFile, fileListParam: UploadUserFile[]): void => {
  fileList.value = fileListParam
  currentFileName.value = ''
  clearOCRError()
  emit('file-remove')
}

/**
 * 重试OCR识别
 *
 * 当OCR识别失败时，用户可以手动重试识别过程
 */
const retryOCR = async (): Promise<void> => {
  if (!props.file) {
    ElMessage.warning('护照文件不存在，请重新上传')
    return
  }

  console.log('🔄 用户手动重试OCR...', props.file.name)
  clearOCRError()

  try {
    const result = await retryOCRProcessing(props.file)

    if (result.success && result.data) {
      console.log('✅ OCR重试成功:', result.data)
      emit('ocr-result', result.data)
      ElMessage.success(result.message ?? 'OCR识别成功')
    } else {
      console.warn('❌ OCR重试失败:', result.message)
      ocrError.value = result.message ?? 'OCR识别失败'
      emit('ocr-error', result.message ?? 'OCR识别失败')
    }
  } catch (error: unknown) {
    console.error('💥 OCR重试异常:', error)
    const errorMessage = error instanceof Error ? error.message : 'OCR重试过程中发生错误'
    ocrError.value = errorMessage
    emit('ocr-error', errorMessage)
  }
}

/**
 * 清除OCR错误状态
 */
const clearOCRError = (): void => {
  ocrError.value = ''
}

/**
 * 图片预览功能
 *
 * @param file - 要预览的文件对象
 */
const handlePictureCardPreview = (file: UploadUserFile): void => {
  console.log('预览图片:', file)
  if (file.url) {
    previewImageUrl.value = file.url
    previewVisible.value = true
  }
}
</script>

<style scoped lang="scss">
.file-upload-item {
  .upload-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 8px;

    .required {
      color: var(--el-color-danger);
    }
  }

  .upload-tips {
    margin-top: 8px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
    text-align: center;
    line-height: 1.3;
  }
}

// Element Plus Upload 官方样式优化
:deep(.el-upload) {
  &.passport-uploader {
    .el-upload--picture-card {
      width: 120px;
      height: 120px;
      border-radius: 8px;
      border: 2px dashed var(--el-border-color);
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--el-color-primary);
        background-color: var(--el-fill-color-light);
      }

      .upload-icon {
        font-size: 28px;
        color: var(--el-color-primary);
      }
    }
  }
}

// 自定义文件预览样式
.file-preview-container {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .file-preview-item {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid var(--el-border-color);
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--el-color-primary);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .file-actions {
        opacity: 1;
      }
    }

    .preview-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      cursor: pointer;
    }

    .file-actions {
      position: absolute;
      bottom: 8px;
      right: 8px;
      display: flex;
      gap: 4px;
      opacity: 0;
      transition: all 0.3s ease;

      .el-button {
        width: 24px;
        height: 24px;

        .el-icon {
          font-size: 12px;
        }
      }
    }
  }

  // 护照操作按钮样式
  .passport-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;

    .el-button {
      flex-shrink: 0;
    }
  }
}

// OCR状态指示器样式
.ocr-status-container {
  margin-top: 12px;
  position: relative;
}

.ocr-status-indicator {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #0ea5e9;
  border-radius: 8px;
  padding: 12px;
  animation: pulse 2s infinite;

  .ocr-status-content {
    display: flex;
    align-items: center;
    gap: 12px;

    .ocr-loading-spinner {
      width: 20px;
      height: 20px;
      border: 2px solid #e0f2fe;
      border-top: 2px solid #0ea5e9;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .ocr-status-text {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 2px;

      .ocr-main-text {
        font-size: 14px;
        font-weight: 500;
        color: #0369a1;
      }

      .ocr-file-name {
        font-size: 12px;
        color: #64748b;
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .ocr-cancel-btn {
      width: 24px;
      height: 24px;
      min-height: 24px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// OCR错误提示样式
.ocr-error-container {
  margin-top: 12px;
}

// 动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(14, 165, 233, 0.3);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(14, 165, 233, 0);
  }
}

:deep(.el-upload-list) {
  &.el-upload-list--picture-card {
    .el-upload-list__item {
      width: 120px;
      height: 120px;
      border-radius: 8px;
      overflow: hidden;

      .el-upload-list__item-thumbnail {
        width: 100%;
        height: 100%;
        object-fit: contain;
        background-color: var(--el-fill-color-light);
      }

      .el-upload-list__item-actions {
        .el-upload-list__item-preview,
        .el-upload-list__item-delete {
          color: #fff;
          font-size: 16px;
        }
      }
    }
  }
}
</style>
