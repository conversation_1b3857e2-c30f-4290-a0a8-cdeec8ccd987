<template>
  <div class="file-upload-item">
    <label class="upload-label">Portrait Photo 上传照片 <span class="required">*</span></label>

    <!-- 仅在没有文件时显示上传区域 -->
    <el-upload
      v-if="fileList.length === 0"
      ref="uploadRef"
      class="portrait-uploader"
      :file-list="fileList"
      list-type="picture-card"
      :auto-upload="false"
      :limit="1"
      accept="image/jpeg,image/png"
      :on-change="handleFileChange"
      :on-remove="handleFileRemove"
      :on-preview="handlePictureCardPreview"
      :before-upload="beforeUpload"
      :disabled="isSubmitting"
    >
      <template #default>
        <el-icon class="upload-icon"><Plus /></el-icon>
      </template>
    </el-upload>

    <!-- 仅在有文件时显示预览 -->
    <div v-else class="file-preview-container">
      <div v-for="fileItem in fileList" :key="fileItem.uid" class="file-preview-item">
        <img
          :src="fileItem.url"
          @click="handlePictureCardPreview(fileItem)"
          class="preview-image"
        />
        <div class="file-actions">
          <el-button @click="handlePictureCardPreview(fileItem)" size="small" circle>
            <el-icon><ZoomIn /></el-icon>
          </el-button>
          <el-button
            @click="handleFileRemove(fileItem, fileList)"
            size="small"
            circle
            type="danger"
          >
            <el-icon><Delete /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <div v-if="fileList.length === 0" class="upload-tips"></div>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewVisible" width="600px" align-center>
      <img :src="previewImageUrl" alt="预览图片" style="width: 100%; height: auto" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * PortraitUpload - 头像照片上传组件
 *
 * 专门负责处理用户头像照片的上传功能，包括：
 * - 文件格式验证（JPG、PNG）
 * - 文件大小限制（10MB）
 * - 图片预览功能
 * - 文件移除功能
 *
 * @component
 * @example
 * ```vue
 * <PortraitUpload
 *   :file="portraitFile"
 *   :preview="portraitPreview"
 *   :is-submitting="false"
 *   @file-change="handlePortraitChange"
 *   @file-remove="handlePortraitRemove"
 * />
 * ```
 */

import { ref, watchEffect } from 'vue'
import { Plus, ZoomIn, Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { UploadUserFile } from 'element-plus'

/**
 * 组件属性接口
 */
interface Props {
  /** 上传的文件对象 */
  file?: File
  /** 图片预览URL */
  preview?: string
  /** 是否正在提交表单 */
  isSubmitting: boolean
}

/**
 * 组件事件接口
 */
interface Emits {
  /** 文件变更事件 - 当用户选择新文件时触发 */
  (e: 'file-change', file: File): void
  /** 文件移除事件 - 当用户删除文件时触发 */
  (e: 'file-remove'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式状态管理
/** 上传组件引用 */
const uploadRef = ref()
/** 文件列表，用于Element Plus Upload组件 */
const fileList = ref<UploadUserFile[]>([])
/** 预览对话框显示状态 */
const previewVisible = ref(false)
/** 预览图片URL */
const previewImageUrl = ref('')

/**
 * 响应式初始化文件列表
 * 当父组件传入文件和预览URL时，更新内部文件列表状态
 */
watchEffect(() => {
  if (props.file && props.preview) {
    fileList.value = [
      {
        name: props.file.name,
        url: props.preview,
        uid: Date.now(),
        status: 'success',
      },
    ]
  } else {
    fileList.value = []
  }
})

/**
 * 文件上传前验证
 *
 * @param file - 要上传的文件
 * @returns false - 阻止默认上传行为，由父组件处理文件
 */
const beforeUpload = (file: File): boolean => {
  // 文件类型验证
  const isValidType = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isValidType) {
    ElMessage.error('只支持 JPG 和 PNG 格式的图片！')
    return false
  }

  // 文件大小验证 (10MB)
  const isValidSize = file.size / 1024 / 1024 < 10
  if (!isValidSize) {
    ElMessage.error('图片大小不能超过 10MB！')
    return false
  }

  return false // 阻止默认上传，由父组件处理
}

/**
 * 文件变化处理函数
 *
 * @param file - Element Plus 上传文件对象
 * @param fileListParam - Element Plus 文件列表
 */
const handleFileChange = (file: UploadUserFile, fileListParam: UploadUserFile[]): void => {
  if (file.status === 'ready') {
    fileList.value = fileListParam.slice(-1) // 只保留最后一个文件
    emit('file-change', file.raw as File)
  }
}

/**
 * 移除文件处理函数
 *
 * @param file - 要移除的文件对象
 * @param fileListParam - 更新后的文件列表
 */
const handleFileRemove = (file: UploadUserFile, fileListParam: UploadUserFile[]): void => {
  fileList.value = fileListParam
  emit('file-remove')
}

/**
 * 图片预览功能
 *
 * @param file - 要预览的文件对象
 */
const handlePictureCardPreview = (file: UploadUserFile): void => {
  console.log('预览图片:', file)
  if (file.url) {
    previewImageUrl.value = file.url
    previewVisible.value = true
  }
}
</script>

<style scoped lang="scss">
/**
 * 头像上传组件样式
 * 
 * 包含标签样式、上传区域样式和预览样式
 */
.file-upload-item {
  .upload-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 8px;

    .required {
      color: var(--el-color-danger);
    }
  }

  .upload-tips {
    margin-top: 8px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
    text-align: center;
    line-height: 1.3;
  }
}

/**
 * Element Plus Upload 组件样式优化
 */
:deep(.el-upload) {
  &.portrait-uploader {
    .el-upload--picture-card {
      width: 120px;
      height: 120px;
      border-radius: 8px;
      border: 2px dashed var(--el-border-color);
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--el-color-primary);
        background-color: var(--el-fill-color-light);
      }

      .upload-icon {
        font-size: 28px;
        color: var(--el-color-primary);
      }
    }
  }
}

/**
 * 自定义文件预览样式
 */
.file-preview-container {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .file-preview-item {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid var(--el-border-color);
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--el-color-primary);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .file-actions {
        opacity: 1;
      }
    }

    .preview-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      cursor: pointer;
    }

    .file-actions {
      position: absolute;
      bottom: 8px;
      right: 8px;
      display: flex;
      gap: 4px;
      opacity: 0;
      transition: all 0.3s ease;

      .el-button {
        width: 24px;
        height: 24px;

        .el-icon {
          font-size: 12px;
        }
      }
    }
  }
}

:deep(.el-upload-list) {
  &.el-upload-list--picture-card {
    .el-upload-list__item {
      width: 120px;
      height: 120px;
      border-radius: 8px;
      overflow: hidden;

      .el-upload-list__item-thumbnail {
        width: 100%;
        height: 100%;
        object-fit: contain;
        background-color: var(--el-fill-color-light);
      }

      .el-upload-list__item-actions {
        .el-upload-list__item-preview,
        .el-upload-list__item-delete {
          color: #fff;
          font-size: 16px;
        }
      }
    }
  }
}
</style>
