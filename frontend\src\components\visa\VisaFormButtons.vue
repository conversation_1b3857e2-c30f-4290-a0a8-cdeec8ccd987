<template>
  <div class="form-actions-bottom">
    <el-button type="info" @click="saveAsDraft" :disabled="isSubmitting"> 保存草稿 </el-button>
    <el-button @click="resetForm" :disabled="isSubmitting"> 重置表单 </el-button>

    <!-- 简单的取消申请按钮 -->
    <el-button
      v-if="showCancelButton"
      type="danger"
      @click="handleCancelApplication"
      :loading="isCancelling"
    >
      {{ isCancelling ? '取消中...' : '取消申请' }}
    </el-button>

    <el-button
      type="primary"
      @click="handleSubmitClick"
      :loading="isSubmitting"
      :disabled="isSubmitting"
    >
      {{ isSubmitting ? '提交中...' : '提交申请' }}
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { api } from '@/api/request'
import { useApplicationStore } from '@/stores/application'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, ref } from 'vue'

// Props - 接收必要的状态
interface Props {
  isFormValid: boolean
  isSubmitting: boolean
}

// Emits - 只发出按钮点击事件
interface Emits {
  (event: 'submit-form'): void
  (event: 'reset-form'): void
  (event: 'save-as-draft'): void
  (event: 'show-snapshot'): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const applicationStore = useApplicationStore()
const isCancelling = ref(false)

// 🔥 重构：是否显示取消按钮（有自动化任务处理中的申请时显示）
const showCancelButton = computed(() => {
  return applicationStore.applications.some((app) => app.automationStatus === 'processing')
})

// 处理提交点击，显示数据快照预览
const handleSubmitClick = () => {
  // 发出显示快照预览的事件
  emit('show-snapshot')
}

// 🔥 重构：取消自动化任务处理
const handleCancelApplication = async () => {
  const processingApps = applicationStore.applications.filter(
    (app) => app.automationStatus === 'processing',
  )

  if (processingApps.length === 0) {
    ElMessage.warning('没有正在处理的申请')
    return
  }

  try {
    await ElMessageBox.confirm('确定要取消正在处理的申请吗？', '取消确认', {
      confirmButtonText: '确定取消',
      cancelButtonText: '继续处理',
      type: 'warning',
    })

    isCancelling.value = true

    // 找到最近的processing申请（简单取第一个）
    const appToCancel = processingApps[0]

    // 🔍 调试信息：检查应用对象的结构
    console.log('🔍 准备取消的应用对象:', appToCancel)
    console.log('🔍 orderNo:', appToCancel.orderNo)
    console.log('🔍 passportNumber:', appToCancel.passportNumber)

    // 🔧 修复：必须使用orderNo，不能用passportNumber
    if (!appToCancel.orderNo) {
      ElMessage.error('无法找到订单编号，无法取消申请')
      console.error('❌ 应用缺少orderNo字段:', appToCancel)
      return
    }

    console.log('🔍 使用的order_no:', appToCancel.orderNo)

    try {
      const response = await api.post<{ success: boolean; message?: string }>('/api/visa/cancel', {
        order_no: appToCancel.orderNo,
      })

      console.log('🔍 取消请求响应:', response)
      console.log('🔍 response.data:', response.data)
      console.log('🔍 response结构:', JSON.stringify(response, null, 2))

      // 🔧 修复：处理不同的响应结构
      const responseData = response.data ?? response
      console.log('🔍 最终使用的数据:', responseData)

      if (responseData?.success) {
        ElMessage.success('申请已取消')
        // 🔧 修复：更新申请状态为cancelled而不是failed
        applicationStore.updateApplicationStatus(appToCancel.passportNumber, 'cancelled')
      } else {
        console.error('❌ 取消失败，服务器返回:', responseData?.message)
        ElMessage.error(responseData?.message ?? '取消失败')
      }
    } catch (error: unknown) {
      console.error('❌ 取消申请API调用失败:', error)
      if (error && typeof error === 'object' && 'response' in error) {
        const apiError = error as { response?: { data?: { detail?: string } } }
        console.error('❌ 错误响应:', apiError.response?.data)
        ElMessage.error(apiError.response?.data?.detail ?? '取消申请失败')
      } else {
        ElMessage.error('取消申请失败')
      }
    }
  } catch {
    console.log('用户取消了取消操作')
  } finally {
    isCancelling.value = false
  }
}

// 重置表单
const resetForm = () => {
  emit('reset-form')
}

// 保存草稿
const saveAsDraft = () => {
  emit('save-as-draft')
}
</script>

<style scoped lang="scss">
.form-actions-bottom {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding: 16px 0;

  .el-button {
    border-radius: 6px;
    font-weight: 500;
    padding: 8px 16px;
    font-size: 14px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &:active {
      transform: translateY(0);
    }

    &:disabled {
      transform: none;
      box-shadow: none;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .form-actions-bottom {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    z-index: 1000;
    background: white;
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);

    .el-button {
      margin-bottom: 0;
    }
  }
}
</style>
