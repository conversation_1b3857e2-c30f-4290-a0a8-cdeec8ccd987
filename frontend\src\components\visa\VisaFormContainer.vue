<template>
  <el-card class="file-upload-section" shadow="hover">
    <template #header>
      <div class="section-header">
        <el-icon><Upload /></el-icon>
        <span class="title">Document Uploads & Visa Application 签证申请信息</span>
      </div>
    </template>

    <div class="section-content">
      <!-- 左侧：文件上传区域 (35%宽度) -->
      <FileUploadPanel
        :portrait-file="portraitFile"
        :passport-file="passportFile"
        :portrait-preview="portraitPreview"
        :passport-preview="passportPreview"
        :is-submitting="isSubmitting"
        :ocr-processing="ocrProcessing"
        @file-change="(file, type) => $emit('file-change', file, type)"
        @file-remove="(type) => $emit('file-remove', type)"
        @trigger-ocr="triggerOCR"
        @ocr-result="(data) => $emit('ocr-result', data)"
        @ocr-error="(error) => $emit('ocr-error', error)"
        @ocr-cancel="cancelOCR"
      />

      <!-- 右侧：签证申请详细信息 (65%宽度) -->
      <VisaApplicationForm
        v-model="visaInfo"
        :get-field-class="getFieldClass"
        @validate-visa="(isValid) => $emit('validate-visa', isValid)"
        @expedited-type-change="(value) => $emit('expedited-type-change', value)"
      />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { useNotificationStore } from '@/stores/notification'
import type { OCRPassportData, PreviousVisitInfo, VietnamContactInfo, VisaInfo } from '@/types/form'
import { Upload } from '@element-plus/icons-vue'
import FileUploadPanel from './FileUploadPanel.vue'
import VisaApplicationForm from './VisaApplicationForm.vue'

interface Props {
  portraitFile?: File
  passportFile?: File
  portraitPreview?: string
  passportPreview?: string
  isSubmitting: boolean
  ocrProcessing: boolean
  getFieldClass?: (fieldPath: string, value: unknown) => { [key: string]: boolean }
}

interface Emits {
  (e: 'file-change', file: File, type: 'portrait' | 'passport'): void
  (e: 'file-remove', type: 'portrait' | 'passport'): void
  (e: 'trigger-ocr'): void
  (e: 'ocr-result', data: OCRPassportData): void
  (e: 'ocr-error', error: string): void
  (e: 'ocr-cancel'): void
  (e: 'validate-visa', isValid: boolean): void
  (e: 'expedited-type-change', value: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// v-model 绑定签证信息
const visaInfo = defineModel<VisaInfo & PreviousVisitInfo & VietnamContactInfo>({ required: true })

// 简化的OCR事件转发 - 只做基本验证，不含业务逻辑
const triggerOCR = () => {
  if (!props.passportFile) {
    const notificationStore = useNotificationStore()
    notificationStore.showWarning('请先上传护照扫描件')
    return
  }
  emit('trigger-ocr')
}

const cancelOCR = () => {
  emit('ocr-cancel')
}
</script>

<style scoped lang="scss">
// 容器组件样式 - 只保留布局相关样式
.file-upload-section {
  margin-bottom: 24px;
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .section-header {
    display: flex;
    align-items: center;
    gap: 8px;

    .title {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
}

.section-content {
  display: flex;
  gap: 24px;

  @media (max-width: 768px) {
    flex-direction: column;
  }
}
</style>
