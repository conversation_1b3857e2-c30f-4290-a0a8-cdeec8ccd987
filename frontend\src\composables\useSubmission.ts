import { request } from '@/api/request'
import type { VisaApplicationResponse } from '@/api/types'
import type { ApplicationItem } from '@/stores/application'
import { useApplicationStore } from '@/stores/application'
import { useNotificationStore } from '@/stores/notification'
import { useSessionStore } from '@/stores/session'
import { useSubmissionStore } from '@/stores/submission'
import type { VisaFormData } from '@/types/form'
import { redirectToLogin } from '@/utils/navigation'
import { ElMessageBox } from 'element-plus'
import { ref } from 'vue'

// 提交状态接口
export interface SubmissionStatus {
  isSubmitting: boolean
  buttonText: string
  disabled: boolean
}

// Helper function to check if error is an authentication error
function isAuthError(error: unknown): boolean {
  if (!error || typeof error !== 'object') {
    return false
  }

  // Check for HTTP 401 error
  if ('response' in error) {
    const httpError = error as { response?: { status?: number } }
    if (httpError.response?.status === 401) {
      return true
    }
  }

  // Check for session expired message
  if ('message' in error) {
    const messageError = error as { message?: string }
    if (typeof messageError.message === 'string' && messageError.message.includes('会话已过期')) {
      return true
    }
  }

  return false
}

export const useSubmission = () => {
  const submissionStore = useSubmissionStore()
  const applicationStore = useApplicationStore()
  const sessionStore = useSessionStore()
  const notificationStore = useNotificationStore()

  // 提交状态响应式数据
  const submissionStatus = ref<SubmissionStatus>({
    isSubmitting: false,
    buttonText: '提交申请',
    disabled: false,
  })

  // 显示确认对话框，严格对应旧版Utils.showConfirm
  const showConfirmDialog = async (
    title: string,
    message: string,
    confirmText = '确认',
    cancelText = '取消',
  ): Promise<boolean> => {
    try {
      await ElMessageBox.confirm(message, title, {
        confirmButtonText: confirmText,
        cancelButtonText: cancelText,
        type: 'warning',
        dangerouslyUseHTMLString: true,
      })
      return true
    } catch {
      return false
    }
  }

  // 处理本地重复提交检查，优化为快速重复点击检查
  const handleQuickDuplicateCheck = async (passportNumber: string): Promise<boolean> => {
    if (submissionStore.isQuickDuplicateSubmission(passportNumber)) {
      const userConfirm = await showConfirmDialog(
        '快速重复提交确认',
        `护照号 ${passportNumber} 刚刚已经提交过申请（1分钟内）。<br><br>是否确认重新提交？<br><br><strong>注意：快速重复提交可能是误操作。</strong>`,
        '确认提交',
        '取消',
      )

      return userConfirm
    }
    return true
  }

  // 处理服务器端重复提交检查，严格对应旧版逻辑
  const handleServerDuplicateCheck = async (
    passportNumber: string,
    formData: FormData,
  ): Promise<boolean> => {
    try {
      console.log('🔍 开始服务器端重复检查...', passportNumber)
      const checkResult = await submissionStore.checkServerDuplicate(passportNumber)

      console.log('✅ 服务器重复检查完成:', checkResult)

      if (checkResult.exists) {
        // 根据can_resubmit决定是否需要弹窗
        if (checkResult.can_resubmit) {
          // 可以直接重新提交，无需弹窗
          console.log(`✅ 允许直接重新提交: ${passportNumber} (${checkResult.warning_type})`)
          return true
        } else {
          // 需要弹窗提醒用户
          console.log('🚨 服务器检测到重复提交，显示确认对话框')

          let message = ''
          const title = '重复提交确认'

          // 🔥 优化：更好地处理订单系统的数据
          const displayId = checkResult.order_no ?? checkResult.application_number ?? '未知'
          const isOrderNo = checkResult.order_no && checkResult.order_no.startsWith('VN')
          const idType = isOrderNo ? '订单编号' : '申请编号'

          switch (checkResult.warning_type) {
            case 'success':
              message = `护照号 ${passportNumber} 之前已成功提交过申请。<br><br>${idType}：${displayId}<br>提交时间：${checkResult.submission_time ?? '未知'}<br>状态：提交成功<br><br>是否确认重新提交？<br><br><strong>注意：重复提交可能产生额外费用。</strong>`
              break
            case 'pending':
              message = `护照号 ${passportNumber} 的申请正在提交中。<br><br>${idType}：${displayId}<br>当前状态：${checkResult.status ?? '处理中'}<br>提交时间：${checkResult.submission_time ?? '未知'}<br><br>是否确认重新提交？<br><br><strong>注意：重复提交可能产生额外费用。</strong>`
              break
            case 'failed':
              // 失败状态通常会直接重新提交，但如果到这里说明有特殊情况
              message = `护照号 ${passportNumber} 之前的申请处理失败。<br><br>${idType}：${displayId}<br>提交时间：${checkResult.submission_time ?? '未知'}<br><br>是否确认重新提交？`
              break
            default:
              message = `护照号 ${passportNumber} 之前已提交过申请。<br><br>${idType}：${displayId}<br>提交时间：${checkResult.submission_time ?? '未知'}<br><br>是否确认重新提交？<br><br><strong>注意：重复提交可能产生额外费用。</strong>`
          }

          const userConfirm = await showConfirmDialog(title, message, '确认重新提交', '取消')

          if (!userConfirm) {
            return false
          } else {
            // 继续提交，但需要告诉后端这是用户确认的重复提交
            formData.append('force_resubmit', 'true')
            return true
          }
        }
      }
      return true
    } catch (error) {
      console.error('❌ 服务器重复检查过程中发生错误:', error)
      if (error instanceof Error && error.message === 'UNAUTHORIZED') {
        // 会话过期，显示错误并重定向
        console.log('重复检查时发现会话过期，提醒用户')
        notificationStore.showAuthError('您的登录会话已过期，请重新登录。')
        setTimeout(() => {
          redirectToLogin()
        }, 1500)
        return false
      } else {
        console.warn('⚠️ 检查重复提交失败，继续提交:', error)
        // 🔥 关键修复：如果重复检查失败，应该继续提交流程，而不是阻止提交
        return true
      }
    }
  }

  // 更新提交按钮状态
  const updateSubmissionButton = (isSubmitting: boolean, originalText: string = '提交申请') => {
    submissionStatus.value = {
      isSubmitting,
      buttonText: isSubmitting ? '提交中...' : originalText,
      disabled: isSubmitting,
    }
  }

  // 构建表单数据，严格对应旧版FormData构建逻辑
  const buildFormData = (formData: VisaFormData): FormData => {
    const submitFormData = new FormData()

    console.log('🔍 DEBUG: 开始构建FormData，输入数据:', formData)

    // 遍历所有字段并添加到FormData
    Object.entries(formData).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        if (value instanceof File) {
          submitFormData.append(key, value)
        } else {
          submitFormData.append(key, String(value))
        }
      }
    })

    return submitFormData
  }

  // 🔥 删除：无用的监控函数，状态更新由轮询机制处理

  // 主提交方法，严格迁移旧版完整流程
  const submitApplication = async (formData: VisaFormData): Promise<boolean> => {
    // 检查是否在登录页面，如果是则中止提交
    if (typeof window !== 'undefined' && window.location.pathname === '/login') {
      submissionStore.resetSubmissionState()
      return false
    }

    try {
      // 🔧 关键修复：开始提交前强制确保状态是干净的
      if (submissionStore.isSubmitting) {
        submissionStore.resetSubmissionState()
      }

      // 🟢 基于状态的防重复点击检查
      if (submissionStore.isSubmitting) {
        notificationStore.showWarning('申请正在提交中，请勿重复点击提交按钮。')
        return false
      }

      // 🟢 立即更新按钮状态，提供视觉反馈
      updateSubmissionButton(true)

      // 提交前检查会话状态
      const sessionValid = await sessionStore.checkSession()
      if (!sessionValid) {
        console.log('❌ 提交前发现会话已过期')
        notificationStore.showAuthError('登录已过期，正在跳转到登录页面...')

        // 重置提交状态，避免按钮卡住
        submissionStore.resetSubmissionState()
        updateSubmissionButton(false)

        // 立即跳转到登录页面
        if (window.location.pathname !== '/login') {
          console.log('🔄 立即跳转到登录页面')
          redirectToLogin()
        }

        return false
      }
      console.log('✅ 会话有效，继续提交')

      console.log('🔍 DEBUG: 获取到的护照号:', formData.passport_number)

      // 开始提交流程
      submissionStore.startSubmission(formData.passport_number)

      // 检查本地重复提交
      console.log('🔍 开始本地快速重复检查...')
      const localCheckPassed = await handleQuickDuplicateCheck(formData.passport_number)
      if (!localCheckPassed) {
        console.log('❌ 本地快速重复检查未通过')
        submissionStore.resetSubmissionState()
        updateSubmissionButton(false)
        return false
      }
      console.log('✅ 本地快速重复检查通过')

      // 构建FormData
      console.log('🔍 开始构建表单数据...')
      const submitFormData = buildFormData(formData)
      console.log('✅ 表单数据构建完成')

      // 检查服务器端重复提交
      console.log('🔍 开始服务器端重复检查...')
      const serverCheckPassed = await handleServerDuplicateCheck(
        formData.passport_number,
        submitFormData,
      )
      if (!serverCheckPassed) {
        console.log('❌ 服务器端重复检查未通过')
        submissionStore.resetSubmissionState()
        updateSubmissionButton(false)
        return false
      }
      console.log('✅ 服务器端重复检查通过')

      console.log('✅ 重复检查完成，继续执行提交')

      // 显示加载状态
      notificationStore.showInfo('正在提交申请，请稍候...')

      // 🔧 修复：同步获取orderNo，异步处理自动化任务
      try {
        console.log('🚀 开始同步提交获取orderNo...')
        const apiResponse = await request.upload<VisaApplicationResponse>(
          '/api/visa/apply',
          submitFormData,
        )
        const response = apiResponse.data ?? (apiResponse as VisaApplicationResponse)

        // 获取表单数据用于显示
        const surname = formData.surname ?? ''
        const given_name = formData.given_name ?? ''
        const chinese_name = formData.chinese_name ?? ''
        const orderNo = response?.tracking_info?.order_no

        // 🔥 简化：只设置必要字段，其他字段会自动为undefined
        const applicationData: ApplicationItem = {
          passportNumber: formData.passport_number,
          applicantName:
            `${surname} ${given_name}`.trim() ?? chinese_name ?? formData.passport_number,
          chineseName: chinese_name,
          submissionTime: new Date().toLocaleString('zh-CN'),
          orderStatus: 'created',
          orderNo: orderNo,
          automationStatus: 'processing',
        }

        applicationStore.addApplication(applicationData)

        notificationStore.showOperationSuccess(
          `申请已提交！\n\n订单编号: ${orderNo}\n\n护照号: ${formData.passport_number}\n\n申请正在后台处理中，请稍后查看结果。`,
        )

        submissionStore.markSubmissionSuccess(formData.passport_number)
        console.log('✅ 提交流程完成，orderNo已设置:', orderNo)

        // 🔥 删除：状态监控由轮询机制统一处理，无需单独启动

        // 重置按钮状态
        updateSubmissionButton(false)
      } catch (error) {
        console.error('❌ 同步提交失败:', error)
        notificationStore.showError('提交失败，请重试')
        updateSubmissionButton(false)
        return false
      }

      return true
    } catch (error: unknown) {
      console.error('申请提交失败:', error)

      // 检查是否是认证错误
      if (isAuthError(error)) {
        submissionStore.markSubmissionFailed('会话已过期，正在跳转到登录页面...')
        // 错误处理由API拦截器处理，这里不需要额外跳转
        return false
      }

      // 🔥 修复：安全地获取错误消息
      const errorMessage =
        error &&
        typeof error === 'object' &&
        'message' in error &&
        typeof (error as { message?: string }).message === 'string'
          ? (error as { message: string }).message
          : '提交失败，请重试'

      submissionStore.markSubmissionFailed(errorMessage)
      updateSubmissionButton(false)
      return false
    } finally {
      // 🔧 新增：确保按钮状态在异常情况下也能正确重置
      // 延迟重置，确保正常流程中的按钮状态不被覆盖
      setTimeout(() => {
        if (submissionStore.isSubmitting) {
          console.log('🔓 最终重置按钮状态（异常处理）')
          updateSubmissionButton(false)
        }
      }, 1000)
    }
  }

  return {
    // 状态
    submissionStatus,

    // 方法
    submitApplication,
    updateSubmissionButton,
  }
}
