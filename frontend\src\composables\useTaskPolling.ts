/**
 * 订单轮询组合式函数 - 新架构版本
 * 🔥 适配新架构：通过FastAPI查询automation_logs获取任务状态
 */

import { computed, onMounted, onUnmounted, readonly, ref } from 'vue'

import { api } from '@/api/request'
import type { OrderInfo } from '@/api/types'
import { useApplicationStore } from '@/stores/application'
import { useAuthStore } from '@/stores/auth'

interface PollingOptions {
  interval?: number
  enabled?: boolean
}

interface TaskStatus {
  order_no: string
  status: 'processing' | 'success' | 'failed' | 'cancelled' | 'pending'
  message?: string
  task_type?: string
  celery_task_id?: string
  started_at?: string
  completed_at?: string
  updated_at: string
}

// 状态更新记录接口
interface StatusUpdateRecord {
  orderNo: string
  oldStatus: string
  newStatus: string
  timestamp: string
  message: string
}

// 轮询错误类型
interface PollingError {
  message?: string
  toString?: () => string
}

export function useTaskPolling(options: PollingOptions = {}) {
  const { interval = 30000, enabled = true } = options

  // 状态管理
  const isPolling = ref(false)
  const isConnected = ref(true)
  const isPageVisible = ref(true)
  const hasError = ref(false)
  const errorMessage = ref('')
  const canRetry = ref(true)

  // 订单和状态更新
  const orders = ref<OrderInfo[]>([])
  const statusUpdates = ref<StatusUpdateRecord[]>([])

  // 应用商店
  const applicationStore = useApplicationStore()
  // 🎯 使用认证store替代直接localStorage访问
  const authStore = useAuthStore()

  let pollingTimer: number | null = null

  /**
   * 查询单个任务的状态
   */
  const queryTaskStatus = async (orderNo: string): Promise<TaskStatus | null> => {
    try {
      // ✅ 依赖统一拦截器处理认证，移除手动token检查
      console.log(`🌐 发起API请求: /api/automation-logs/status/${orderNo}`)
      const response = await api.get(`/api/automation-logs/status/${orderNo}`)
      console.log(`📡 API响应:`, response)

      if (response.success) {
        console.log(`✅ 成功获取任务状态:`, response.data)
        return response.data as TaskStatus
      } else {
        console.warn(`⚠️ API返回失败:`, response)
        return null
      }
    } catch (error) {
      console.error(`❌ 查询任务状态API失败 (${orderNo}):`, error)
      return null
    }
  }

  /**
   * 执行轮询
   */
  const performPolling = async () => {
    console.log('🔄 轮询开始执行', { enabled, isPageVisible: isPageVisible.value })

    if (!enabled || !isPageVisible.value) {
      console.log('⏸️ 轮询跳过：', { enabled, isPageVisible: isPageVisible.value })
      return
    }

    try {
      hasError.value = false
      errorMessage.value = ''

      // 🔥 实现真正的轮询逻辑：查询automation_logs状态
      const applications = applicationStore.applications
      console.log(
        '📋 当前应用列表：',
        applications.map((app) => ({
          orderNo: app.orderNo,
          automationStatus: app.automationStatus,
          name: app.chineseName ?? app.applicantName,
        })),
      )

      // 🔥 修复：轮询所有处理中的任务，即使orderNo为undefined
      const processingApps = applications.filter((app) => app.automationStatus === 'processing')

      console.log(
        '🔄 需要轮询的任务：',
        processingApps.map((app) => ({
          orderNo: app.orderNo,
          automationStatus: app.automationStatus,
        })),
      )

      if (processingApps.length === 0) {
        console.log('✅ 没有需要轮询的任务')
        isConnected.value = true
        return
      }

      console.log(`🔄 开始轮询 ${processingApps.length} 个处理中的任务`)

      // 批量查询任务状态
      for (const app of processingApps) {
        try {
          // 🔥 修复：如果orderNo为undefined，跳过查询但记录日志
          if (!app.orderNo) {
            console.log(`⏸️ 跳过查询: ${app.passportNumber} (orderNo为undefined，等待后台处理完成)`)
            continue
          }

          console.log(`🔍 查询任务状态: ${app.orderNo}`)
          const taskStatus = await queryTaskStatus(app.orderNo)
          console.log(`📊 查询结果:`, { orderNo: app.orderNo, taskStatus })

          if (taskStatus) {
            console.log(`🔄 状态比较: 当前=${app.automationStatus}, 查询到=${taskStatus.status}`)

            if (taskStatus.status !== app.automationStatus) {
              console.log(
                `📝 自动化任务状态发生变化: ${app.orderNo} ${app.automationStatus} → ${taskStatus.status}`,
              )

              // 只更新有效的状态（排除pending状态）
              if (taskStatus.status !== 'pending') {
                console.log(`✅ 更新自动化任务状态: ${app.orderNo} → ${taskStatus.status}`)

                // 更新自动化任务状态
                applicationStore.updateApplicationStatusByOrderNo(
                  app.orderNo,
                  taskStatus.status as 'processing' | 'success' | 'failed' | 'cancelled',
                )

                // 记录状态更新
                statusUpdates.value.unshift({
                  orderNo: app.orderNo,
                  oldStatus: app.automationStatus,
                  newStatus: taskStatus.status,
                  timestamp: new Date().toISOString(),
                  message: taskStatus.message ?? '自动化任务状态已更新',
                })

                // 限制状态更新记录数量
                if (statusUpdates.value.length > 50) {
                  statusUpdates.value = statusUpdates.value.slice(0, 50)
                }
              } else {
                console.log(`⏸️ 跳过pending状态更新: ${app.orderNo}`)
              }
            } else {
              console.log(`➡️ 自动化任务状态无变化: ${app.orderNo} 保持 ${app.automationStatus}`)
            }
          } else {
            console.warn(`❌ 未获取到任务状态: ${app.orderNo}`)
          }
        } catch (error) {
          console.error(`❌ 查询任务状态失败: ${app.orderNo}`, error)
        }
      }

      isConnected.value = true
    } catch (error: unknown) {
      let pollingError: PollingError = {}

      // 安全的错误消息提取
      if (typeof error === 'object' && error !== null && 'message' in error) {
        pollingError = error as PollingError
      } else if (typeof error === 'string') {
        pollingError.message = error
      } else if (error instanceof Error) {
        pollingError.message = error.message
      } else {
        // 对于其他类型的错误，使用默认消息
        pollingError.message = '轮询失败'
      }

      console.error('轮询失败:', error)
      hasError.value = true
      errorMessage.value = pollingError.message ?? '轮询失败'
      isConnected.value = false
    }
  }

  /**
   * 开始轮询
   */
  const startPolling = () => {
    console.log('🚀 startPolling被调用', { enabled, interval })

    if (!enabled) {
      console.log('❌ 轮询被禁用，跳过启动')
      return
    }

    // ✅ 移除手动认证检查，依赖统一拦截器处理
    console.log('✅ 开始启动轮询...')
    isPolling.value = true

    // 立即执行一次
    console.log('🔄 立即执行第一次轮询')
    performPolling()

    // 设置定时器
    if (pollingTimer) {
      console.log('🔄 清除旧的轮询定时器')
      clearInterval(pollingTimer)
    }

    console.log(`⏰ 设置轮询定时器，间隔${interval}ms`)
    pollingTimer = window.setInterval(performPolling, interval)

    console.log('✅ 轮询启动完成', { pollingTimer, isPolling: isPolling.value })
  }

  /**
   * 停止轮询
   */
  const stopPolling = () => {
    isPolling.value = false

    if (pollingTimer) {
      clearInterval(pollingTimer)
      pollingTimer = null
    }
  }

  /**
   * 清除错误
   */
  const clearError = () => {
    hasError.value = false
    errorMessage.value = ''
    canRetry.value = true
  }

  /**
   * 获取最近的状态更新
   */
  const getRecentUpdates = (limit = 10) => {
    return statusUpdates.value.slice(0, limit)
  }

  /**
   * 页面可见性处理
   */
  const handleVisibilityChange = () => {
    isPageVisible.value = !document.hidden

    if (isPageVisible.value && isPolling.value) {
      // 页面重新可见时，立即执行一次轮询
      performPolling()
    }
  }

  /**
   * 监听认证状态变化
   */
  const watchAuthStatus = () => {
    // ✅ 使用authStore状态，移除直接localStorage操作
    const checkAuthStatus = () => {
      // 🎯 依赖authStore的认证状态而非直接访问localStorage
      const isAuthenticated = authStore.isAuthenticated.value

      if (!isAuthenticated && isPolling.value) {
        console.log('🔒 检测到用户登出，停止轮询')
        stopPolling()
        hasError.value = true
        errorMessage.value = '用户已登出'
      } else if (isAuthenticated && !isPolling.value && enabled) {
        console.log('🔓 检测到用户登录，准备启动轮询')
        clearError()
        // 延迟启动，避免频繁切换
        setTimeout(() => {
          if (enabled && !isPolling.value) {
            startPolling()
          }
        }, 1000)
      }
    }

    // 定期检查认证状态（每30秒）
    const authCheckInterval = setInterval(checkAuthStatus, 30000)

    return () => clearInterval(authCheckInterval)
  }

  // 生命周期管理
  onMounted(() => {
    console.log('🔧 useTaskPolling onMounted被调用', { enabled })

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // 🔐 启动认证状态监听
    const stopAuthWatch = watchAuthStatus()

    // ✅ 简化启动逻辑，依赖统一拦截器处理认证
    if (enabled) {
      console.log('🚀 启用轮询，开始启动')
      startPolling()
    } else {
      console.log('⏸️ 轮询被禁用，不启动')
    }

    // 清理函数
    onUnmounted(() => {
      stopPolling()
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      stopAuthWatch()
    })
  })

  // 计算属性
  const activeTasksCount = computed(() => {
    return applicationStore.applications.filter((app) => app.automationStatus === 'processing')
      .length
  })

  const completedTasksCount = computed(() => {
    return applicationStore.applications.filter(
      (app) =>
        app.automationStatus === 'success' ||
        app.automationStatus === 'failed' ||
        app.automationStatus === 'cancelled',
    ).length
  })

  // 🔧 调试函数：手动测试轮询
  const debugPolling = async () => {
    console.log('🔧 手动调试轮询...')
    await performPolling()
  }

  return {
    // 状态
    isPolling: readonly(isPolling),
    isConnected: readonly(isConnected),
    isPageVisible: readonly(isPageVisible),
    hasError: readonly(hasError),
    errorMessage: readonly(errorMessage),
    canRetry: readonly(canRetry),

    // 数据
    orders: readonly(orders),
    statusUpdates: readonly(statusUpdates),

    // 计算属性
    activeTasksCount,
    completedTasksCount,

    // 方法
    startPolling,
    stopPolling,
    performPolling,
    clearError,
    getRecentUpdates,
    queryTaskStatus,
    debugPolling, // 🔧 调试方法
  }
}
