import { useApplicationStore } from '@/stores/application'
import type {
  ContactInfo,
  OCRPassportData,
  PassportInfo,
  PersonalInfo,
  PreviousVisitInfo,
  VietnamContactInfo,
  VisaFormData,
  VisaInfo,
} from '@/types/form'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, reactive } from 'vue'
import { useFormPersistence } from './useFormPersistence'
import { useOCR } from './useOCR'
import { useStorageCleanup } from './useStorageCleanup'
import { useSubmission } from './useSubmission'

export interface ValidationStatus {
  [key: string]: boolean
}

// 组件内部使用的表单数据类型 - 避免与原生FormData冲突
interface VisaFormStructure {
  personalInfo: PersonalInfo
  passportInfo: PassportInfo
  contactInfo: ContactInfo
  visaInfo: VisaInfo & PreviousVisitInfo & VietnamContactInfo
  files: {
    portrait_photo?: File
    passport_scan?: File
    portrait_preview?: string
    passport_preview?: string
  }
}

export const useVisaForm = () => {
  // 🔧 修复：使用applicationStore统一状态管理
  const applicationStore = useApplicationStore()

  // 表单数据初始化 - 严格遵循遗留字段名称
  const visaFormData = reactive<VisaFormStructure>({
    personalInfo: {
      surname: '',
      given_name: '',
      chinese_name: '',
      sex: '' as 'M' | 'F' | '',
      dob: '',
      place_of_birth: '',
      nationality: 'CHINA',
      religion: 'NO',
    },
    passportInfo: {
      passport_number: '',
      date_of_issue: '',
      place_of_issue: '',
      passport_expiry: '',
      passport_type: 'Ordinary passport',
    },
    contactInfo: {
      email: '',
      telephone_number: '',
      permanent_address: '',
      contact_address: '',
    },
    visaInfo: {
      visa_entry_type: 'Multiple-entry' as 'Single-entry' | 'Multiple-entry' | '', // 默认多次
      visa_validity_duration: '90天' as '30天' | '90天' | '', // 默认90天
      visa_start_date: '',
      intended_entry_gate: 'Tan Son Nhat Int Airport (Ho Chi Minh City)',
      purpose_of_entry: 'Tourist' as
        | 'Tourist'
        | 'Business'
        | 'Visiting relatives'
        | 'Working'
        | 'Other',
      expedited_type: '4days' as '4days' | '3days' | '2days' | '1days' | undefined, // 默认出签生效(4工)
      visited_vietnam_last_year: false as boolean | undefined, // 默认否
      previous_entry_date: '',
      previous_exit_date: '',
      previous_purpose: '',
      // Vietnam contact defaults
      has_vietnam_contact: false as boolean | undefined, // 默认否
      vietnam_contact_organization: '',
      vietnam_contact_phone: '',
      vietnam_contact_address: '',
      vietnam_contact_purpose: '',
    },
    files: {
      portrait_photo: undefined as File | undefined,
      passport_scan: undefined as File | undefined,
      portrait_preview: undefined as string | undefined,
      passport_preview: undefined as string | undefined,
    },
  })

  // 验证状态管理
  const validationStatus = reactive<ValidationStatus>({
    portrait_photo: false,
    passport_scan: false,
    personalInfo: false,
    passportInfo: false,
    contactInfo: false,
    visaInfo: false,
  })

  // 🔧 修复：提交历史记录改为从applicationStore获取
  const submissionHistory = computed(() => {
    // 将applicationStore的数据转换为SubmissionRecord格式
    return applicationStore.sortedApplications.map((app) => ({
      id: app.passportNumber,
      orderNo: app.orderNo, // 新增：包含订单编号
      status:
        app.automationStatus === 'processing'
          ? ('submitting' as const)
          : app.automationStatus === 'success'
            ? ('success' as const)
            : ('failed' as const),
      applicantName: app.applicantName,
      submitTime: new Date(app.submissionTime),
    }))
  })

  // OCR状态管理 - 与外部OCR composable同步
  const ocrStatus = reactive({
    isProcessing: computed(() => ocrProcessing.value), // 与OCR composable同步
    hasResult: false,
    applied: false,
    data: null as OCRPassportData | null,
  })

  // 使用现有的 composables
  const { submitApplication, submissionStatus } = useSubmission()
  const { recognizePassport, handleOCRSuccess, isProcessing: ocrProcessing } = useOCR()

  // 🔥 新增：使用表单持久化功能
  const {
    saveFormToStorage,
    clearFormFromStorage,
    isFormDataEmpty,
    initializeFormData,
    setupAutoSave,
    cleanupOrphanedFileReferences,
  } = useFormPersistence()

  // 🔥 新增：使用存储清理功能
  const { initAutoCleanup } = useStorageCleanup()

  // 🔥 新增：设置自动保存
  const cleanupAutoSave = setupAutoSave(visaFormData)

  // 🔥 修改：初始化时先恢复数据，再更新验证状态，并执行清理检查
  setTimeout(async () => {
    // 1. 初始化自动清理（检查存储容量和过期文件）
    await initAutoCleanup()

    // 2. 清理孤立的文件引用
    await cleanupOrphanedFileReferences()

    // 3. 恢复表单数据
    await initializeFormData(visaFormData, updateValidationStatus)
    updateValidationStatus()
  }, 100)

  // 计算属性
  const isFormValid = computed(() => {
    // 强制更新验证状态，确保同步
    updateValidationStatus()

    const result =
      validationStatus.portrait_photo &&
      validationStatus.passport_scan &&
      validationStatus.personalInfo &&
      validationStatus.passportInfo &&
      validationStatus.contactInfo &&
      validationStatus.visaInfo

    return result
  })

  const isSubmitting = computed(() => submissionStatus.value.isSubmitting)

  // 字段样式计算
  const getFieldClass = (_fieldPath: string, value: unknown) => {
    const isEmpty = !value || (typeof value === 'string' && value.trim() === '')
    return {
      'field-filled': !isEmpty,
      'field-empty': isEmpty,
    }
  }

  // 处理表单段落验证
  const handleSectionValidate = (section: string, isValid: boolean) => {
    validationStatus[section] = isValid
    console.log(`表单段落验证: ${section} = ${isValid}`)
  }

  // 更新验证状态
  const updateValidationStatus = () => {
    // 检查个人信息必填字段
    validationStatus.personalInfo = !!(
      visaFormData.personalInfo.surname &&
      visaFormData.personalInfo.given_name &&
      visaFormData.personalInfo.sex &&
      visaFormData.personalInfo.dob &&
      visaFormData.personalInfo.place_of_birth &&
      visaFormData.personalInfo.nationality &&
      visaFormData.personalInfo.religion
    )

    // 检查护照信息必填字段
    validationStatus.passportInfo = !!(
      visaFormData.passportInfo.passport_number &&
      visaFormData.passportInfo.date_of_issue &&
      visaFormData.passportInfo.place_of_issue &&
      visaFormData.passportInfo.passport_expiry
    )

    // 检查联系信息必填字段
    validationStatus.contactInfo = !!(
      visaFormData.contactInfo.email && visaFormData.contactInfo.telephone_number
    )

    // 检查签证信息必填字段 - expedited_type是可选的，不参与验证
    validationStatus.visaInfo = !!(
      visaFormData.visaInfo.visa_entry_type &&
      visaFormData.visaInfo.visa_validity_duration &&
      visaFormData.visaInfo.visa_start_date &&
      visaFormData.visaInfo.intended_entry_gate &&
      visaFormData.visaInfo.purpose_of_entry
    )

    // 检查文件上传状态 - 修复：直接检查文件是否存在
    validationStatus.portrait_photo = !!visaFormData.files.portrait_photo
    validationStatus.passport_scan = !!visaFormData.files.passport_scan
  }

  // 处理字段变化
  const handleFieldChange = (field: string, value: unknown) => {
    console.log(`字段变化: ${field} = ${value}`)

    // 解析字段路径并更新对应的数据
    const [section, fieldName] = field.split('.')

    if (section === 'personalInfo' && fieldName && fieldName in visaFormData.personalInfo) {
      ;(visaFormData.personalInfo as Record<string, unknown>)[fieldName] = value
    } else if (section === 'passportInfo' && fieldName && fieldName in visaFormData.passportInfo) {
      ;(visaFormData.passportInfo as Record<string, unknown>)[fieldName] = value
    } else if (section === 'contactInfo' && fieldName && fieldName in visaFormData.contactInfo) {
      ;(visaFormData.contactInfo as Record<string, unknown>)[fieldName] = value
    } else if (section === 'visaInfo' && fieldName && fieldName in visaFormData.visaInfo) {
      ;(visaFormData.visaInfo as Record<string, unknown>)[fieldName] = value
    }

    // 触发验证检查
    updateValidationStatus()
  }

  // 处理OCR识别结果 - 增强版本，支持字段级别验证和反馈
  const handleOCRResult = (data: OCRPassportData) => {
    ocrStatus.hasResult = true
    ocrStatus.applied = false
    ocrStatus.data = data

    // 使用增强的OCR成功处理，支持字段级别验证
    handleOCRSuccess(data, applyOCRResultDirectly, handleFieldFilled)
  }

  // 字段填充回调 - 用于实时验证和UI反馈
  const handleFieldFilled = (_field: string, _value: unknown, _isValid: boolean) => {
    // 根据验证结果更新UI状态
    if (_isValid) {
      // 成功填充的字段会显示绿色勾号
      console.log(`✅ 字段 ${_field} 验证通过`)
    } else {
      // 验证失败的字段会显示警告
      console.log(`⚠️ 字段 ${_field} 验证失败`)
    }
  }

  // 直接应用OCR识别结果 - 对应旧版本 fillFormWithOCRData 逻辑
  // 增强功能：支持日期格式转换、性别自动选择、字段验证
  const applyOCRResultDirectly = (data: OCRPassportData) => {
    console.log('🚀 直接应用OCR数据到表单:', data)

    // 更新个人信息 - 支持自动验证，使用OCR字段到表单字段的映射
    if (
      data.surname ||
      data.given_name ||
      data.chinese_name ||
      data.sex ||
      data.date_of_birth ||
      data.place_of_birth ||
      data.nationality
    ) {
      Object.assign(visaFormData.personalInfo, {
        surname: data.surname ?? visaFormData.personalInfo.surname,
        given_name: data.given_name ?? visaFormData.personalInfo.given_name,
        chinese_name: data.chinese_name ?? visaFormData.personalInfo.chinese_name,
        sex: data.sex ?? visaFormData.personalInfo.sex, // 自动选择性别radio
        dob: data.date_of_birth ?? visaFormData.personalInfo.dob, // OCR的date_of_birth映射到表单的dob
        place_of_birth: data.place_of_birth ?? visaFormData.personalInfo.place_of_birth,
        nationality: data.nationality ?? visaFormData.personalInfo.nationality,
      })

      console.log('✅ 个人信息已更新:', visaFormData.personalInfo)
    }

    // 更新护照信息 - 支持自动验证，使用OCR字段到表单字段的映射
    if (data.passport_number || data.date_of_issue || data.place_of_issue || data.date_of_expiry) {
      Object.assign(visaFormData.passportInfo, {
        passport_number: data.passport_number ?? visaFormData.passportInfo.passport_number,
        date_of_issue: data.date_of_issue ?? visaFormData.passportInfo.date_of_issue,
        place_of_issue: data.place_of_issue ?? visaFormData.passportInfo.place_of_issue,
        passport_expiry: data.date_of_expiry ?? visaFormData.passportInfo.passport_expiry, // OCR的date_of_expiry映射到表单的passport_expiry
      })

      console.log('✅ 护照信息已更新:', visaFormData.passportInfo)
    }

    ocrStatus.applied = true

    // 自动触发验证更新 - 对应旧版validation logic
    updateValidationStatus()

    // 成功提示已在handleOCRSuccess中处理
  }

  // OCR错误处理 - 支持用户重试
  const handleOCRError = (error: string) => {
    console.error('❌ OCR识别失败:', error)
    ocrStatus.hasResult = false
    ocrStatus.applied = false
    ocrStatus.data = null

    // 显示错误提示但不阻塞表单操作
    ElMessage.error({
      message: error,
      duration: 5000,
      showClose: true,
    })
  }

  // OCR取消处理
  const handleOCRCancel = () => {
    console.log('🚫 OCR处理被用户取消')
    ocrStatus.hasResult = false
    ocrStatus.applied = false
    ocrStatus.data = null

    ElMessage.info('OCR识别已取消，您可以手动填写表单或重新上传护照')
  }

  // 清除OCR结果
  const clearOCRResult = () => {
    ocrStatus.hasResult = false
    ocrStatus.applied = false
    ocrStatus.data = null
    ElMessage.info('已清除OCR识别结果')
  }

  // 文件上传处理 - 严格按照原文件实现
  const handleFileChange = (file: unknown, type: 'portrait' | 'passport') => {
    console.log(`文件上传: ${type}`, file)

    // 修复：支持直接传递File对象或包含raw属性的对象
    let targetFile: File | null = null

    if (file instanceof File) {
      // 直接传递的File对象
      targetFile = file
    } else if (file && typeof file === 'object' && 'raw' in file) {
      // Element Plus上传组件格式
      const uploadFile = file as { name: string; raw: File }
      targetFile = uploadFile.raw
    }

    if (!targetFile) {
      console.warn('⚠️ 无效的文件格式:', file)
      return
    }

    console.log(`✅ 文件解析成功: ${targetFile.name}, 大小: ${targetFile.size}`)

    // 预览图片 - 严格按照原文件实现
    const reader = new FileReader()
    reader.onload = (e) => {
      if (type === 'portrait') {
        visaFormData.files.portrait_preview = e.target?.result as string
        visaFormData.files.portrait_photo = targetFile
        validationStatus.portrait_photo = true
        console.log('✅ 头像文件已设置，验证状态:', validationStatus.portrait_photo)
      } else if (type === 'passport') {
        visaFormData.files.passport_preview = e.target?.result as string
        visaFormData.files.passport_scan = targetFile
        validationStatus.passport_scan = true
        console.log('✅ 护照文件已设置，验证状态:', validationStatus.passport_scan)

        // 自动触发OCR - 与原文件一致
        setTimeout(() => {
          triggerOCR()
        }, 100)
      }

      // 立即更新验证状态
      updateValidationStatus()
    }
    reader.readAsDataURL(targetFile)
  }

  // 移除文件 - 简化实现，与原文件一致
  const removeFile = (type: 'portrait' | 'passport') => {
    if (type === 'portrait') {
      visaFormData.files.portrait_photo = undefined
      visaFormData.files.portrait_preview = undefined
      validationStatus.portrait_photo = false
    } else if (type === 'passport') {
      visaFormData.files.passport_scan = undefined
      visaFormData.files.passport_preview = undefined
      validationStatus.passport_scan = false

      // 清除OCR结果
      clearOCRResult()
    }

    // 🔥 新增：更新验证状态以反映文件清除
    updateValidationStatus()
  }

  // 触发OCR识别 - 使用真实OCR服务
  const triggerOCR = async () => {
    if (!visaFormData.files.passport_scan) return

    try {
      const result = await recognizePassport(visaFormData.files.passport_scan)
      if (result.success && result.data) {
        handleOCRResult(result.data)
      } else {
        ElMessage.error(result.message ?? 'OCR识别失败')
      }
    } catch (error) {
      console.error('OCR识别失败:', error)
      ElMessage.error('OCR识别失败，请重试')
    }
  }

  // 提交表单
  const submitForm = async (validateFunctions: (() => Promise<boolean>)[]) => {
    // 基础验证检查
    if (!isFormValid.value) {
      ElMessage.error('请完善所有必填信息后再提交')
      return
    }

    try {
      // 验证所有表单组件
      const validationResults = await Promise.all(validateFunctions.map((fn) => fn()))

      const allValid = validationResults.every((result) => result === true)
      if (!allValid) {
        ElMessage.error('表单验证失败，请检查错误信息')
        return
      }

      // 构建提交数据
      const submitData: VisaFormData = {
        // 个人信息字段
        surname: visaFormData.personalInfo.surname,
        given_name: visaFormData.personalInfo.given_name,
        chinese_name: visaFormData.personalInfo.chinese_name,
        sex: visaFormData.personalInfo.sex,
        dob: visaFormData.personalInfo.dob,
        place_of_birth: visaFormData.personalInfo.place_of_birth,
        nationality: visaFormData.personalInfo.nationality,
        religion: visaFormData.personalInfo.religion,

        // 护照信息字段
        passport_number: visaFormData.passportInfo.passport_number,
        date_of_issue: visaFormData.passportInfo.date_of_issue,
        place_of_issue: visaFormData.passportInfo.place_of_issue,
        passport_expiry: visaFormData.passportInfo.passport_expiry,
        passport_type: visaFormData.passportInfo.passport_type,

        // 联系信息字段
        email: visaFormData.contactInfo.email,
        telephone_number: visaFormData.contactInfo.telephone_number,
        permanent_address: visaFormData.contactInfo.permanent_address,
        contact_address: visaFormData.contactInfo.contact_address,

        // 签证信息字段
        visa_entry_type: visaFormData.visaInfo.visa_entry_type,
        visa_validity_duration: visaFormData.visaInfo.visa_validity_duration,
        visa_start_date: visaFormData.visaInfo.visa_start_date,
        intended_entry_gate: visaFormData.visaInfo.intended_entry_gate,
        purpose_of_entry: visaFormData.visaInfo.purpose_of_entry,
        expedited_type: visaFormData.visaInfo.expedited_type,
        visited_vietnam_last_year: visaFormData.visaInfo.visited_vietnam_last_year,
        previous_entry_date: visaFormData.visaInfo.previous_entry_date,
        previous_exit_date: visaFormData.visaInfo.previous_exit_date,
        previous_purpose: visaFormData.visaInfo.previous_purpose,

        // Vietnam contact fields
        has_vietnam_contact: visaFormData.visaInfo.has_vietnam_contact,
        vietnam_contact_organization: visaFormData.visaInfo.vietnam_contact_organization,
        vietnam_contact_phone: visaFormData.visaInfo.vietnam_contact_phone,
        vietnam_contact_address: visaFormData.visaInfo.vietnam_contact_address,
        vietnam_contact_purpose: visaFormData.visaInfo.vietnam_contact_purpose,

        // 文件字段
        portrait_photo: visaFormData.files.portrait_photo,
        passport_scan: visaFormData.files.passport_scan,
      } as VisaFormData

      // 创建提交记录 - 严格使用护照号，绝不生成临时标识符
      const submissionId = visaFormData.passportInfo.passport_number
      if (!submissionId) {
        ElMessage.error('护照号码不能为空')
        return false
      }

      // 🔧 修复：删除重复逻辑，只使用submitApplication
      // submitApplication内部会创建应用记录，不需要重复创建
      const success = await submitApplication(submitData)

      // 🔧 修复：不要强制覆盖状态，让submitApplication内部逻辑处理

      return success
    } catch (error) {
      console.error('❌ 表单提交过程中出错:', error)
      ElMessage.error(`提交失败: ${error instanceof Error ? error.message : '未知错误'}`)

      // 🔧 修复：只有在有护照号的情况下才标记失败
      const passportNumber = visaFormData.passportInfo.passport_number
      if (passportNumber) {
        applicationStore.updateApplicationStatus(passportNumber, 'failed')
      }

      return false
    }
  }

  // 重置表单
  const resetForm = (resetFunctions: (() => void)[]) => {
    ElMessageBox.confirm('确定要重置表单吗？这将清除所有已填写的信息。', '确认重置', {
      confirmButtonText: '确定重置',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      // 重置所有表单组件
      resetFunctions.forEach((fn) => fn())

      // 🔥 新增：清除上传的文件
      removeFile('portrait')
      removeFile('passport')

      // 重置状态
      Object.keys(validationStatus).forEach((key) => {
        validationStatus[key] = false
      })

      clearOCRResult()

      // 🔥 新增：清除本地存储的表单数据（包括文件）
      await clearFormFromStorage()

      ElMessage.success('表单已重置')
    })
  }

  // 保存草稿
  const saveAsDraft = async () => {
    if (isFormDataEmpty(visaFormData)) {
      ElMessage.warning('表单为空，无需保存草稿')
      return
    }

    await saveFormToStorage(visaFormData)
    ElMessage.success({
      message: '草稿已保存到本地，刷新页面或切换页面后会自动恢复',
      duration: 4000,
    })
  }

  return {
    // 数据
    formData: visaFormData,
    validationStatus,
    submissionHistory,
    ocrStatus,

    // 计算属性
    isFormValid,
    isSubmitting,

    // 方法
    getFieldClass,
    updateValidationStatus,
    handleSectionValidate,
    handleFieldChange,
    handleOCRResult,
    clearOCRResult,
    handleFileChange,
    removeFile,
    triggerOCR,
    submitForm,
    resetForm,
    saveAsDraft,
    handleOCRError,
    handleOCRCancel,

    // 🔥 新增：表单持久化工具方法
    saveFormToStorage,
    clearFormFromStorage,
    cleanupAutoSave,
  }
}
