/**
 * 消息文本常量
 * 集中管理所有用户提示消息
 */

export const MESSAGES = {
  // 成功消息
  SUCCESS: {
    LOGIN: '登录成功',
    LOGOUT: '已安全退出',
    SUBMIT: '申请已提交',
    COPY: '已复制到剪贴板',
    SAVE: '保存成功',
    DELETE: '删除成功',
    CANCEL: '订单已取消',
  },
  
  // 错误消息
  ERROR: {
    NETWORK: '网络连接失败，请检查网络后重试',
    TIMEOUT: '请求超时，请重试',
    UNAUTHORIZED: '登录已过期，请重新登录',
    FORBIDDEN: '权限不足',
    NOT_FOUND: '请求的资源不存在',
    SERVER_ERROR: '服务器错误，请稍后重试',
    VALIDATION: '请求参数验证失败',
    UNKNOWN: '未知错误，请重试',
    COPY_FAILED: '复制失败',
    SUBMIT_FAILED: '提交失败，请重试',
  },
  
  // 警告消息
  WARNING: {
    DUPLICATE_SUBMIT: '申请正在提交中，请勿重复点击提交按钮',
    SESSION_EXPIRE_SOON: '登录即将过期，请及时保存数据',
    UNSAVED_CHANGES: '有未保存的更改，确定要离开吗？',
  },
  
  // 确认对话框
  CONFIRM: {
    DELETE: '确定要删除吗？删除后无法恢复',
    CANCEL_ORDER: '确定要取消此订单吗？取消后无法恢复',
    LOGOUT: '确定要退出登录吗？',
    CLEAR_DATA: '确定要清空所有数据吗？',
  },
  
  // 加载状态
  LOADING: {
    SUBMITTING: '正在提交...',
    LOADING: '加载中...',
    PROCESSING: '处理中...',
    UPLOADING: '上传中...',
    DOWNLOADING: '下载中...',
  },
} as const

export type MessageCategory = keyof typeof MESSAGES
export type MessageKey<T extends MessageCategory> = keyof typeof MESSAGES[T]
