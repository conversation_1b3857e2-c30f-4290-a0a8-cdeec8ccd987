/**
 * 时间相关常量
 * 集中管理所有时间间隔、延迟等配置
 */

export const TIMING = {
  // 轮询间隔
  POLLING_INTERVAL: 30000,           // 30秒 - 订单状态轮询
  STATISTICS_REFRESH: 30000,         // 30秒 - 统计数据刷新
  
  // 延迟时间
  REDIRECT_DELAY: 1500,              // 1.5秒 - 登录跳转延迟
  STATE_RESET_DELAY: 1000,           // 1秒 - 状态重置延迟
  
  // 消息显示时长
  MESSAGE_DURATION: 4000,            // 4秒 - 错误消息显示时长
  SUCCESS_MESSAGE_DURATION: 3000,   // 3秒 - 成功消息显示时长
  
  // 重复检查窗口
  DUPLICATE_CHECK_WINDOW: 60000,     // 1分钟 - 重复提交检查窗口
  
  // API 超时
  API_TIMEOUT: 30000,                // 30秒 - API请求超时
  OCR_TIMEOUT: 60000,                // 60秒 - OCR处理超时
} as const

export type TimingKey = keyof typeof TIMING
