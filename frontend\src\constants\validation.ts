import type { ValidationRules } from '@/types/form'

// 严格按照旧版 validationRules 迁移 - 不得更改任何字段名或验证规则
export const VALIDATION_RULES: ValidationRules = {
  // 个人信息验证规则 - 严格对应旧版
  surname: {
    required: true,
    minLength: 1,
    pattern: /^[A-Za-z\s]+$/,
    message: '姓氏只能包含英文字母和空格',
  },
  given_name: {
    required: true,
    minLength: 1,
    pattern: /^[A-Za-z\s]+$/,
    message: '名字只能包含英文字母和空格',
  },
  chinese_name: {
    required: false,
    pattern: /^[\u4e00-\u9fa5]+$/,
    message: '中文名只能包含中文字符',
  },
  dob: {
    required: true,
    pattern: /^\d{1,2}\/\d{1,2}\/\d{4}$/,
    message: '请输入正确的日期格式 DD/MM/YYYY',
  },
  place_of_birth: {
    required: true,
    minLength: 2,
    message: '出生地至少需要2个字符',
  },

  // 护照信息验证规则 - 严格对应旧版
  passport_number: {
    required: true,
    pattern: /^[A-Z0-9]{6,12}$/,
    message: '护照号码应为6-12位字母和数字组合',
  },
  date_of_issue: {
    required: true,
    pattern: /^\d{1,2}\/\d{1,2}\/\d{4}$/,
    message: '请输入正确的日期格式 DD/MM/YYYY',
  },
  passport_expiry: {
    required: true,
    pattern: /^\d{1,2}\/\d{1,2}\/\d{4}$/,
    message: '请输入正确的日期格式 DD/MM/YYYY',
  },

  // 联系信息验证规则 - 严格对应旧版
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: '请输入有效的邮箱地址',
  },
  telephone_number: {
    required: true,
    pattern: /^[\d\+\-\s\(\)]{8,20}$/,
    message: '请输入有效的电话号码',
  },

  // 签证信息验证规则 - 严格对应旧版
  visa_start_date: {
    required: true,
    pattern: /^\d{1,2}\/\d{1,2}\/\d{4}$/,
    message: '请输入正确的日期格式 DD/MM/YYYY',
  },
}

// 性别选项 - 严格对应旧版
export const SEX_OPTIONS = [
  { label: '男性', value: 'M' },
  { label: '女性', value: 'F' },
] as const

// 签证入境类型 - 严格对应旧版
export const VISA_ENTRY_TYPES = [
  { label: '单次入境', value: 'Single-entry' },
  { label: '多次入境', value: 'Multiple-entry' },
] as const

// 签证有效期 - 严格对应旧版
export const VISA_VALIDITY_DURATIONS = [
  { label: '30天', value: '30天' },
  { label: '90天', value: '90天' },
] as const

// 入境目的 - 严格对应旧版
export const PURPOSE_OF_ENTRY = [
  { label: '旅游', value: 'Tourist' },
  { label: '商务', value: 'Business' },
  { label: '探亲', value: 'Visiting relatives' },
  { label: '工作', value: 'Working' },
  { label: '其他', value: 'Other' },
] as const

// 加急类型 - 严格对应旧版
export const EXPEDITED_TYPES = [
  { label: '4工出签', value: '4days' },
  { label: '3工加急', value: '3days' },
  { label: '2工加急', value: '2days' },
  { label: '1工加急', value: '1days' },
] as const

// 入境口岸选项 - 严格对应旧版 HTML select 选项
export const ENTRY_GATES = [
  { label: '胡志明', value: 'Tan Son Nhat Int Airport (Ho Chi Minh City)' },
  { label: '河内', value: 'Noi Bai Int Airport' },
  { label: '岘港', value: 'Da Nang International Airport' },
  { label: '芽庄', value: 'Cam Ranh Int Airport (Khanh Hoa)' },
  { label: '东兴', value: 'Mong Cai Landport' },
  { label: '友谊', value: 'Huu Nghi Landport' },
] as const

// 默认值 - 严格对应旧版
export const DEFAULT_VALUES = {
  nationality: 'CHINA',
  religion: 'NO',
  passport_type: 'Ordinary passport',
  purpose_of_entry: 'Tourist',
  visited_vietnam_last_year: false,
} as const

// 文件上传限制 - 对应旧版文件上传规则
export const FILE_UPLOAD_RULES = {
  portrait_photo: {
    accept: ['image/jpeg', 'image/png'],
    maxSize: 5 * 1024 * 1024, // 5MB
    required: true,
  },
  passport_scan: {
    accept: ['image/jpeg', 'image/png'],
    maxSize: 5 * 1024 * 1024, // 5MB
    required: true,
  },
} as const

// 日期格式常量 - 对应旧版日期格式
export const DATE_FORMAT = 'DD/MM/YYYY'
export const DATE_REGEX = /^\d{1,2}\/\d{1,2}\/\d{4}$/

// 错误消息 - 对应旧版错误提示
export const ERROR_MESSAGES = {
  REQUIRED: '此字段为必填项',
  INVALID_FORMAT: '格式不正确',
  FILE_TOO_LARGE: '文件大小不能超过5MB',
  INVALID_FILE_TYPE: '请选择JPG或PNG格式的图片',
  NETWORK_ERROR: '网络错误，请稍后重试',
  SUBMIT_ERROR: '提交失败，请检查表单信息',
} as const
