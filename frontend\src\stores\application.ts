import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 应用状态类型定义，严格对应旧版逻辑
// 🔥 重构：分离三种状态系统（彻底删除旧字段）
export interface ApplicationItem {
  passportNumber: string
  applicantName: string
  chineseName?: string
  submissionTime: string

  // 🎯 1. 订单状态 (Order Management)
  orderStatus: 'created' | 'cancelled'
  orderNo?: string // 我们的订单编号（如 VN20250602RL34XY）

  // 🎯 2. 自动化任务状态 (Automation Task)
  automationStatus: 'processing' | 'success' | 'failed' | 'cancelled'
  automationMessage?: string

  // 🎯 3. 签证流程状态 (Visa Process)
  visaStatus?: 'submit_failure' | 'submitted' | 'additional_info_required' | 'approved' | 'denied'
  visaMessage?: string
  applicationId?: string // 越南官方申请编号
}

// 🔥 重构：分离状态枚举
export const AutomationStatus = {
  PROCESSING: 'processing' as const,
  SUCCESS: 'success' as const,
  FAILED: 'failed' as const,
  CANCELLED: 'cancelled' as const,
} as const

export const VisaStatus = {
  SUBMIT_FAILURE: 'submit_failure' as const,
  SUBMITTED: 'submitted' as const,
  ADDITIONAL_INFO_REQUIRED: 'additional_info_required' as const,
  APPROVED: 'approved' as const,
  DENIED: 'denied' as const,
} as const

// 🎯 自动化任务状态文本映射
const AUTOMATION_STATUS_TEXT_MAP = {
  processing: '申请提交中',
  success: '提交成功',
  failed: '提交失败',
  cancelled: '已取消',
} as const

// 🎯 签证流程状态文本映射
const VISA_STATUS_TEXT_MAP = {
  submit_failure: '提交失败',
  submitted: '已提交',
  additional_info_required: '需补充资料',
  approved: '已出签',
  denied: '已拒签',
} as const

// 🎯 自动化任务状态图标映射
const AUTOMATION_STATUS_ICON_MAP = {
  processing: '⏳',
  success: '✅',
  failed: '❌',
  cancelled: '🚫',
} as const

// 🎯 签证流程状态图标映射
const VISA_STATUS_ICON_MAP = {
  submit_failure: '❌',
  submitted: '📋',
  additional_info_required: '📝',
  approved: '✅',
  denied: '❌',
} as const

export const useApplicationStore = defineStore('application', () => {
  // 应用列表
  const applications = ref<ApplicationItem[]>([])

  // 计算属性：是否有已提交的申请
  const hasSubmittedApplications = computed(() => applications.value.length > 0)

  // 计算属性：按自动化任务状态统计申请数量
  const applicationStats = computed(() => ({
    total: applications.value.length,
    processing: applications.value.filter((app) => app.automationStatus === 'processing').length,
    success: applications.value.filter((app) => app.automationStatus === 'success').length,
    failed: applications.value.filter((app) => app.automationStatus === 'failed').length,
    cancelled: applications.value.filter((app) => app.automationStatus === 'cancelled').length,
  }))

  // 🔥 重构：添加应用（确保使用新数据结构）
  const addApplication = (application: ApplicationItem) => {
    // 确保应用数据包含所有必需的新字段
    const normalizedApp: ApplicationItem = {
      passportNumber: application.passportNumber,
      applicantName: application.applicantName,
      chineseName: application.chineseName,
      submissionTime: application.submissionTime,
      orderStatus: application.orderStatus || 'created',
      orderNo: application.orderNo,
      automationStatus: application.automationStatus || 'processing',
      automationMessage: application.automationMessage,
      visaStatus: application.visaStatus,
      visaMessage: application.visaMessage,
      applicationId: application.applicationId,
    }

    // 检查是否已存在相同护照号的应用
    const existingIndex = applications.value.findIndex(
      (app) => app.passportNumber === normalizedApp.passportNumber,
    )

    if (existingIndex >= 0) {
      // 更新现有应用
      applications.value[existingIndex] = {
        ...applications.value[existingIndex],
        ...normalizedApp,
      }
    } else {
      // 添加新应用
      applications.value.unshift(normalizedApp)
    }

    console.log('✅ 应用已添加/更新:', normalizedApp)
    saveToStorage()
  }

  // 🔥 重构：更新自动化任务状态
  const updateApplicationStatus = (
    passportNumber: string,
    automationStatus: 'processing' | 'success' | 'failed' | 'cancelled',
    additionalData?: Partial<ApplicationItem>,
  ) => {
    const application = applications.value.find((app) => app.passportNumber === passportNumber)
    if (application) {
      application.automationStatus = automationStatus

      // 更新额外数据
      if (additionalData) {
        Object.assign(application, additionalData)
      }

      console.log(`✅ 自动化任务状态已更新: ${passportNumber} -> ${automationStatus}`)
      saveToStorage()
    } else {
      console.warn(`⚠️ 未找到护照号为 ${passportNumber} 的应用`)
    }
  }

  // 获取应用列表
  const getApplications = () => {
    return applications.value
  }

  // 🔥 重构：获取特定自动化状态的应用
  const getApplicationsByStatus = (
    automationStatus: 'processing' | 'success' | 'failed' | 'cancelled',
  ) => {
    return applications.value.filter((app) => app.automationStatus === automationStatus)
  }

  // 清空应用列表
  const clearApplications = () => {
    applications.value = []
    console.log('✅ 应用列表已清空')
  }

  // 🔥 简化：只加载新格式数据
  const loadFromStorage = () => {
    try {
      const stored = localStorage.getItem('visa_applications')
      if (stored) {
        applications.value = JSON.parse(stored)
        console.log('✅ 从本地存储加载应用列表:', applications.value.length)
      }
    } catch (error) {
      console.error('❌ 从本地存储加载失败:', error)
      applications.value = []
    }
  }

  // 保存到本地存储
  const saveToStorage = () => {
    try {
      localStorage.setItem('visa_applications', JSON.stringify(applications.value))
      console.log('✅ 应用列表已保存到本地存储')
    } catch (error) {
      console.error('❌ 保存到本地存储失败:', error)
    }
  }

  // 监听应用列表变化，自动保存
  const autoSave = () => {
    // 使用watch或watchEffect在组件中监听变化
    saveToStorage()
  }

  // 🎯 获取自动化任务状态文本
  function getAutomationStatusText(status: ApplicationItem['automationStatus']): string {
    return AUTOMATION_STATUS_TEXT_MAP[status] || '未知'
  }

  // 🎯 获取自动化任务状态图标
  function getAutomationStatusIcon(status: ApplicationItem['automationStatus']): string {
    return AUTOMATION_STATUS_ICON_MAP[status] || '❓'
  }

  // 🎯 获取签证流程状态文本
  function getVisaStatusText(status?: ApplicationItem['visaStatus']): string {
    if (!status) return '未知'
    return VISA_STATUS_TEXT_MAP[status] || '未知'
  }

  // 🎯 获取签证流程状态图标
  function getVisaStatusIcon(status?: ApplicationItem['visaStatus']): string {
    if (!status) return '❓'
    return VISA_STATUS_ICON_MAP[status] || '❓'
  }

  // 🔧 兼容性：获取主要显示状态（优先显示签证状态，其次自动化状态）
  function getDisplayStatus(app: ApplicationItem): {
    text: string
    icon: string
    type: 'automation' | 'visa'
  } {
    if (app.visaStatus) {
      return {
        text: getVisaStatusText(app.visaStatus),
        icon: getVisaStatusIcon(app.visaStatus),
        type: 'visa',
      }
    }
    return {
      text: getAutomationStatusText(app.automationStatus),
      icon: getAutomationStatusIcon(app.automationStatus),
      type: 'automation',
    }
  }

  // 获取按时间排序的申请列表（最新的在前）
  const sortedApplications = computed(() => {
    return [...applications.value].sort(
      (a, b) => new Date(b.submissionTime).getTime() - new Date(a.submissionTime).getTime(),
    )
  })

  // 🔥 新增：更新应用的订单编号（订单系统返回后）
  const updateApplicationOrderNo = (
    passportNumber: string,
    orderNo: string,
    applicationId?: string,
  ) => {
    const existingIndex = applications.value.findIndex(
      (app) => app.passportNumber === passportNumber,
    )

    if (existingIndex >= 0) {
      applications.value[existingIndex] = {
        ...applications.value[existingIndex],
        orderNo,
        applicationId,
      }

      console.log(`✅ 订单编号已更新: ${passportNumber} -> ${orderNo}`)
      saveToStorage()
    }
  }

  // 🔥 重构：通过订单编号更新自动化任务状态（用于轮询）
  const updateApplicationStatusByOrderNo = (
    orderNo: string,
    automationStatus: 'processing' | 'success' | 'failed' | 'cancelled',
    additionalData?: Partial<ApplicationItem>,
  ) => {
    const application = applications.value.find((app) => app.orderNo === orderNo)
    if (application) {
      application.automationStatus = automationStatus

      // 更新额外数据
      if (additionalData) {
        Object.assign(application, additionalData)
      }

      console.log(`✅ 自动化任务状态已更新(通过订单编号): ${orderNo} -> ${automationStatus}`)
      saveToStorage()
    } else {
      console.warn(`⚠️ 未找到订单编号为 ${orderNo} 的应用`)
    }
  }

  return {
    applications,
    hasSubmittedApplications,
    applicationStats,
    sortedApplications,
    addApplication,
    updateApplicationStatus,
    updateApplicationStatusByOrderNo,
    getApplications,
    getApplicationsByStatus,
    clearApplications,
    loadFromStorage,
    saveToStorage,
    autoSave,
    // 🎯 状态显示函数
    getAutomationStatusText,
    getAutomationStatusIcon,
    getVisaStatusText,
    getVisaStatusIcon,
    getDisplayStatus,
    updateApplicationOrderNo,
  }
})
