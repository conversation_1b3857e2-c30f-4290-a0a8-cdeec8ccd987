import { getUserInfo<PERSON><PERSON>, login<PERSON>pi, logout<PERSON>pi } from '@/api/auth'
import type { LoginRequest, User } from '@/types/auth'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { useNotificationStore } from './notification'

export const useAuthStore = defineStore(
  'auth',
  () => {
    // 🔧 修复：使用统一的通知系统
    const notificationStore = useNotificationStore()

    // 状态
    const token = ref<string | null>(localStorage.getItem('auth_token'))
    const user = ref<User | null>(null)
    const isLoading = ref(false)

    // 计算属性
    const isAuthenticated = computed(() => !!token.value && !!user.value)

    // 登录方法
    const login = async (loginData: LoginRequest): Promise<boolean> => {
      try {
        isLoading.value = true
        notificationStore.clearMessages() // 清除之前的消息

        console.log('🔐 开始登录:', { username: loginData.username, remember: loginData.remember })

        const response = await loginApi(loginData)

        console.log('📦 登录响应:', response)

        if (response.success) {
          // 保存token到localStorage和状态
          token.value = response.data.token
          user.value = response.data.user
          localStorage.setItem('auth_token', response.data.token)

          // 🔥 新增：处理"记住我"功能
          if (loginData.remember) {
            // 记住用户名和密码
            localStorage.setItem('remembered_username', loginData.username)
            localStorage.setItem('remembered_password', loginData.password)
            localStorage.setItem('remember_me', 'true')
            console.log('💾 已保存登录信息（记住我）')
          } else {
            // 清除记住的登录信息
            localStorage.removeItem('remembered_username')
            localStorage.removeItem('remembered_password')
            localStorage.removeItem('remember_me')
            console.log('🧹 已清除记住的登录信息')
          }

          console.log('✅ 登录成功:', {
            user: response.data.user,
            token: response.data.token ? 'Token已设置' : 'Token未设置',
          })

          // 🔧 修复：使用统一通知系统
          notificationStore.showSuccess('登录成功')
          return true
        } else {
          console.log('❌ 登录失败:', response.message)
          // 🔧 修复：使用统一通知系统
          notificationStore.showError(response.message || '登录失败')
          return false
        }
      } catch (error: unknown) {
        console.error('💥 登录异常:', error)
        // 🔧 修复：使用统一通知系统
        notificationStore.showError(
          error instanceof Error ? error.message : '登录过程中发生未知错误',
        )
        return false
      } finally {
        isLoading.value = false
      }
    }

    // 获取用户信息
    const getUserInfo = async (): Promise<boolean> => {
      if (!token.value) return false

      try {
        console.log('👤 获取用户信息...')
        const response = await getUserInfoApi()

        if (response.success) {
          user.value = response.data
          console.log('✅ 用户信息获取成功:', response.data)
          return true
        } else {
          // Token可能已过期，清除认证信息
          console.log('❌ 用户信息获取失败，清除认证状态')
          await logout()
          // 🔧 修复：使用统一通知系统
          notificationStore.showAuthError('用户信息获取失败，请重新登录')
          return false
        }
      } catch (error) {
        console.error('💥 获取用户信息异常:', error)
        await logout()
        // 🔧 修复：使用统一通知系统
        notificationStore.showAuthError('获取用户信息失败，请重新登录')
        return false
      }
    }

    // 登出方法
    const logout = async (): Promise<void> => {
      try {
        if (token.value) {
          await logoutApi()
        }
      } catch (error) {
        console.error('登出API调用失败:', error)
      } finally {
        // 无论API调用是否成功，都清除本地状态
        token.value = null
        user.value = null
        localStorage.removeItem('auth_token')
        notificationStore.clearMessages() // 清除消息状态
        console.log('🚪 已退出登录')
      }
    }

    // 检查Token有效性
    const checkTokenValidity = async (): Promise<boolean> => {
      if (!token.value) return false

      console.log('🔍 检查Token有效性...')

      // 如果有token但没有用户信息，尝试获取用户信息
      if (!user.value) {
        return await getUserInfo()
      }

      return true
    }

    // 清除认证信息（用于Token失效时）
    const clearAuth = (): void => {
      token.value = null
      user.value = null
      localStorage.removeItem('auth_token')
      notificationStore.clearMessages() // 清除消息状态
      console.log('🧹 认证信息已清除')
    }

    // 🔥 新增：获取记住的登录信息
    const getRememberedCredentials = (): {
      username: string
      password: string
      remember: boolean
    } | null => {
      const rememberMe = localStorage.getItem('remember_me') === 'true'
      if (rememberMe) {
        const username = localStorage.getItem('remembered_username') ?? ''
        const password = localStorage.getItem('remembered_password') ?? ''
        if (username && password) {
          console.log('💾 已加载记住的登录信息:', { username })
          return { username, password, remember: true }
        }
      }
      return null
    }

    // 🔥 新增：清除记住的登录信息
    const clearRememberedCredentials = (): void => {
      localStorage.removeItem('remembered_username')
      localStorage.removeItem('remembered_password')
      localStorage.removeItem('remember_me')
      console.log('🧹 已清除记住的登录信息')
    }

    return {
      // 状态
      token: computed(() => token.value),
      user: computed(() => user.value),
      isLoading: computed(() => isLoading.value),
      isAuthenticated,

      // 方法
      login,
      logout,
      getUserInfo,
      checkTokenValidity,
      clearAuth,
      getRememberedCredentials,
      clearRememberedCredentials,

      // 🔧 移除：通知状态由 useNotificationStore 统一管理
    }
  },
  {
    // 启用持久化 - 提升用户体验
    persist: {
      key: 'auth-store',
      storage: localStorage,
    },
  },
)
