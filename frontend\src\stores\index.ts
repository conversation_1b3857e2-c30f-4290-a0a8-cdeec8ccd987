/**
 * Pinia Store导出文件
 * 集中导出所有Store，避免重复创建Pinia实例
 *
 * 🔄 API变更说明：
 * - useBusinessStore 已被移除（职责重叠）
 * - 请使用 useApplicationStore + useSubmissionStore 的组合方案
 * - ApplicationStore: 负责数据管理和状态展示
 * - SubmissionStore: 负责流程控制和业务逻辑
 */

// 导出所有Store
export { useApplicationStore } from './application'
export { useAuthStore } from './auth'
export { useNotificationStore } from './notification'
export { useSessionStore } from './session'
export { useSubmissionStore } from './submission'
export { useVisaFormStore } from './visaForm'
