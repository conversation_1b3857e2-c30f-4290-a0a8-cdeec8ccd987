import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface FormSnapshot {
  personalInfo: Record<string, unknown>
  passportInfo: Record<string, unknown>
  contactInfo: Record<string, unknown>
  visaInfo: Record<string, unknown>
  files: Record<string, unknown>
  timestamp: string
}

export const useVisaFormStore = defineStore('visaForm', () => {
  const currentSnapshot = ref<FormSnapshot | null>(null)

  const saveSnapshot = (formData: Record<string, unknown>) => {
    const deepClone = (obj: unknown): Record<string, unknown> => {
      if (obj && typeof obj === 'object') {
        return JSON.parse(JSON.stringify(obj))
      }
      return {}
    }

    currentSnapshot.value = {
      personalInfo: deepClone(formData.personalInfo),
      passportInfo: deepClone(formData.passportInfo),
      contactInfo: deepClone(formData.contactInfo),
      visaInfo: deepClone(formData.visaInfo),
      files: deepClone(formData.files),
      timestamp: new Date().toLocaleString('zh-CN'),
    }
  }

  const getSnapshot = () => currentSnapshot.value

  const clearSnapshot = () => {
    currentSnapshot.value = null
  }

  return {
    currentSnapshot,
    saveSnapshot,
    getSnapshot,
    clearSnapshot,
  }
})
