// 用户信息类型
export interface User {
  id: string
  username: string
  email: string
  role: string
  createdAt: string
  updatedAt: string
}

// 登录请求类型
export interface LoginRequest {
  username: string
  password: string
  remember?: boolean
}

// 登录响应数据类型
export interface LoginResponseData {
  token: string
  user: User
  expiresIn: number
}

// API响应基础类型
export interface ApiResponse<T = unknown> {
  success: boolean
  message: string
  data: T
  code?: number
}

// 登录响应类型
export type LoginResponse = ApiResponse<LoginResponseData>

// 用户信息响应类型
export type UserInfoResponse = ApiResponse<User>

// 表单验证规则类型
export interface LoginFormRules {
  username: Array<{
    required?: boolean
    message: string
    trigger?: string | string[]
    min?: number
    max?: number
  }>
  password: Array<{
    required?: boolean
    message: string
    trigger?: string | string[]
    min?: number
    max?: number
  }>
}
