/**
 * 日期时间工具函数
 * 统一管理所有日期时间相关的格式化和验证
 */

/**
 * 格式化时间为本地化字符串
 * @param timeStr 时间字符串或Date对象
 * @param options 格式化选项
 * @returns 格式化后的时间字符串
 */
export const formatTime = (
  timeStr: string | Date,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  }
): string => {
  try {
    const date = typeof timeStr === 'string' ? new Date(timeStr) : timeStr
    return date.toLocaleString('zh-CN', options)
  } catch {
    return typeof timeStr === 'string' ? timeStr : timeStr.toString()
  }
}

/**
 * 验证日期格式 DD/MM/YYYY
 * @param dateStr 日期字符串
 * @returns 是否为有效日期
 */
export const isValidDate = (dateStr: string): boolean => {
  if (!dateStr) return false

  const regex = /^\d{2}\/\d{2}\/\d{4}$/
  if (!regex.test(dateStr)) return false

  const [day, month, year] = dateStr.split('/').map(Number)
  const date = new Date(year, month - 1, day)

  return date.getDate() === day && date.getMonth() === month - 1 && date.getFullYear() === year
}

/**
 * 格式化日期为 DD/MM/YYYY 格式
 * @param date Date对象或时间戳
 * @returns DD/MM/YYYY 格式的日期字符串
 */
export const formatDateDDMMYYYY = (date: Date | number): string => {
  try {
    const d = typeof date === 'number' ? new Date(date) : date
    const day = d.getDate().toString().padStart(2, '0')
    const month = (d.getMonth() + 1).toString().padStart(2, '0')
    const year = d.getFullYear()
    return `${day}/${month}/${year}`
  } catch {
    return ''
  }
}

/**
 * 解析 DD/MM/YYYY 格式的日期字符串为 Date 对象
 * @param dateStr DD/MM/YYYY 格式的日期字符串
 * @returns Date对象，解析失败返回null
 */
export const parseDateDDMMYYYY = (dateStr: string): Date | null => {
  if (!isValidDate(dateStr)) return null

  const [day, month, year] = dateStr.split('/').map(Number)
  return new Date(year, month - 1, day)
}
