/**
 * 统一的导航工具
 * 优先使用Vue Router，无法访问时回退到原生重定向
 */

import type { Router } from 'vue-router'

// 全局router引用，由main.ts设置
let globalRouter: Router | null = null

/**
 * 设置全局router实例（由main.ts调用）
 */
export function setGlobalRouter(router: Router) {
  globalRouter = router
}

/**
 * 重定向到登录页面
 * 优先使用Vue Router，无法访问时使用原生重定向
 */
export function redirectToLogin() {
  redirectTo('/login', true)
}

/**
 * 重定向到指定路径
 * 优先使用Vue Router，无法访问时使用原生重定向
 * @param path 目标路径
 * @param replace 是否使用replace而不是push（仅用于原生重定向）
 */
export function redirectTo(path: string, replace = false) {
  console.log(`🔄 重定向到: ${path}`)

  // 优先使用Vue Router
  if (globalRouter) {
    globalRouter.push(path).catch((error) => {
      console.warn('Vue Router重定向失败，使用原生重定向:', error)
      // 回退到原生重定向
      if (typeof window !== 'undefined') {
        const currentPath = window.location.pathname
        if (currentPath !== path) {
          if (replace) {
            window.location.replace(path)
          } else {
            window.location.href = path
          }
        }
      }
    })
    return
  }

  // 回退到原生重定向
  if (typeof window !== 'undefined') {
    const currentPath = window.location.pathname
    if (currentPath !== path) {
      if (replace) {
        window.location.replace(path)
      } else {
        window.location.href = path
      }
    }
  }
}
