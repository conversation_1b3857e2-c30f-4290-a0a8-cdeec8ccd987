/**
 * 统一通知系统
 * 为整个应用提供一致的通知接口，解耦UI框架依赖
 *
 * 设计原则：
 * 1. API层、Store层、Composables层都通过此接口显示通知
 * 2. 组件层可直接使用ElMessage，但推荐使用此接口
 * 3. 统一接口便于测试、维护和UI框架替换
 */

import type { ElMessageBoxOptions, MessageParamsWithType } from 'element-plus'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'

// ========== 基础消息通知 ==========

/**
 * 显示错误消息
 * @param message 错误消息文本或配置对象
 */
export function showError(message: string | MessageParamsWithType): void {
  if (typeof message === 'string') {
    ElMessage.error(message)
  } else {
    ElMessage.error(message)
  }
}

/**
 * 显示成功消息
 * @param message 成功消息文本或配置对象
 */
export function showSuccess(message: string | MessageParamsWithType): void {
  if (typeof message === 'string') {
    ElMessage.success(message)
  } else {
    ElMessage.success(message)
  }
}

/**
 * 显示警告消息
 * @param message 警告消息文本或配置对象
 */
export function showWarning(message: string | MessageParamsWithType): void {
  if (typeof message === 'string') {
    ElMessage.warning(message)
  } else {
    ElMessage.warning(message)
  }
}

/**
 * 显示信息消息
 * @param message 信息消息文本或配置对象
 */
export function showInfo(message: string | MessageParamsWithType): void {
  if (typeof message === 'string') {
    ElMessage.info(message)
  } else {
    ElMessage.info(message)
  }
}

// ========== 确认对话框 ==========

/**
 * 显示确认对话框
 * @param message 确认消息
 * @param title 对话框标题
 * @param options 额外配置选项
 * @returns Promise<boolean> - true表示确认，false表示取消
 */
export async function showConfirm(
  message: string,
  title?: string,
  options?: Partial<ElMessageBoxOptions>,
): Promise<boolean> {
  try {
    await ElMessageBox.confirm(message, title || '确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      ...options,
    })
    return true
  } catch {
    return false // 用户取消
  }
}

/**
 * 显示删除确认对话框（预设样式）
 * @param message 确认消息
 * @param title 对话框标题
 * @returns Promise<boolean>
 */
export async function showDeleteConfirm(
  message: string,
  title: string = '确认删除',
): Promise<boolean> {
  return showConfirm(message, title, {
    confirmButtonText: '确定删除',
    cancelButtonText: '取消',
    type: 'error',
    confirmButtonClass: 'el-button--danger',
  })
}

/**
 * 显示重置确认对话框（预设样式）
 * @param message 确认消息
 * @param title 对话框标题
 * @returns Promise<boolean>
 */
export async function showResetConfirm(
  message: string,
  title: string = '确认重置',
): Promise<boolean> {
  return showConfirm(message, title, {
    confirmButtonText: '确定重置',
    cancelButtonText: '取消',
    type: 'warning',
  })
}

// ========== 桌面通知 ==========

/**
 * 显示桌面通知
 * @param message 通知消息
 * @param type 通知类型
 * @param title 通知标题
 * @param duration 显示时长（毫秒）
 */
export function showNotification(
  message: string,
  type: 'success' | 'warning' | 'info' | 'error' = 'info',
  title?: string,
  duration: number = 4500,
): void {
  ElNotification({
    title: title || '通知',
    message,
    type,
    duration,
  })
}

/**
 * 显示成功桌面通知
 */
export function showSuccessNotification(message: string, title?: string): void {
  showNotification(message, 'success', title || '操作成功')
}

/**
 * 显示错误桌面通知
 */
export function showErrorNotification(message: string, title?: string): void {
  showNotification(message, 'error', title || '操作失败')
}

/**
 * 显示警告桌面通知
 */
export function showWarningNotification(message: string, title?: string): void {
  showNotification(message, 'warning', title || '注意')
}

// ========== 特殊用途消息 ==========

/**
 * 显示加载中消息
 * @param message 加载消息
 * @param duration 显示时长，0表示不自动关闭
 * @returns 消息实例，可用于手动关闭
 */
export function showLoading(message: string = '加载中...', duration: number = 0) {
  return ElMessage({
    message,
    type: 'info',
    duration,
    showClose: true,
  })
}

/**
 * 显示进度消息（用于长时间操作）
 * @param message 进度消息
 * @param duration 显示时长
 */
export function showProgress(message: string, duration: number = 0) {
  return ElMessage({
    message,
    type: 'info',
    duration,
    showClose: true,
    customClass: 'progress-message',
  })
}

// ========== 业务特定消息 ==========

/**
 * 显示认证相关错误
 * @param message 错误消息
 */
export function showAuthError(message: string = '登录已过期，请重新登录'): void {
  showError({
    message,
    duration: 5000,
    showClose: true,
  })
}

/**
 * 显示网络错误
 * @param message 错误消息
 */
export function showNetworkError(message: string = '网络请求失败，请检查网络连接'): void {
  showError({
    message,
    duration: 6000,
    showClose: true,
  })
}

/**
 * 显示表单验证错误
 * @param message 错误消息
 */
export function showValidationError(message: string): void {
  showError({
    message,
    duration: 4000,
    showClose: true,
  })
}

/**
 * 显示操作成功消息（用于表单提交、数据保存等）
 * @param message 成功消息
 */
export function showOperationSuccess(message: string): void {
  showSuccess({
    message,
    duration: 3000,
    showClose: false,
  })
}

// ========== 复制粘贴便利方法 ==========

/**
 * 显示复制成功消息
 * @param content 复制的内容描述
 */
export function showCopySuccess(content: string = '内容'): void {
  showSuccess(`${content}已复制`)
}

/**
 * 显示复制失败消息
 */
export function showCopyError(): void {
  showError('复制失败')
}
