import type { OrderStatus } from '@/api/types'

/**
 * 订单编号生成和管理工具类
 * 注意：实际编号生成只能在后端进行，这里只提供格式验证和解析功能
 */
export class OrderNumberGenerator {
  // 订单编号格式：VN + 8位日期 + 6位随机码
  private static readonly ORDER_NO_PATTERN = /^VN\d{8}[A-Z0-9]{6}$/
  private static readonly COUNTRY_PREFIX = 'VN'
  private static readonly DATE_LENGTH = 8
  private static readonly RANDOM_LENGTH = 6
  private static readonly TOTAL_LENGTH = 16 // VN(2) + 日期(8) + 随机(6)

  /**
   * 验证订单编号格式是否正确
   */
  static validate(orderNo: string): boolean {
    if (!orderNo || typeof orderNo !== 'string') {
      return false
    }

    return this.ORDER_NO_PATTERN.test(orderNo)
  }

  /**
   * 从订单编号中提取日期部分
   * @param orderNo 订单编号，如：VN20241215A1B2C3
   * @returns 日期字符串，如：20241215，如果格式错误返回null
   */
  static extractDate(orderNo: string): string | null {
    if (!this.validate(orderNo)) {
      return null
    }

    const dateStr = orderNo.substring(2, 10) // 跳过VN前缀，取8位日期

    // 验证日期格式是否有效
    const year = parseInt(dateStr.substring(0, 4))
    const month = parseInt(dateStr.substring(4, 6))
    const day = parseInt(dateStr.substring(6, 8))

    // 基础日期验证
    if (year < 2024 || year > 2030 || month < 1 || month > 12 || day < 1 || day > 31) {
      return null
    }

    return dateStr
  }

  /**
   * 从订单编号中提取随机码部分
   */
  static extractRandomCode(orderNo: string): string | null {
    if (!this.validate(orderNo)) {
      return null
    }

    return orderNo.substring(10) // 跳过VN前缀和8位日期
  }

  /**
   * 格式化显示订单编号（保持原始格式，简洁明了）
   * VN20241215A1B2C3 -> VN20241215A1B2C3 (不添加分隔符)
   */
  static formatDisplay(orderNo: string): string {
    if (!this.validate(orderNo)) {
      return orderNo
    }

    // 用户要求：简洁、清晰、明确，不需要分隔符
    return orderNo
  }

  /**
   * 获取订单编号的创建日期（解析后的Date对象）
   */
  static getCreatedDate(orderNo: string): Date | null {
    const dateStr = this.extractDate(orderNo)
    if (!dateStr) {
      return null
    }

    const year = parseInt(dateStr.substring(0, 4))
    const month = parseInt(dateStr.substring(4, 6)) - 1 // JavaScript月份从0开始
    const day = parseInt(dateStr.substring(6, 8))

    try {
      const date = new Date(year, month, day)
      // 验证日期是否有效
      if (date.getFullYear() !== year || date.getMonth() !== month || date.getDate() !== day) {
        return null
      }
      return date
    } catch {
      return null
    }
  }

  /**
   * 检查订单编号是否是今天创建的
   */
  static isCreatedToday(orderNo: string): boolean {
    const createdDate = this.getCreatedDate(orderNo)
    if (!createdDate) {
      return false
    }

    const today = new Date()
    return (
      createdDate.getFullYear() === today.getFullYear() &&
      createdDate.getMonth() === today.getMonth() &&
      createdDate.getDate() === today.getDate()
    )
  }

  /**
   * 生成用于显示的订单编号掩码（隐藏部分随机码）
   * VN20241215A1B2C3 -> VN20241215****C3
   */
  static getMaskedOrderNo(orderNo: string): string {
    if (!this.validate(orderNo)) {
      return orderNo
    }

    const prefix = orderNo.substring(0, 10) // VN + 日期
    const randomCode = orderNo.substring(10)
    const masked = randomCode.substring(0, 1) + '****' + randomCode.substring(5)

    return prefix + masked
  }
}

/**
 * 订单状态帮助工具类
 */
export class OrderStatusHelper {
  // 状态显示文本映射 - 根据修正后的业务流程
  private static readonly STATUS_TEXT_MAP: Record<OrderStatus, string> = {
    created: '订单已创建',
    processing: '处理中',
    success: '提交成功',
    failed: '提交失败',
    cancelled: '已取消',
    pending_approve: '等待审批',
    approved: '已审批',
    pending_download: '等待下载',
    downloaded: '已下载',
    paid: '已付款',
    unknown: '状态未知',
    reserved1: '扩展状态1',
    reserved2: '扩展状态2',
    reserved3: '扩展状态3',
  }

  // 状态样式类型映射
  private static readonly STATUS_TYPE_MAP: Record<
    OrderStatus,
    'success' | 'warning' | 'danger' | 'info'
  > = {
    created: 'info',
    processing: 'warning',
    success: 'success',
    pending_approve: 'warning',
    approved: 'success',
    pending_download: 'warning',
    downloaded: 'success',
    paid: 'success',
    failed: 'danger',
    cancelled: 'info',
    unknown: 'info',
    reserved1: 'info',
    reserved2: 'info',
    reserved3: 'info',
  }

  /**
   * 获取状态显示文本
   */
  static getStatusText(status: OrderStatus): string {
    return this.STATUS_TEXT_MAP[status] || status
  }

  /**
   * 获取状态样式类型（用于Element Plus的tag组件）
   */
  static getStatusType(status: OrderStatus): 'success' | 'warning' | 'danger' | 'info' {
    return this.STATUS_TYPE_MAP[status] || 'info'
  }

  /**
   * 判断订单是否可以重试
   */
  static canRetry(status: OrderStatus): boolean {
    return status === 'failed'
  }

  /**
   * 判断订单是否可以下载
   */
  static canDownload(status: OrderStatus): boolean {
    return ['approved', 'pending_download', 'downloaded', 'paid'].includes(status)
  }

  /**
   * 判断订单是否可以取消
   */
  static canCancel(status: OrderStatus): boolean {
    return status === 'created'
  }

  /**
   * 获取订单的下一步操作建议
   */
  static getNextSteps(status: OrderStatus): string[] {
    const steps: Record<OrderStatus, string[]> = {
      created: ['等待系统处理', '如长时间无变化请联系客服'],
      processing: ['正在处理中', '请耐心等待'],
      success: ['填表提交成功', '等待进入审批流程'],
      pending_approve: ['等待越南官方审批', '请耐心等待'],
      approved: ['审批通过', '准备进入付款流程'],
      pending_download: ['签证可下载', '请及时下载'],
      downloaded: ['签证已下载', '请检查文件完整性'],
      paid: ['付款完成', '申请流程已完成'],
      failed: ['处理失败', '可点击重试或联系客服'],
      cancelled: ['订单已取消', '如需重新申请请创建新订单'],
      unknown: ['状态异常', '请联系客服'],
      reserved1: ['扩展操作1'],
      reserved2: ['扩展操作2'],
      reserved3: ['扩展操作3'],
    }

    return steps[status] || ['请联系客服']
  }

  /**
   * 获取状态进度百分比（用于进度条显示）
   */
  static getProgressPercentage(status: OrderStatus): number {
    const progressMap: Record<OrderStatus, number> = {
      created: 10,
      processing: 20,
      success: 30,
      pending_approve: 50,
      approved: 70,
      pending_download: 85,
      downloaded: 95,
      paid: 100,
      failed: 0,
      cancelled: 0,
      unknown: 0,
      reserved1: 0,
      reserved2: 0,
      reserved3: 0,
    }

    return progressMap[status] || 0
  }
}

export default {
  OrderNumberGenerator,
  OrderStatusHelper,
}
