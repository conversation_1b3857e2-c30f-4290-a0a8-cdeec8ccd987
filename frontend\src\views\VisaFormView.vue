<template>
  <div class="visa-form-view">
    <el-row :gutter="20" class="form-layout">
      <!-- 主表单区域 -->
      <el-col :xs="24" :sm="24" :md="18" :lg="18" :xl="18" class="form-main">
        <el-form
          ref="formRef"
          :model="formData"
          label-width="120px"
          label-position="top"
          class="visa-form"
        >
          <!-- 文件上传与签证申请详情合并区域 -->
          <VisaFormContainer
            v-model="formData.visaInfo"
            :portrait-file="formData.files.portrait_photo"
            :passport-file="formData.files.passport_scan"
            :portrait-preview="formData.files.portrait_preview"
            :passport-preview="formData.files.passport_preview"
            :is-submitting="isSubmitting"
            :ocr-processing="ocrStatus.isProcessing"
            :get-field-class="getFieldClass"
            @file-change="handleFileChange"
            @file-remove="removeFile"
            @trigger-ocr="triggerOCR"
            @ocr-result="handleOCRResult"
            @ocr-error="handleOCRError"
            @ocr-cancel="handleOCRCancel"
            @validate-visa="(isValid: boolean) => handleSectionValidate('visaInfo', isValid)"
          />

          <!-- 个人信息表单 -->
          <VisaPersonalInfoForm
            v-model:personalInfo="formData.personalInfo"
            v-model:passportInfo="formData.passportInfo"
            v-model:contactInfo="formData.contactInfo"
            :get-field-class="getFieldClass"
            @validate-personal="(isValid) => handleSectionValidate('personalInfo', isValid)"
            @validate-passport="(isValid) => handleSectionValidate('passportInfo', isValid)"
            @validate-contact="(isValid) => handleSectionValidate('contactInfo', isValid)"
          />

          <!-- 表单按钮 -->
          <VisaFormButtons
            :is-form-valid="isFormValid"
            :is-submitting="isSubmitting"
            @submit-form="handleSubmit"
            @reset-form="handleReset"
            @save-as-draft="saveAsDraft"
            @show-snapshot="showDataSnapshot"
          />
        </el-form>
      </el-col>

      <!-- 实时状态面板  -->
      <el-col :xs="24" :sm="24" :md="6" :lg="6" :xl="6" class="form-status">
        <VisaSubmissionStatusPanel @retry="handleRetrySubmission" />
      </el-col>
    </el-row>

    <!-- 数据快照预览对话框 -->
    <el-dialog
      v-model="showSnapshotDialog"
      title="提交预览（请仔细核对信息，确认无误后再提交）"
      width="1200px"
      :before-close="cancelSnapshot"
      class="snapshot-preview-dialog"
    >
      <DataSnapshotDialog @confirm="confirmSubmitFromSnapshot" @cancel="cancelSnapshot" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useVisaForm } from '@/composables/useVisaForm'
import VisaFormContainer from '@/components/visa/VisaFormContainer.vue'
import VisaPersonalInfoForm from '@/components/visa/VisaPersonalInfoForm.vue'
import VisaFormButtons from '@/components/visa/VisaFormButtons.vue'
import VisaSubmissionStatusPanel from '@/components/visa/VisaSubmissionStatusPanel.vue'
import DataSnapshotDialog from '@/components/common/DataSnapshotDialog.vue'
import { useVisaFormStore } from '@/stores/visaForm'

// 表单引用
const formRef = ref()

// 数据快照相关
const visaFormStore = useVisaFormStore()
const showSnapshotDialog = ref(false)

// 使用组合式API获取所有表单逻辑
const {
  // 数据
  formData,
  ocrStatus,

  // 计算属性
  isFormValid,
  isSubmitting,

  // 方法
  getFieldClass,
  handleOCRResult,
  handleOCRError,
  handleOCRCancel,
  handleFileChange,
  triggerOCR,
  submitForm,
  resetForm,
  saveAsDraft,
  removeFile,
  handleSectionValidate,
} = useVisaForm()

// 提交逻辑，保持表单验证但按钮始终可点击
const handleSubmit = async () => {
  console.log('🚀 点击提交申请，开始验证表单')

  // 使用 useVisaForm 的内置验证和提交逻辑
  await submitForm([])
}

// 重新定义提交函数，使用表单引用验证
const handleReset = () => {
  resetForm([() => formRef.value?.resetFields()])
}

// 处理重试提交 - 直接调用现有的提交流程
const handleRetrySubmission = async (application: { passportNumber: string }) => {
  console.log('🔄 重试提交申请:', application.passportNumber)
  // 重试时也弹出数据快照预览
  showDataSnapshot()
}

// 显示数据快照预览
const showDataSnapshot = () => {
  console.log('📸 显示数据快照预览')
  // 保存当前表单数据到store
  visaFormStore.saveSnapshot(formData)
  // 显示快照对话框
  showSnapshotDialog.value = true
}

// 确认提交（从快照对话框）
const confirmSubmitFromSnapshot = async () => {
  showSnapshotDialog.value = false
  await handleSubmit()
}

// 取消快照预览
const cancelSnapshot = () => {
  showSnapshotDialog.value = false
}
</script>

<style scoped lang="scss">
.visa-form-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;

  .form-layout {
    max-width: 1400px;
    margin: 0 auto;
  }

  .form-main {
    .visa-form {
      // 表单样式继承现有样式
    }
  }

  .form-status {
    // 状态面板样式继承现有样式
  }
}

// 响应式设计
@media (max-width: 768px) {
  .visa-form-view {
    padding: 15px;

    .form-status {
      margin-top: 20px;
    }
  }
}
</style>
