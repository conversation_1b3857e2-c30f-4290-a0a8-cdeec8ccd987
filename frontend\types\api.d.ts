/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/api/login": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Compatible Login
         * @description 兼容原有/api/login路径
         *     使用fastapi-users认证，保持响应格式一致
         */
        post: operations["compatible_login_api_login_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/logout": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Compatible Logout
         * @description 兼容原有/api/logout路径
         *     JWT是无状态的，只需要客户端删除token
         */
        post: operations["compatible_logout_api_logout_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/session-status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Compatible Session Status
         * @description 兼容原有/api/session-status路径
         *     使用fastapi-users的current_user依赖
         */
        get: operations["compatible_session_status_api_session_status_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/user/profile": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Compatible User Profile
         * @description 兼容原有/api/user/profile路径
         *     获取当前用户资料
         */
        get: operations["compatible_user_profile_api_user_profile_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/refresh-token": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Compatible Refresh Token
         * @description 兼容原有/api/refresh-token路径
         *     刷新访问令牌
         */
        post: operations["compatible_refresh_token_api_refresh_token_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/apply": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Apply Visa
         * @description 提交签证申请（包含文件上传）
         *
         *     - **form_data**: 签证申请表单数据
         *     - **portrait_photo**: 证件照片文件
         *     - **passport_scan**: 护照扫描文件
         *     - **返回**: 申请结果和跟踪信息
         */
        post: operations["apply_visa_api_visa_apply_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/cancel": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Cancel Application
         * @description 取消申请 - 简单版本
         *
         *     检查是否可以取消（付款未完成 且 申请未成功提交），如果可以则取消Celery任务
         */
        post: operations["cancel_application_api_visa_cancel_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/status/{application_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Visa Status
         * @description 查询签证申请状态
         *
         *     - **application_id**: 申请编号或护照号码
         *     - **返回**: 申请状态信息
         */
        get: operations["get_visa_status_api_visa_status__application_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/user/history/{passport_number}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get User History
         * @description 获取用户申请历史记录
         *
         *     - **passport_number**: 护照号码
         *     - **返回**: 用户的所有申请记录
         */
        get: operations["get_user_history_api_visa_user_history__passport_number__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/applications": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Applications
         * @description 获取申请列表
         *
         *     - **page**: 页码
         *     - **page_size**: 每页大小
         *     - **status**: 状态筛选
         *     - **返回**: 申请列表
         */
        get: operations["list_applications_api_visa_applications_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/applications/{application_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        /**
         * Cancel Application
         * @description 取消申请
         *
         *     - **application_id**: 申请ID
         *     - **返回**: 取消结果
         */
        delete: operations["cancel_application_api_visa_applications__application_id__delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/calculate-expedited-date": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Expedited Date
         * @description 计算出签生效日期（从今天起第N个工作日）
         *
         *     - **days**: 工作日数量 (1, 2, 3 或 4，默认 4)
         *     - **返回**: 计算后的出签生效日期和相关信息
         */
        get: operations["get_expedited_date_api_visa_calculate_expedited_date_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/check-duplicate/{passport_number}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Check Duplicate Submission
         * @description 检查是否存在重复提交
         *
         *     🔥 基于修复后的订单系统：
         *     - 每次提交都会生成新订单
         *     - 重复检查基于业务逻辑：是否已有成功/处理中的申请
         *     - 失败的申请可以重新提交，无需弹窗
         *
         *     - **passport_number**: 护照号码
         *     - **返回**: 是否存在重复提交的信息
         *
         *     业务逻辑（基于订单状态）：
         *     1. 失败订单(status=failed) - 可直接重新提交，无需弹窗
         *     2. 创建中订单(status=created) - 可直接重新提交，无需弹窗
         *     3. 成功订单(status=success且有越南申请编号) - 需要弹窗提醒
         *     4. 提交中订单(status=pending_*) - 需要弹窗提醒
         */
        get: operations["check_duplicate_submission_api_visa_check_duplicate__passport_number__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/admin/dashboard": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Admin Dashboard
         * @description 管理员仪表板
         *
         *     - **返回**: 统计信息和仪表板数据
         */
        get: operations["admin_dashboard_api_visa_admin_dashboard_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/admin/query": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Admin Query Applications
         * @description 管理员查询申请记录
         *
         *     - **page**: 页码 (默认: 1)
         *     - **limit**: 每页记录数 (默认: 20, 最大: 100)
         *     - **status**: 申请状态筛选 (SUCCESS, FAILED, PENDING)
         *     - **date_from**: 开始日期 (YYYY-MM-DD)
         *     - **date_to**: 结束日期 (YYYY-MM-DD)
         *     - **search**: 搜索关键词 (姓名、护照号码、申请编号)
         *     - **返回**: 分页的申请记录列表
         */
        get: operations["admin_query_applications_api_visa_admin_query_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/admin/statistics": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Admin Statistics
         * @description 管理员统计数据
         *
         *     - **period**: 统计周期 (1d, 7d, 30d, 90d, 1y)
         *     - **返回**: 详细统计数据
         */
        get: operations["admin_statistics_api_visa_admin_statistics_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/admin/application/{application_number}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Application Details
         * @description 获取申请详情
         *
         *     - **application_number**: 申请编号
         *     - **返回**: 申请详细信息
         */
        get: operations["get_application_details_api_visa_admin_application__application_number__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/admin/realtime-stats": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Admin Realtime Stats
         * @description 管理员实时统计数据 - 简化HTTP统计接口
         */
        get: operations["admin_realtime_stats_api_visa_admin_realtime_stats_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/admin/basic-stats": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Admin Basic Stats
         * @description 管理员基础统计数据
         *
         *     - **返回**: 基础统计数据
         */
        get: operations["admin_basic_stats_api_visa_admin_basic_stats_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/admin/export": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Admin Export Data
         * @description 导出申请数据
         *
         *     - **format**: 导出格式 (简单化，暂时只支持excel)
         *     - **date_from**: 开始日期 (YYYY-MM-DD)
         *     - **date_to**: 结束日期 (YYYY-MM-DD)
         *     - **status**: 状态筛选
         *     - **返回**: Excel格式导出文件
         */
        get: operations["admin_export_data_api_visa_admin_export_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/health": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Health Check
         * @description 健康检查接口
         *
         *     Returns:
         *         Dict: 健康状态信息
         */
        get: operations["health_check_api_visa_health_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/routes": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Routes
         * @description 列出所有可用路由
         *
         *     Returns:
         *         Dict: 路由列表
         */
        get: operations["list_routes_api_visa_routes_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };

    "/api/visa/orders/create": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create Order
         * @description 创建新订单
         *
         *     实现L Notepad核心要求：
         *     - 原子性：编号生成和数据库写入在同一事务
         *     - 幂等性：同一用户+姓名+护照号+出生日期只能创建一个订单
         *     - 安全性：编号不可推算，只能由后端生成
         */
        post: operations["create_order_api_visa_orders_create_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/orders/query": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Query Orders
         * @description 查询订单列表
         *
         *     安全性：只能查询当前用户的订单
         *     支持多条件筛选和分页
         */
        get: operations["query_orders_api_visa_orders_query_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/orders/{order_no}/detail": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Order Detail
         * @description 获取订单详情
         *
         *     安全性：用户ID + 订单编号双重校验
         *     包含完整订单信息和状态变更历史
         */
        get: operations["get_order_detail_api_visa_orders__order_no__detail_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/orders/{order_no}/update": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * Update Order
         * @description 更新订单信息
         *
         *     主要用于更新越南官方编号、状态等
         *     安全性：用户ID + 订单编号双重校验
         */
        put: operations["update_order_api_visa_orders__order_no__update_put"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/orders/{order_no}/retry": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Retry Order
         * @description 重试订单处理
         *
         *     用于失败订单的重新处理
         *     只有failed状态的订单可以重试
         */
        post: operations["retry_order_api_visa_orders__order_no__retry_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/orders/by-application/{application_number}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Order By Application Number
         * @description 根据越南官方编号查询订单
         *
         *     用于兼容性查询和状态同步
         *     安全性：只能查询当前用户的订单
         */
        get: operations["get_order_by_application_number_api_visa_orders_by_application__application_number__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/orders/{order_no}/history": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Order Status History
         * @description 获取订单状态历史
         *
         *     返回订单的完整状态变更记录
         */
        get: operations["get_order_status_history_api_visa_orders__order_no__history_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/orders/{order_no}/cancel": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Cancel Order
         * @description 取消订单
         *
         *     仅限created状态的订单可以取消
         */
        post: operations["cancel_order_api_visa_orders__order_no__cancel_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/visa/orders/stats/summary": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取订单统计信息
         * @description 获取用户订单统计信息，包括各状态订单数量
         */
        get: operations["get_order_stats_api_visa_orders_stats_summary_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Api Info
         * @description API服务信息 - 根路径
         */
        get: operations["api_info__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/ocr-passport/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Ocr Passport Legacy Endpoint
         * @description 护照OCR识别 (兼容旧API)
         *
         *     这个端点与旧API保持完全一致，直接返回OCR字段而不包装在response对象中
         */
        post: operations["ocr_passport_legacy_endpoint_ocr_passport__post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        /** Body_apply_visa_api_visa_apply_post */
        Body_apply_visa_api_visa_apply_post: {
            /**
             * Portrait Photo
             * Format: binary
             * @description Portrait photo (JPG/PNG)
             */
            portrait_photo: string;
            /**
             * Passport Scan
             * Format: binary
             * @description Passport scan (JPG/PNG)
             */
            passport_scan: string;
            /** Surname */
            surname?: string | null;
            /** Given Name */
            given_name?: string | null;
            /** Chinese Name */
            chinese_name?: string | null;
            /** Sex */
            sex: string;
            /** Dob */
            dob?: string | null;
            /** Place Of Birth */
            place_of_birth?: string | null;
            /**
             * Nationality
             * @default CHINA
             */
            nationality: string | null;
            /**
             * Religion
             * @default NO
             */
            religion: string | null;
            /** Passport Number */
            passport_number?: string | null;
            /**
             * Passport Type
             * @default Ordinary passport
             */
            passport_type: string | null;
            /** Place Of Issue */
            place_of_issue?: string | null;
            /** Date Of Issue */
            date_of_issue?: string | null;
            /** Passport Expiry */
            passport_expiry?: string | null;
            /** Email */
            email?: string | null;
            /** Telephone Number */
            telephone_number?: string | null;
            /** Permanent Address */
            permanent_address?: string | null;
            /** Contact Address */
            contact_address?: string | null;
            /** Emergency Contact Name */
            emergency_contact_name?: string | null;
            /** Emergency Address */
            emergency_address?: string | null;
            /** Emergency Contact Phone */
            emergency_contact_phone?: string | null;
            /** Visa Entry Type */
            visa_entry_type: string;
            /** Visa Validity Duration */
            visa_validity_duration: string;
            /** Visa Start Date */
            visa_start_date: string;
            /** Intended Entry Gate */
            intended_entry_gate: string;
            /**
             * Purpose Of Entry
             * @default Tourist
             */
            purpose_of_entry: string;
            /** Expedited Type */
            expedited_type?: string | null;
            /**
             * Visited Vietnam Last Year
             * @default false
             */
            visited_vietnam_last_year: boolean;
            /** Previous Entry Date */
            previous_entry_date?: string | null;
            /** Previous Exit Date */
            previous_exit_date?: string | null;
            /** Previous Purpose */
            previous_purpose?: string | null;
            /**
             * Has Vietnam Contact
             * @default false
             */
            has_vietnam_contact: boolean;
            /** Vietnam Contact Organization */
            vietnam_contact_organization?: string | null;
            /** Vietnam Contact Phone */
            vietnam_contact_phone?: string | null;
            /** Vietnam Contact Address */
            vietnam_contact_address?: string | null;
            /** Vietnam Contact Purpose */
            vietnam_contact_purpose?: string | null;
            /** Force Resubmit */
            force_resubmit?: string | null;
        };
        /** Body_compatible_login_api_login_post */
        Body_compatible_login_api_login_post: {
            /** Username */
            username: string;
            /** Password */
            password: string;
        };
        /** Body_ocr_passport_legacy_endpoint_ocr_passport__post */
        Body_ocr_passport_legacy_endpoint_ocr_passport__post: {
            /**
             * Passport Scan
             * Format: binary
             */
            passport_scan: string;
        };
        /**
         * CreateOrderRequest
         * @description 创建订单请求
         */
        CreateOrderRequest: {
            /**
             * Applicant Name
             * @description 申请人姓名
             */
            applicant_name: string;
            /**
             * Passport Number
             * @description 护照号码
             */
            passport_number: string;
            /**
             * Date Of Birth
             * Format: date
             * @description 出生日期
             */
            date_of_birth: string;
            /**
             * Application Data
             * @description 完整的申请表单数据
             */
            application_data: {
                [key: string]: unknown;
            };
        };
        /**
         * CreateOrderResponse
         * @description 创建订单响应
         */
        CreateOrderResponse: {
            /** Success */
            success: boolean;
            /** Data */
            data?: {
                [key: string]: unknown;
            } | null;
            /** Message */
            message?: string | null;
        };
        /** HTTPValidationError */
        HTTPValidationError: {
            /** Detail */
            detail?: components["schemas"]["ValidationError"][];
        };
        /**
         * HealthResponse
         * @description 健康检查响应
         * @example {
         *       "dependencies": {
         *         "database": "connected",
         *         "email_service": "running",
         *         "ocr_service": "available"
         *       },
         *       "message": "服务运行正常",
         *       "status": "healthy",
         *       "success": true,
         *       "timestamp": "2025-01-01T12:00:00",
         *       "uptime": "2 days, 3 hours, 45 minutes",
         *       "version": "1.0.0"
         *     }
         */
        HealthResponse: {
            /**
             * Success
             * @description 操作是否成功
             */
            success: boolean;
            /**
             * Message
             * @description 响应消息
             */
            message: string;
            /**
             * Timestamp
             * Format: date-time
             * @description 响应时间
             */
            timestamp?: string;
            /**
             * Status
             * @description 服务状态
             */
            status: string;
            /**
             * Version
             * @description 版本号
             */
            version: string;
            /**
             * Uptime
             * @description 运行时间
             */
            uptime?: string | null;
            /**
             * Dependencies
             * @description 依赖服务状态
             */
            dependencies?: {
                [key: string]: string;
            } | null;
        };
        /**
         * OrderStatus
         * @description 订单状态枚举 - 根据修正后的业务流程
         * @enum {string}
         */
        OrderStatus: "created" | "processing" | "success" | "pending_approve" | "approved" | "pending_download" | "downloaded" | "paid" | "failed" | "unknown" | "reserved1" | "reserved2" | "reserved3";
        /**
         * TrackingInfo
         * @description 跟踪信息模型
         */
        TrackingInfo: {
            /**
             * Submission Time
             * @description 提交时间
             */
            submission_time: string;
            /**
             * Expected Processing Days
             * @description 预期处理天数
             * @default 3-5
             */
            expected_processing_days: string;
            /**
             * Email
             * @description 联系邮箱
             */
            email?: string | null;
            /**
             * Order No
             * @description 订单编号
             */
            order_no?: string | null;
            /**
             * Vietnam Application Number
             * @description 越南官方申请编号
             */
            vietnam_application_number?: string | null;
            /**
             * Is Duplicate
             * @description 是否重复提交
             * @default false
             */
            is_duplicate: boolean | null;
            /**
             * Failure Reason
             * @description 失败原因
             */
            failure_reason?: string | null;
        };
        /**
         * UpdateOrderRequest
         * @description 订单更新请求
         */
        UpdateOrderRequest: {
            /** Application Number */
            application_number?: string | null;
            status?: components["schemas"]["OrderStatus"] | null;
            /** Error Message */
            error_message?: string | null;
        };
        /** ValidationError */
        ValidationError: {
            /** Location */
            loc: (string | number)[];
            /** Message */
            msg: string;
            /** Error Type */
            type: string;
        };
        /**
         * VisaApplicationResponse
         * @description 签证申请响应模型
         */
        VisaApplicationResponse: {
            /**
             * Success
             * @description 是否成功
             */
            success: boolean;
            /**
             * Message
             * @description 响应消息
             */
            message: string;
            /**
             * Application Id
             * @description 申请ID
             */
            application_id?: string | null;
            /**
             * Status
             * @description 申请状态
             */
            status: string;
            /** @description 跟踪信息 */
            tracking_info?: components["schemas"]["TrackingInfo"] | null;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    compatible_login_api_login_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/x-www-form-urlencoded": components["schemas"]["Body_compatible_login_api_login_post"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    compatible_logout_api_logout_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    compatible_session_status_api_session_status_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    compatible_user_profile_api_user_profile_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    compatible_refresh_token_api_refresh_token_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    apply_visa_api_visa_apply_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "multipart/form-data": components["schemas"]["Body_apply_visa_api_visa_apply_post"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["VisaApplicationResponse"];
                };
            };
            /** @description 表单验证错误 */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    cancel_application_api_visa_cancel_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": {
                    [key: string]: unknown;
                };
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description 表单验证错误 */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_visa_status_api_visa_status__application_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                application_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description 申请记录未找到 */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_user_history_api_visa_user_history__passport_number__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                passport_number: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description 申请记录未找到 */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    list_applications_api_visa_applications_get: {
        parameters: {
            query?: {
                page?: number;
                page_size?: number;
                status?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description 申请记录未找到 */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    cancel_application_api_visa_applications__application_id__delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                application_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description 申请记录未找到 */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_expedited_date_api_visa_calculate_expedited_date_get: {
        parameters: {
            query?: {
                days?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description 参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    check_duplicate_submission_api_visa_check_duplicate__passport_number__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                passport_number: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description 参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    admin_dashboard_api_visa_admin_dashboard_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description 权限不足 */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    admin_query_applications_api_visa_admin_query_get: {
        parameters: {
            query?: {
                /** @description 页码 */
                page?: number;
                /** @description 每页记录数 */
                limit?: number;
                /** @description 状态筛选 */
                status?: string | null;
                /** @description 开始日期 */
                date_from?: string | null;
                /** @description 结束日期 */
                date_to?: string | null;
                /** @description 搜索关键词 */
                search?: string | null;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description 权限不足 */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    admin_statistics_api_visa_admin_statistics_get: {
        parameters: {
            query?: {
                /** @description 统计周期 */
                period?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description 权限不足 */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get_application_details_api_visa_admin_application__application_number__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                application_number: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description 权限不足 */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    admin_realtime_stats_api_visa_admin_realtime_stats_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description 权限不足 */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    admin_basic_stats_api_visa_admin_basic_stats_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description 权限不足 */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    admin_export_data_api_visa_admin_export_get: {
        parameters: {
            query?: {
                /** @description 导出格式 */
                format?: string;
                /** @description 开始日期 */
                date_from?: string | null;
                /** @description 结束日期 */
                date_to?: string | null;
                /** @description 状态筛选 */
                status?: string | null;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description 导出参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
            /** @description 导出失败 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    health_check_api_visa_health_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    list_routes_api_visa_routes_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };

    create_order_api_visa_orders_create_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CreateOrderRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CreateOrderResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    query_orders_api_visa_orders_query_get: {
        parameters: {
            query?: {
                /** @description 页码 */
                page?: number;
                /** @description 每页数量 */
                limit?: number;
                /** @description 订单状态 */
                status?: string | null;
                /** @description 订单编号（支持模糊查询） */
                order_no?: string | null;
                /** @description 申请编号（支持模糊查询） */
                application_number?: string | null;
                /** @description 开始日期 */
                date_from?: string | null;
                /** @description 结束日期 */
                date_to?: string | null;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_order_detail_api_visa_orders__order_no__detail_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                order_no: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    update_order_api_visa_orders__order_no__update_put: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                order_no: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdateOrderRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    retry_order_api_visa_orders__order_no__retry_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                order_no: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_order_by_application_number_api_visa_orders_by_application__application_number__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                application_number: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_order_status_history_api_visa_orders__order_no__history_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                order_no: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    cancel_order_api_visa_orders__order_no__cancel_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                order_no: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": {
                    [key: string]: unknown;
                };
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_order_stats_api_visa_orders_stats_summary_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    api_info__get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    ocr_passport_legacy_endpoint_ocr_passport__post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "multipart/form-data": components["schemas"]["Body_ocr_passport_legacy_endpoint_ocr_passport__post"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
}
