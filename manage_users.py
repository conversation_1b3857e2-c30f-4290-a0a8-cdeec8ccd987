#!/usr/bin/env python3
"""
用户管理脚本 - 管理越南签证系统的用户账号
使用方法:
    python manage_users.py list                    # 列出所有用户
    python manage_users.py create                  # 创建新用户
    python manage_users.py change-password         # 修改用户密码
    python manage_users.py delete                  # 删除用户
    python manage_users.py make-admin              # 设置管理员
"""

import asyncio
import getpass
import os
import sys

from dotenv import load_dotenv

from backend.auth_fastapi_users.user_management import (
    change_user_password,
    create_user_interactive,
    delete_user_by_email,
    get_user_by_email,
    list_users,
    toggle_user_admin,
)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


# 检测运行环境并加载相应的环境变量
def load_environment():
    """根据运行环境加载合适的环境变量"""
    # 检查是否在Docker容器内运行
    if os.path.exists("/.dockerenv"):
        print("🐳 检测到Docker容器环境")
        load_dotenv()  # 使用容器内的环境变量
    else:
        print("🖥️ 检测到宿主机环境")
        # 尝试加载本地环境变量
        if os.path.exists(".env.local"):
            load_dotenv(".env.local")
            print("✅ 已加载本地环境变量配置 (.env.local)")
        else:
            print("⚠️ 未找到 .env.local 文件")

        # 强制设置宿主机连接Docker数据库的配置
        # 覆盖任何可能导致连接失败的配置
        os.environ["POSTGRES_HOST"] = "localhost"
        os.environ["POSTGRES_PORT"] = "5432"
        os.environ["POSTGRES_DB"] = "visa_automator"
        os.environ["POSTGRES_USER"] = "visa_user"
        os.environ["POSTGRES_PASSWORD"] = "visa_password_2024"
        os.environ["DEBUG"] = "true"
        os.environ["SECRET_KEY"] = "your-very-secret-key"
        print("🔧 已强制设置宿主机连接Docker数据库的配置")
        print(f"   数据库主机: {os.environ['POSTGRES_HOST']}")
        print(f"   数据库端口: {os.environ['POSTGRES_PORT']}")
        print(f"   数据库名称: {os.environ['POSTGRES_DB']}")
        print(f"   数据库用户: {os.environ['POSTGRES_USER']}")


# 加载环境变量
load_environment()


def print_banner():
    """打印程序横幅"""
    print("=" * 60)
    print("🔐 越南签证系统 - 用户管理工具")
    print("=" * 60)


def print_help():
    """打印帮助信息"""
    print("\n📚 使用方法:")
    print("  python manage_users.py list                    # 列出所有用户")
    print("  python manage_users.py create                  # 创建新用户")
    print("  python manage_users.py change-password         # 修改用户密码")
    print("  python manage_users.py delete                  # 删除用户")
    print("  python manage_users.py make-admin              # 设置/取消管理员")
    print("  python manage_users.py help                    # 显示帮助")
    print()


async def interactive_create_user():
    """交互式创建用户"""
    print("\n🆕 创建新用户")
    print("-" * 30)

    # 获取用户输入
    username = input("请输入用户名: ").strip()
    if not username:
        print("❌ 用户名不能为空")
        return

    email = input("请输入邮箱地址: ").strip()
    if not email:
        print("❌ 邮箱地址不能为空")
        return

    # 安全地获取密码
    while True:
        password = getpass.getpass("请输入密码: ")
        if len(password) < 6:
            print("❌ 密码长度不能少于6位")
            continue

        confirm_password = getpass.getpass("请确认密码: ")
        if password != confirm_password:
            print("❌ 两次输入的密码不一致")
            continue
        break

    # 询问是否设为管理员
    is_admin_input = input("是否设为管理员? (y/N): ").strip().lower()
    is_admin = is_admin_input in ["y", "yes", "是"]

    try:
        user = await create_user_interactive(
            username=username, email=email, password=password, is_superuser=is_admin
        )

        if user:
            print("✅ 用户创建成功!")
            print(f"   用户名: {username}")
            print(f"   邮箱: {email}")
            print(f"   管理员: {'是' if is_admin else '否'}")
        else:
            print("❌ 用户创建失败")

    except Exception as e:
        print(f"❌ 创建用户时发生错误: {e}")


async def interactive_change_password():
    """交互式修改密码"""
    print("\n🔑 修改用户密码")
    print("-" * 30)

    email = input("请输入要修改密码的用户邮箱: ").strip()
    if not email:
        print("❌ 邮箱地址不能为空")
        return

    # 检查用户是否存在
    user = await get_user_by_email(email)
    if not user:
        print(f"❌ 用户 {email} 不存在")
        return

    print(f"找到用户: {user.username} ({user.email})")

    # 安全地获取新密码
    while True:
        new_password = getpass.getpass("请输入新密码: ")
        if len(new_password) < 6:
            print("❌ 密码长度不能少于6位")
            continue

        confirm_password = getpass.getpass("请确认新密码: ")
        if new_password != confirm_password:
            print("❌ 两次输入的密码不一致")
            continue
        break

    try:
        success = await change_user_password(email, new_password)
        if success:
            print("✅ 密码修改成功!")
        else:
            print("❌ 密码修改失败")
    except Exception as e:
        print(f"❌ 修改密码时发生错误: {e}")


async def interactive_delete_user():
    """交互式删除用户"""
    print("\n🗑️ 删除用户")
    print("-" * 30)

    email = input("请输入要删除的用户邮箱: ").strip()
    if not email:
        print("❌ 邮箱地址不能为空")
        return

    # 检查用户是否存在
    user = await get_user_by_email(email)
    if not user:
        print(f"❌ 用户 {email} 不存在")
        return

    print(f"找到用户: {user.username} ({user.email})")
    print(f"管理员: {'是' if user.is_superuser else '否'}")

    # 确认删除
    confirm = input("⚠️ 确定要删除此用户吗? 此操作不可恢复! (yes/NO): ").strip()
    if confirm.lower() != "yes":
        print("操作已取消")
        return

    try:
        success = await delete_user_by_email(email)
        if success:
            print("✅ 用户删除成功!")
        else:
            print("❌ 用户删除失败")
    except Exception as e:
        print(f"❌ 删除用户时发生错误: {e}")


async def interactive_toggle_admin():
    """交互式设置/取消管理员"""
    print("\n👑 管理员权限设置")
    print("-" * 30)

    email = input("请输入用户邮箱: ").strip()
    if not email:
        print("❌ 邮箱地址不能为空")
        return

    # 检查用户是否存在
    user = await get_user_by_email(email)
    if not user:
        print(f"❌ 用户 {email} 不存在")
        return

    current_status = "管理员" if user.is_superuser else "普通用户"
    print(f"找到用户: {user.username} ({user.email})")
    print(f"当前状态: {current_status}")

    # 询问操作
    if user.is_superuser:
        action = input("是否要取消管理员权限? (y/N): ").strip().lower()
        make_admin = action not in ["y", "yes", "是"]
    else:
        action = input("是否要设为管理员? (y/N): ").strip().lower()
        make_admin = action in ["y", "yes", "是"]

    try:
        success = await toggle_user_admin(email, make_admin)
        if success:
            new_status = "管理员" if make_admin else "普通用户"
            print(f"✅ 权限修改成功! 现在是: {new_status}")
        else:
            print("❌ 权限修改失败")
    except Exception as e:
        print(f"❌ 修改权限时发生错误: {e}")


async def test_database_connection():
    """测试数据库连接"""
    try:
        from backend.db_config.unified_connection import get_unified_db

        print("🔗 正在测试数据库连接...")

        db = await get_unified_db()
        health_ok = await db.health_check()

        if health_ok:
            print("✅ 数据库连接测试成功")
            return True
        else:
            print("❌ 数据库连接测试失败")
            return False
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        if "[Errno 11001] getaddrinfo failed" in str(e):
            print("\n💡 提示：")
            print("   - 确保 Docker 容器正在运行: docker-compose up -d")
            print(
                "   - 或者在容器内运行此脚本: docker exec -it visa_automator-visa-automator-1 python manage_users.py"
            )
        return False


async def main():
    """主函数"""
    print_banner()

    if len(sys.argv) < 2:
        print_help()
        return

    command = sys.argv[1].lower()

    # 测试数据库连接
    if not await test_database_connection():
        print("\n❌ 无法连接到数据库，请检查配置后重试")
        return

    try:
        if command == "list":
            await list_users()
        elif command == "create":
            await interactive_create_user()
        elif command == "change-password":
            await interactive_change_password()
        elif command == "delete":
            await interactive_delete_user()
        elif command == "make-admin":
            await interactive_toggle_admin()
        elif command == "help":
            print_help()
        else:
            print(f"❌ 未知命令: {command}")
            print_help()
    except KeyboardInterrupt:
        print("\n\n👋 操作已取消")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        if "[Errno 11001] getaddrinfo failed" in str(e):
            print("\n💡 提示：")
            print("   - 确保 Docker 容器正在运行: docker-compose up -d")
            print(
                "   - 或者在容器内运行此脚本: docker exec -it visa_automator-visa-automator-1 python manage_users.py"
            )


if __name__ == "__main__":
    asyncio.run(main())
