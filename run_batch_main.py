from dotenv import load_dotenv

from app.batch_processing.batch_applicant_importer import import_applicants_from_folder
from app.batch_processing.batch_runner import run_batch
from app.utils.logger_config import get_logger, setup_logger

# 加载环境变量
load_dotenv()

setup_logger(console_level="DEBUG", file_path="logs.batch_processing.log")

logger = get_logger()

# 批处理主程序
# 该模块负责从指定文件夹导入申请人信息，并批量执行任务。带有文件名解析功能以及护照和照片的自动匹配。匹配方式：照片命名 1.jgpg，护照命名 11-30天-单次-岘港-出签生效.jpg
# 手机号码支持文本解析和护照文件名解析，例如33-90天-单次-胡志明<EMAIL>。默认使用文件内容解析手机号码和邮箱，如果文件名中包含手机号码和邮箱，则优先使用文件名中的信息。


def main():
    folder = r"C:\Users\<USER>\Desktop\visa_data"  # 替换为实际路径
    logger.info(f"开始从文件夹导入申请人: {folder}")
    applicants = import_applicants_from_folder(folder)
    logger.info(f"✅ 共导入 {len(applicants)} 位申请人")
    run_batch(applicants, max_concurrent=4, launch_window=30)
    logger.info("✅批处理任务全部完成,处理结果请查看日志文件")


# ✅ Windows 多进程保护入口
if __name__ == "__main__":
    logger.info("批处理程序启动...")
    main()
