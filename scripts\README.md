# Scripts - 运维和分析工具

本目录包含用于数据库分析、性能监控和运维的独立脚本工具。

## 📁 脚本列表

### 🔍 `db_performance_comparison.py`
**数据库性能需求分析工具**

- **用途**: 分析业务需求，量化对比PostgreSQL vs MySQL性能过剩程度
- **运行**: `python scripts/db_performance_comparison.py`
- **输出**: 详细的数据库选型建议报告
- **适用场景**: 
  - 数据库选型决策
  - 性能规划评估
  - 成本效益分析

### 📊 `db_performance_monitor.py`
**PostgreSQL性能监控工具（原生版本）**

- **用途**: 使用原生asyncpg连接监控PostgreSQL数据库性能指标
- **运行**: `python scripts/db_performance_monitor.py`
- **输出**: 
  - 控制台性能摘要
  - JSON格式详细报告 (`reports/postgresql_performance_*.json`)
- **监控指标**:
  - 连接数统计
  - 表和索引使用情况
  - 慢查询分析
  - 缓存命中率
  - 数据库大小

### 🔗 `unified_db_performance_monitor.py`
**统一连接池性能监控工具（推荐）**

- **用途**: 集成统一连接池的增强版监控工具
- **运行**: `python scripts/unified_db_performance_monitor.py`
- **输出**: 
  - 控制台性能摘要（兼容原版格式）
  - JSON格式详细报告 (`reports/unified_performance_*.json`)
- **新增功能**:
  - **统一连接池状态监控**: 连接池大小、健康状态、资源使用
  - **业务数据统计**: Orders表 vs VisaTask表数据对比
  - **数据一致性检查**: 自动检测数据源统一迁移进度
  - **智能回退**: 统一连接池不可用时自动回退到原生模式
- **命令行参数**:
  ```bash
  # 基础监控
  python scripts/unified_db_performance_monitor.py
  
  # 与传统监控对比
  python scripts/unified_db_performance_monitor.py --compare reports/postgresql_performance_*.json
  
  # 指定输出文件
  python scripts/unified_db_performance_monitor.py --output custom_report.json
  
  # 仅输出JSON格式
  python scripts/unified_db_performance_monitor.py --format json
  ```

### ✅ `monitor_validation.py`
**监控数据验证工具**

- **用途**: 验证统一监控与原生监控的数据一致性
- **运行**: `python scripts/monitor_validation.py`
- **功能**:
  - 自动运行新旧监控脚本
  - 对比关键指标数据差异
  - 生成详细验证报告
  - 设置合理的差异阈值
- **验证项目**:
  - 连接统计数据一致性
  - 数据库大小一致性
  - 表统计数据一致性
  - 缓存命中率一致性
- **输出**: 验证报告 (`reports/monitor_validation_*.json`)

## 🚀 使用方法

### 环境准备
```bash
# 确保PostgreSQL连接信息正确配置
export POSTGRES_HOST=localhost
export POSTGRES_PORT=5432
export POSTGRES_DB=visa_automator
export POSTGRES_USER=visa_user
export POSTGRES_PASSWORD=visa_password_2024
```

### 运行脚本
```bash
# 数据库性能分析
python scripts/db_performance_comparison.py

# 原生数据库性能监控
python scripts/db_performance_monitor.py

# 统一连接池监控（推荐）
python scripts/unified_db_performance_monitor.py

# 监控数据验证
python scripts/monitor_validation.py
```

## 📋 依赖要求

- Python 3.8+
- asyncpg (用于PostgreSQL连接)
- SQLAlchemy + asyncpg (用于统一连接池)
- 环境变量或直接数据库访问权限

## 📄 输出文件

监控脚本会在项目根目录创建 `reports/` 文件夹，保存详细的JSON监控报告：

- `postgresql_performance_*.json`: 原生监控报告
- `unified_performance_*.json`: 统一连接池监控报告
- `monitor_validation_*.json`: 监控数据验证报告
- `comparison_*.json`: 新旧监控对比报告

## 🔧 实施建议

### 📈 监控脚本优化完成
✅ **已实施**: 统一连接池监控脚本
- 新增连接池状态监控
- 业务数据一致性检查  
- 完全兼容原版输出格式
- 智能回退机制

### 🎯 使用建议
1. **日常监控**: 使用 `unified_db_performance_monitor.py`
2. **数据验证**: 定期运行 `monitor_validation.py` 确保一致性
3. **问题排查**: 对比新旧监控报告定位问题
4. **架构验证**: 观察统一连接池运行状况

### 🔧 扩展计划

未来可能添加的脚本：
- 数据库备份自动化脚本
- 签证数据统计分析脚本
- 系统健康检查脚本
- 日志分析工具 
