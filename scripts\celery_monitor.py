#!/usr/bin/env python3
"""
Celery 集群监控脚本
✅ 生产级监控：心跳检测、连接池状态、任务队列长度
✅ 支持大规模部署的健康检查
"""

from datetime import datetime
import json
import os
import sys
import time
from typing import Any

import redis

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class CeleryMonitor:
    """Celery 集群监控器"""

    def __init__(self):
        self.redis_host = os.environ.get("REDIS_HOST", "localhost")
        self.redis_client = redis.Redis(
            host=self.redis_host, port=6379, db=0, decode_responses=True
        )

        # 初始化 Celery app
        from celery_worker.celery_app import app

        self.app = app
        self.inspect = self.app.control.inspect()

    def check_redis_health(self) -> dict[str, Any]:
        """检查 Redis 健康状态"""
        try:
            # 基础连接测试
            ping_result = self.redis_client.ping()

            # 获取连接信息
            info = self.redis_client.info()
            clients_info = {
                "connected_clients": info.get("connected_clients", 0),
                "blocked_clients": info.get("blocked_clients", 0),
                "max_clients": info.get("maxclients", 0),
                "used_memory_human": info.get("used_memory_human", "unknown"),
                "redis_version": info.get("redis_version", "unknown"),
            }

            return {
                "status": "healthy" if ping_result else "error",
                "ping": ping_result,
                "clients": clients_info,
                "uptime_seconds": info.get("uptime_in_seconds", 0),
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def check_workers_status(self) -> dict[str, Any]:
        """检查 Worker 状态"""
        try:
            # 获取活跃 workers
            active = self.inspect.active()
            stats = self.inspect.stats()
            ping = self.inspect.ping()

            workers_info = []

            if active:
                for worker_name, tasks in active.items():
                    worker_stats = stats.get(worker_name, {}) if stats else {}
                    worker_ping = ping.get(worker_name, {}) if ping else {}

                    workers_info.append(
                        {
                            "name": worker_name,
                            "active_tasks": len(tasks),
                            "total_tasks": worker_stats.get("total", {}),
                            "pool_info": worker_stats.get("pool", {}),
                            "ping_response": worker_ping,
                            "last_heartbeat": datetime.now().isoformat(),
                        }
                    )

            return {
                "status": "healthy" if workers_info else "no_workers",
                "worker_count": len(workers_info),
                "workers": workers_info,
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def check_queue_status(self) -> dict[str, Any]:
        """检查队列状态"""
        try:
            # 检查默认队列长度
            queue_length = self.redis_client.llen("celery")

            # 检查结果后端
            result_keys = self.redis_client.keys("celery-task-meta-*")

            return {
                "status": "healthy",
                "pending_tasks": queue_length,
                "result_keys_count": len(result_keys),
                "queue_name": "celery",
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def run_health_check(self) -> dict[str, Any]:
        """执行完整的健康检查"""
        timestamp = datetime.now().isoformat()

        print(f"\n🔍 Celery 集群健康检查 - {timestamp}")
        print("=" * 60)

        # Redis 检查
        print("\n📊 Redis 状态:")
        redis_status = self.check_redis_health()
        print(json.dumps(redis_status, indent=2, ensure_ascii=False))

        # Workers 检查
        print("\n👷 Workers 状态:")
        workers_status = self.check_workers_status()
        print(json.dumps(workers_status, indent=2, ensure_ascii=False))

        # 队列检查
        print("\n📋 队列状态:")
        queue_status = self.check_queue_status()
        print(json.dumps(queue_status, indent=2, ensure_ascii=False))

        # 整体状态评估
        overall_status = self._evaluate_overall_health(
            redis_status, workers_status, queue_status
        )
        print(f"\n🏥 整体健康状态: {overall_status['status']}")

        if overall_status["status"] != "healthy":
            print(f"⚠️  问题: {overall_status.get('issues', [])}")

        return {
            "timestamp": timestamp,
            "redis": redis_status,
            "workers": workers_status,
            "queue": queue_status,
            "overall": overall_status,
        }

    def _evaluate_overall_health(
        self, redis_status, workers_status, queue_status
    ) -> dict[str, Any]:
        """评估整体健康状态"""
        issues = []

        if redis_status["status"] != "healthy":
            issues.append("Redis 连接异常")

        if workers_status["status"] == "no_workers":
            issues.append("没有活跃的 Workers")
        elif workers_status["status"] == "error":
            issues.append("Workers 状态检查失败")

        if queue_status["status"] != "healthy":
            issues.append("队列状态异常")

        # 检查连接数是否过多
        if redis_status.get("clients", {}).get("connected_clients", 0) > 800:
            issues.append("Redis 连接数过多")

        return {"status": "healthy" if not issues else "degraded", "issues": issues}

    def monitor_continuous(self, interval: int = 30):
        """持续监控模式"""
        print(f"🔄 启动持续监控模式 (间隔: {interval}秒)")

        try:
            while True:
                self.run_health_check()
                print(f"\n⏰ 等待 {interval} 秒...")
                time.sleep(interval)
        except KeyboardInterrupt:
            print("\n👋 监控已停止")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="Celery 集群监控工具")
    parser.add_argument("--continuous", "-c", action="store_true", help="持续监控模式")
    parser.add_argument("--interval", "-i", type=int, default=30, help="监控间隔（秒）")

    args = parser.parse_args()

    monitor = CeleryMonitor()

    if args.continuous:
        monitor.monitor_continuous(args.interval)
    else:
        monitor.run_health_check()


if __name__ == "__main__":
    main()
