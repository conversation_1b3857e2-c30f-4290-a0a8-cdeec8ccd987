#!/usr/bin/env python3
"""
PostgreSQL 性能监控脚本
用于监控数据库性能指标和查询统计
"""

import asyncio
from datetime import datetime
import json
import os
from pathlib import Path

import asyncpg


class PostgreSQLMonitor:
    """PostgreSQL 性能监控器"""

    def __init__(self):
        self.connection_string = self._build_connection_string()

    def _build_connection_string(self) -> str:
        """构建连接字符串"""
        host = os.getenv("POSTGRES_HOST", "localhost")
        port = os.getenv("POSTGRES_PORT", "5432")
        database = os.getenv("POSTGRES_DB", "visa_automator")
        user = os.getenv("POSTGRES_USER", "visa_user")
        password = os.getenv("POSTGRES_PASSWORD", "visa_password_2024")

        return f"postgresql://{user}:{password}@{host}:{port}/{database}"

    async def get_connection_stats(self) -> dict:
        """获取连接统计信息"""
        conn = await asyncpg.connect(self.connection_string)
        try:
            result = await conn.fetchrow("""
                SELECT
                    count(*) as total_connections,
                    count(*) FILTER (WHERE state = 'active') as active_connections,
                    count(*) FILTER (WHERE state = 'idle') as idle_connections,
                    count(*) FILTER (WHERE state = 'idle in transaction') as idle_in_transaction
                FROM pg_stat_activity
                WHERE datname = current_database()
            """)
            return dict(result)
        finally:
            await conn.close()

    async def get_table_stats(self) -> list[dict]:
        """获取表统计信息"""
        conn = await asyncpg.connect(self.connection_string)
        try:
            results = await conn.fetch("""
                SELECT
                    schemaname,
                    tablename,
                    n_tup_ins as inserts,
                    n_tup_upd as updates,
                    n_tup_del as deletes,
                    n_live_tup as live_tuples,
                    n_dead_tup as dead_tuples,
                    seq_scan as sequential_scans,
                    seq_tup_read as sequential_reads,
                    idx_scan as index_scans,
                    idx_tup_fetch as index_reads
                FROM pg_stat_user_tables
                ORDER BY n_live_tup DESC
            """)
            return [dict(row) for row in results]
        finally:
            await conn.close()

    async def get_index_stats(self) -> list[dict]:
        """获取索引使用统计"""
        conn = await asyncpg.connect(self.connection_string)
        try:
            results = await conn.fetch("""
                SELECT
                    schemaname,
                    tablename,
                    indexname,
                    idx_scan as scans,
                    idx_tup_read as tuples_read,
                    idx_tup_fetch as tuples_fetched
                FROM pg_stat_user_indexes
                ORDER BY idx_scan DESC
            """)
            return [dict(row) for row in results]
        finally:
            await conn.close()

    async def get_slow_queries(self, limit: int = 10) -> list[dict]:
        """获取慢查询统计（需要pg_stat_statements扩展）"""
        conn = await asyncpg.connect(self.connection_string)
        try:
            results = await conn.fetch(
                """
                SELECT
                    query,
                    calls,
                    total_time,
                    mean_time,
                    rows,
                    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
                FROM pg_stat_statements
                WHERE query NOT LIKE '%pg_stat_statements%'
                ORDER BY mean_time DESC
                LIMIT $1
            """,
                limit,
            )
            return [dict(row) for row in results]
        except Exception as e:
            print(f"获取慢查询失败（可能未启用pg_stat_statements）: {e}")
            return []
        finally:
            await conn.close()

    async def get_database_size(self) -> dict:
        """获取数据库大小信息"""
        conn = await asyncpg.connect(self.connection_string)
        try:
            result = await conn.fetchrow("""
                SELECT
                    pg_size_pretty(pg_database_size(current_database())) as database_size,
                    pg_database_size(current_database()) as database_size_bytes
            """)
            return dict(result)
        finally:
            await conn.close()

    async def get_cache_hit_ratio(self) -> dict:
        """获取缓存命中率"""
        conn = await asyncpg.connect(self.connection_string)
        try:
            result = await conn.fetchrow("""
                SELECT
                    sum(heap_blks_read) as heap_read,
                    sum(heap_blks_hit) as heap_hit,
                    sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read)) * 100 as cache_hit_ratio
                FROM pg_statio_user_tables
            """)
            return dict(result)
        finally:
            await conn.close()

    async def generate_performance_report(self) -> dict:
        """生成完整的性能报告"""
        print("🔍 正在收集PostgreSQL性能数据...")

        report = {
            "timestamp": datetime.now().isoformat(),
            "connection_stats": await self.get_connection_stats(),
            "table_stats": await self.get_table_stats(),
            "index_stats": await self.get_index_stats(),
            "slow_queries": await self.get_slow_queries(),
            "database_size": await self.get_database_size(),
            "cache_hit_ratio": await self.get_cache_hit_ratio(),
        }

        return report

    def print_performance_summary(self, report: dict):
        """打印性能摘要"""
        print("\n" + "=" * 60)
        print("📊 PostgreSQL 性能监控报告")
        print("=" * 60)

        # 连接统计
        conn_stats = report["connection_stats"]
        print("\n🔗 连接统计:")
        print(f"   总连接数: {conn_stats['total_connections']}")
        print(f"   活跃连接: {conn_stats['active_connections']}")
        print(f"   空闲连接: {conn_stats['idle_connections']}")

        # 数据库大小
        db_size = report["database_size"]
        print(f"\n💾 数据库大小: {db_size['database_size']}")

        # 缓存命中率
        cache_ratio = report["cache_hit_ratio"]
        if cache_ratio["cache_hit_ratio"]:
            print(f"📈 缓存命中率: {cache_ratio['cache_hit_ratio']:.2f}%")

        # 表统计（前5个最大的表）
        print("\n📋 表统计 (前5个最大表):")
        for i, table in enumerate(report["table_stats"][:5]):
            print(f"   {i + 1}. {table['tablename']}: {table['live_tuples']:,} 行")

        # 索引使用情况（前5个最常用的索引）
        print("\n🔍 索引使用统计 (前5个最常用):")
        for i, index in enumerate(report["index_stats"][:5]):
            if index["scans"] > 0:
                print(f"   {i + 1}. {index['indexname']}: {index['scans']:,} 次扫描")

        # 慢查询（如果有）
        slow_queries = report["slow_queries"]
        if slow_queries:
            print("\n⚠️  慢查询 (前3个):")
            for i, query in enumerate(slow_queries[:3]):
                print(
                    f"   {i + 1}. 平均时间: {query['mean_time']:.2f}ms, 调用次数: {query['calls']}"
                )
                print(f"      查询: {query['query'][:100]}...")

        print("\n" + "=" * 60)


async def main():
    """主函数"""
    monitor = PostgreSQLMonitor()

    try:
        # 生成性能报告
        report = await monitor.generate_performance_report()

        # 打印摘要
        monitor.print_performance_summary(report)

        # 保存详细报告到文件
        report_dir = Path("reports")
        report_dir.mkdir(exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = report_dir / f"postgresql_performance_{timestamp}.json"

        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)

        print(f"\n📄 详细报告已保存到: {report_file}")

    except Exception as e:
        print(f"❌ 监控失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
