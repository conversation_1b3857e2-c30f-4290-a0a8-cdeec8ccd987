"""
测试配置文件
============

提供测试所需的fixtures和配置
"""

import asyncio
from collections.abc import AsyncGenerator

import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.data.base import Base
from app.repositories.applicant_repository import ApplicantRepository
from app.repositories.application_repository import ApplicationRepository
from app.repositories.automation_logs_repository import AutomationLogsRepository
from app.repositories.order_repository import OrderRepository


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="session")
async def test_engine():
    """创建测试数据库引擎"""
    # 使用内存SQLite数据库进行测试
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        poolclass=StaticPool,
        connect_args={"check_same_thread": False},
        echo=False,
    )

    # 创建所有表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    yield engine

    # 清理
    await engine.dispose()


@pytest_asyncio.fixture
async def test_session(test_engine) -> AsyncGenerator[AsyncSession, None]:
    """创建测试数据库会话"""
    async_session = sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )

    async with async_session() as session:
        yield session
        await session.rollback()


@pytest_asyncio.fixture
async def order_repository(test_session: AsyncSession) -> OrderRepository:
    """创建OrderRepository实例"""
    return OrderRepository(test_session)


@pytest_asyncio.fixture
async def application_repository(test_session: AsyncSession) -> ApplicationRepository:
    """创建ApplicationRepository实例"""
    return ApplicationRepository(test_session)


@pytest_asyncio.fixture
async def applicant_repository(test_session: AsyncSession) -> ApplicantRepository:
    """创建ApplicantRepository实例"""
    return ApplicantRepository(test_session)


@pytest_asyncio.fixture
async def automation_logs_repository(
    test_session: AsyncSession,
) -> AutomationLogsRepository:
    """创建AutomationLogsRepository实例"""
    return AutomationLogsRepository(test_session)


@pytest.fixture
def sample_user_id():
    """示例用户ID"""
    from uuid import uuid4

    return uuid4()


@pytest.fixture
def sample_order_data():
    """示例订单数据"""
    return {
        "order_no": "VN20250621TEST001",
        "order_status": "created",
        "total_amount": 100.0,
    }


@pytest.fixture
def sample_applicant_data():
    """示例申请人数据"""
    from datetime import date

    return {
        "passport_number": "E12345678",
        "surname": "Zhang",
        "given_name": "Wei",
        "chinese_name": "张伟",
        "sex": "M",
        "nationality": "China",
        "date_of_birth": date(1990, 1, 1),
        "place_of_birth": "Beijing",
        "email": "<EMAIL>",
        "telephone_number": "13800138000",
    }


@pytest.fixture
def sample_application_data():
    """示例申请数据"""
    from datetime import date

    return {
        "status": "pending",
        "country": "VNM",
        "category": "tourist",
        "form_snapshot": {"test": "data"},
        "visa_entry_type": "Single-entry",
        "visa_validity_duration": "30天",
        "visa_start_date": date(2025, 7, 1),
        "intended_entry_gate": "Tan Son Nhat Int Airport (Ho Chi Minh City)",
        "purpose_of_entry": "Tourist",
    }
